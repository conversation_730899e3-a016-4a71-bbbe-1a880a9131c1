<resources>

    <style name="TextAppearance.StatusBar.EventContent" parent="@android:style/TextAppearance.StatusBar.EventContent">
    </style>
    <style name="TextAppearance.StatusBar.EventContent.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title">
    </style>
    <style name="TextAppearance.StatusBar.EventContent.Time">
        <item name="android:textSize">@dimen/notification_subtext_size</item>
        <item name="android:textColor">#999999</item>
    </style>
    <style name="TextAppearance_DataLimit">
        <item name="android:textColor">#0D84FF</item>
    </style>

    <style name="Theme.Light.NoTitile" parent="@style/Theme.DayNight.NoTitle">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowTranslucentStatus">true</item>
    </style>

    <style name="ThemeDayNightNoTitle" parent="@style/Theme.DayNight.NoTitle">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="ThemeDayNightNoTitleDialogActivity" parent="@style/ThemeDayNightNoTitle">
        <item name="android:windowAnimationStyle">@null</item>
        <item name="windowExtraPaddingHorizontalEnable">true</item>
        <item name="windowExtraPaddingApplyToContentEnable">false</item>
    </style>
    <style name="ThemeXDayNight" parent="@style/Theme.DayNight">
        <item name="windowSplitActionBar">true</item>
        <item name="recyclerViewCardStyle">@style/RecyclerViewCardStyle.DayNight</item>
        <item name="actionBarBackground">@color/window_bg</item>
        <item name="android:windowBackground">@color/window_bg</item>
        <item name="android:actionModeStyle">@style/actionModeStyle</item>
        <!-- miuix3.1统一边距 -->
        <item name="windowExtraPaddingHorizontalEnable">true</item>
        <item name="contentAutoFitSystemWindow">false</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="miuixAppcompatColorPrimary">@color/window_bg</item>
        <item name="bgBlurOptions">actionBar|splitActionBar</item>
        <item name="bottomMenuMode">bottom</item>
    </style>
    <style name="ThemeXDayNight_Pad" parent="@style/Theme.DayNight">
        <item name="windowSplitActionBar">true</item>
        <item name="recyclerViewCardStyle">@style/RecyclerViewCardStyle.DayNight</item>
        <item name="actionBarBackground">@color/window_bg</item>
        <item name="android:windowBackground">@color/window_bg</item>
        <item name="android:actionModeStyle">@style/actionModeStyle</item>
        <!-- miuix3.1统一边距 -->
        <item name="windowExtraPaddingHorizontalEnable">true</item>
        <item name="contentAutoFitSystemWindow">false</item>
        <item name="windowActionBarOverlay">true</item>
        <item name="miuixAppcompatColorPrimary">@color/window_bg</item>
        <item name="bgBlurOptions">actionBar|splitActionBar</item>
        <item name="bottomMenuMode">suspension</item>
    </style>
    <style name="actionModeStyle" parent="@style/Widget.ActionMode">
        <item name="android:background">@color/window_bg</item>
    </style>
    <style name="WidgetButtonDialog" parent="@style/Widget.Button.Dialog"/>

    <style name="Widget.Button.Dialog.Cancel" >
        <item name="android:textColor">@color/dialog_button_cancel_light</item>
    </style>

    <style name="Widget.Button.Dialog.Ok" parent="@style/Widget.Button.Dialog.Default">
    </style>

    <style name="DialogContent">
        <item name="android:textColor">@color/dialog_content_msg1_light</item>
        <item name="android:textSize">18sp</item>
    </style>

    <style name="DialogContent.Message2">
        <item name="android:textColor">@color/dialog_content_msg2_light</item>
    </style>

    <style name="text_headline">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/text_headline_size</item>
        <item name="android:textColor">@color/text_headline_color</item>
        <item name="android:layout_centerVertical">true</item>
    </style>
    <style name="Widget.CheckBox" parent="@style/Widget.CompoundButton.CheckBox">
    </style>
    <style name="Widget.CheckBox.Dialog"  parent="@style/Widget.CompoundButton.CheckBox">
        <item name="android:textSize">@dimen/dialog_secondary_text_size</item>
    </style>
</resources>
