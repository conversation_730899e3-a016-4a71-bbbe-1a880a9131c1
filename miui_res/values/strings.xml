<?xml version="1.0" encoding="utf-8"?><!-- Copyright (C) 2007 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Text for dialog when user clicks on a download that failed due to insufficient space on
         external storage [CHAR LIMIT=200] -->
    <!-- Text for dialog when user clicks on a download that failed due to insufficient space on
         the internal download cache [CHAR LIMIT=200] -->

    <!-- When create a download task, and create a another download task
         that is the same as the former, then the second should be deleted
         and a toast should thow the text -->
    <string name="task_already_exist">Task already exists</string>

    <string name="privacy_notif_title"> Xunlei Download Accelerator is on</string>
    <string name="privacy_notif_content">Tap to view details.</string>

    <string name="notif_text_engine_on">Accelerating\u2026</string>
    <string name="notif_text_engine_off">Xunlei accelerator is off.</string>
    <string name="notif_title_single_downloading">Downloading %1$s</string>
    <string name="notif_title_multiple_downloading">Downloading %1$d files</string>
    <string name="notif_title_file_size">%1$d total</string>

    <string name="paused_waiting_for_network">Waiting for network</string>

    <string name="warning_tip_title">Data usage warning</string>
    <string name="warning_tip_content">You\'re no longer connected to WLAN. Using your mobile data connection to complete this download will increase your data usage. If you go over your plan\'s data limit, your carrier could charge you additional fees.</string>
    <string name="warning_tip_cancel">Download</string>
    <string name="warning_tip_ok">Pause</string>
    <string name="dialog_not_show_again">"Don't show again"</string>

    <string name="notification_download_success_more">"%1$d downloaded"</string>
    <plurals name="notification_download_failed_more">
        <item quantity="one">Couldn\'t download %1$d item</item>
        <item quantity="other">Couldn\'t download %1$d items</item>
    </plurals>
    <string name="mobile_recommended_title">Mobile data usage</string>
    <string name="recommended_file_size_download_in_mobile">"Use <xliff:g id="SIZE">%s</xliff:g> of mobile data to download this file?"</string>
    <string name="recommended_no_file_size_download_in_mobile">"Use your mobile data connection to download this file?"</string>
    <string name="button_wait_for_wifi">"Wait"</string>
    <string name="button_download_now">"Download now"</string>
    <string name="insufficient_space">"Not enough storage"</string>
    <string name="enter_garbage_clean">"Clean up internal storage."</string>
    <string name="download_apk_desktop_pending">Pending</string>
    <string name="download_apk_desktop_pause">Paused</string>
    <string name="download_apk_desktop_running">Downloading</string>
    <string name="download_apk_desktop_installing">Installing</string>
    <!--高速试用-->
    <string name="xl_speedup_experience_notify_title">Xunlei membership</string>
    <string name="xl_speedup_experience_notify_context">Speed is boosted for every download when you\'re a Xunlei member</string>
    <string name="xl_speedup_notify_button_experience">Try for free</string>
    <string name="xl_speedup_notify_button_member">Become a member</string>

    <string name="notify_disabled_net_by_app">Internet connection restricted by Security app</string>
    <string name="notify_only_wifi_download">Waiting for WLAN</string>

    <string name="notify_install_guide_title">Installed %s successfully</string>
    <string name="notify_install_guide_msg">Installed successfully</string>
    <string name="notify_install_guide_forward_text">Open</string>

    <string name="dialog_data_limit_title">Data usage warning</string>
    <string name="dialog_data_limit_msg1">%s of mobile data will be used to download this file. Download now?</string>
    <string name="dialog_data_limit_msg2">@string/dialog_tip_limit</string>
    <string name="dialog_data_limit_msg3">Change limit</string>
    <string name="dialog_data_limit_button1">Wait for WLAN</string>
    <string name="dialog_data_limit_button2">Download</string>


    <string name="dialog_data_limit_sel_title">@string/dialog_tip_limit</string>
    <string name="dialog_button_ok">OK</string>
    <string name="dialog_button_cancel">Cancel</string>
    <string name="dialog_tip_limit">Warn when downloading file bigger than %s</string>
    <string name="dialog_tip_no_limit">Don\'t warn</string>
    <string name="dialog_tip_limit0">Warn every time</string>
    <string name="dialog_content_limit_max">Unlimited</string>
    <string name="dialog_content_limit0">Restrict downloads</string>

    <string name="download_no_application">No apps that support this format</string>

    <string name="channel_active">Ongoing downloads</string>
    <string name="channel_waiting">Pending downloads</string>
    <string name="channel_insufficient_space">Insufficient storage</string>
    <string name="channel_complete">Finished downloads</string>

    <string name="recommend_open">Open</string>
    <string name="recommend_installing">Installing&#8230;</string>
    <!-- copy from framework -->
    <!-- Suffix added to a number to signify size in bytes. -->
    <string name="byteShort">B</string>
    <!-- Suffix added to a number to signify size in kilobytes. -->
    <string name="kilobyteShort">KB</string>
    <!-- Suffix added to a number to signify size in megabytes. -->
    <string name="megabyteShort">MB</string>
    <!-- Suffix added to a number to signify size in gigabytes. -->
    <string name="gigabyteShort">GB</string>
    <!-- Suffix added to a number to signify size in terabytes. -->
    <string name="terabyteShort">TB</string>
    <!-- Suffix added to a number to signify size in petabytes. -->
    <string name="petabyteShort">PB</string>
    <string name="fileSizeSuffix"><xliff:g example="123" id="number">%1$s</xliff:g> <xliff:g example="KB" id="unit">%2$s</xliff:g></string>

    <string name="download_filesize_unknown">Unknown</string>

    <string name="download_wait_connect">Waiting to connect</string>
    <string name="download_success" msgid="7006048006543495236">"Download complete"</string>
    <string name="download_error_insufficient_space">"Not enough storage"</string>
    <string name="download_error_download_file_not_exists">"File doesn\'t exist"</string>
    <string name="paused_by_app">Paused</string>
    <string name="paused_queued_for_wifi">Waiting for network</string>
    <string name="paused_waiting_to_retry">Waiting to retry</string>
    <string name="paused_unknown">Paused</string>

    <string name="paused_insufficient_space">Waiting</string>
    <string name="download_status_paused">Pause</string>
    <string name="download_status_continue">Resume</string>
    <string name="download_status_fail">Try again</string>

    <string name="downloading_empty">Empty</string>
    <string name="editmode_menu_delete">Delete</string>
    <plurals name="dialog_confirm_delete_downloads_message">
        <item quantity="one">Delete %d download?</item>
        <item quantity="other">Delete %d downloads?</item>
    </plurals>
    <string name="dialog_confirm_delete_the_download_item_message">Delete these tasks</string>
    <string name="dialog_confirm_delete_checkbox_message">Delete local files as well</string>
    <string name="delete_download" msgid="76629022653866471">"Delete"</string>
    <string name="dialog_delete_positive">Delete</string>
    <string name="download_info_start_deleted_tasks">Delete downloads</string>
    <plurals name="download_info_count_deleted_tasks">
        <item quantity="one">Deleted %s download successfully</item>
        <item quantity="other">Deleted %s downloads successfully</item>
    </plurals>
    <string name="dialog_file_missing_title">"Can't open file"</string>
    <string name="dialog_file_missing_body" msgid="2783781332668129395">"Couldn\'t find file, looks like it\'s been deleted."</string>
    <string name="retry_download" msgid="7617100787922717912">"Try again"</string>
    <string name="retry_after_network_available">"Can't connect to the network. Check your data connection and try again."</string>
    <string name="dialog_msg_disabled_net"><xliff:g id="app">%1$s</xliff:g> restricted downloads for the current network.Tap Network settings to go to the Security app.</string>
    <string name="dialog_title_disabled_net">Internet access</string>
    <string name="dialog_btn_ok_disabled_net">Network settings</string>
    <string name="downloading">Downloading\u2026</string>
    <string name="clear_data">Clear</string>
    <string name="download">Downloads</string>
    <string name="recall_tip">Install \"Downloads\" to use all features</string>
    <string name="today_download">Downloaded today</string>
    <string name="toast_net_error">Couldn\'t connect to the network. Check your network settings and try again.</string>
</resources>
