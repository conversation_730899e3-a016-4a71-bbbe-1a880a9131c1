<?xml version="1.0" encoding="utf-8"?><!--
/*
** Copyright 2010, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/item_download"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical"
    android:paddingTop="@dimen/downloading_container_top_bottom_padding"
    android:paddingBottom="@dimen/downloading_container_top_bottom_padding">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:descendantFocusability="blocksDescendants"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.android.providers.downloads.ui.ui.view.RoundedImageView
            android:id="@+id/download_icon"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:layout_gravity="top"
            android:layout_marginEnd="@dimen/icon_end_margin"
            app:riv_corner_radius="@dimen/icon_radius"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:id="@+id/download_title_and_size"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.0"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom">

                <TextView
                    android:id="@+id/downloading_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="middle"
                    android:gravity="start|center_vertical"
                    android:singleLine="true"
                    android:textAlignment="viewStart"
                    android:textColor="@color/text_primary_color"
                    android:textSize="@dimen/text_primary_size"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/date_status_info_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginStart="3dp"
                    android:ellipsize="end"
                    android:gravity="end"
                    android:singleLine="true"
                    android:textColor="@color/text_downloading_btn_color"
                    android:textSize="@dimen/text_secondary_size"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/size_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="1dp"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textAlignment="viewStart"
                    android:textColor="@color/text_secondary_color"
                    android:textSize="@dimen/text_secondary_size" />

                <TextView
                    android:id="@+id/date_status_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginStart="6dp"
                    android:ellipsize="end"
                    android:gravity="end"
                    android:maxWidth="100dp"
                    android:singleLine="true"
                    android:textColor="@color/text_secondary_color"
                    android:textSize="@dimen/text_secondary_size" />

            </LinearLayout>

            <com.android.providers.downloads.ui.ui.view.DownloadProgressBar
                android:id="@+id/progressbar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:layout_marginTop="5dp"
                android:max="100" />

        </LinearLayout>

        <FrameLayout
            android:id="@+id/download_time_and_status"
            android:layout_width="@dimen/download_btn_width"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp">

            <TextView
                android:id="@+id/action_button"
                android:layout_width="@dimen/download_btn_width"
                android:layout_height="@dimen/download_btn_height"
                android:layout_gravity="center"
                android:background="@drawable/downloading_btn_bg"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/text_downloading_btn_color"
                android:textSize="@dimen/text_secondary_size"
                android:textStyle="bold" />
            <CheckBox
                android:id="@android:id/checkbox"
                style="@style/Widget.CheckBox"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical|end"
                android:clickable="false"
                android:focusable="false"
                android:scaleType="fitCenter" />
        </FrameLayout>

    </LinearLayout>
</FrameLayout>
