<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <miuix.springback.view.SpringBackLayout
        android:id="@+id/list_header"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:scrollableView="@id/recycler_view">

        <miuix.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingStart="@dimen/page_margin_space"
            android:paddingEnd="@dimen/page_margin_space" />
    </miuix.springback.view.SpringBackLayout>

    <ViewStub
        android:id="@+id/empth_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</FrameLayout>