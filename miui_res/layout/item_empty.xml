<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_centerHorizontal="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:contentDescription="@string/downloading_empty"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/rank_empthview"
        android:layout_gravity="center_horizontal"
        android:gravity="center_horizontal"
        android:src="@drawable/nodata"
        android:layout_marginBottom="20dp"
        android:layout_marginTop="40dp"
        android:layout_centerHorizontal="true"
        android:contentDescription="@string/downloading_empty"/>

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text="@string/downloading_empty"
        android:textSize="@dimen/text_empty_size"
        android:layout_marginBottom="50dp"
        android:singleLine="true"
        android:textColor="@color/text_empth_color"
        android:layout_below="@id/rank_empthview"
        android:layout_centerHorizontal="true"/>

</RelativeLayout>


