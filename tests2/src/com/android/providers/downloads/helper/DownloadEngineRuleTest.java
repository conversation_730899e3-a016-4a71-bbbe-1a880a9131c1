package com.android.providers.downloads.helper;

import static org.junit.Assert.*;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.android.providers.downloads.api.cloudcontrol.DownloadEngineConfig;
import com.android.providers.downloads.setting.PrivacySettingHelper;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.Map;

@RunWith(AndroidJUnit4.class)
public class DownloadEngineRuleTest {

    private DownloadEngineRule downloadEngineRule;

    @Before
    public void setUp() throws Exception {
        downloadEngineRule = DownloadEngineRule.getInstance();
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void testCloudAndConfig() {
        downloadEngineRule.parseCloudAndConfig(null);
        Map<String,Boolean> map = getDownloadEngineConfig(downloadEngineRule);
        if (map == null) {
            assertNotNull(map);
        } else {
            assertEquals(0,map.size());
            assertEquals(-1,getallEngineType(downloadEngineRule));
        }


        DownloadEngineConfig downloadEngine = new DownloadEngineConfig();
        downloadEngine.xunlei = new ArrayList<>();
        downloadEngine.xunlei.add("com.xiaomi.market");
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        map = getDownloadEngineConfig(downloadEngineRule);
        if (map == null) {
            assertNotNull(map);
        } else {
            assertNotEquals(null, map);
            assertEquals(1, map.size());
            assertEquals(true, map.get("com.xiaomi.market"));
            assertEquals(null, map.get("com.xiaomi.market.other"));
            assertEquals(-1, getallEngineType(downloadEngineRule));
        }

        downloadEngine.xunlei = new ArrayList<>();
        downloadEngine.xunlei.add("all");
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        map = getDownloadEngineConfig(downloadEngineRule);
        if (map == null) {
            assertNotNull(map);
        } else {
            assertNotEquals(null, map);
            assertEquals(1, map.size());
            assertEquals(true, map.get("all"));
            assertEquals(0, getallEngineType(downloadEngineRule));
        }

        downloadEngine.xunlei = null;
        downloadEngine.android = new ArrayList<>();
        downloadEngine.android.add("com.xiaomi.market");
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        map = getDownloadEngineConfig(downloadEngineRule);
        if (map == null) {
            assertNotNull(map);
        } else {
            assertNotEquals(null, map);
            assertEquals(1, map.size());
            assertEquals(false, map.get("com.xiaomi.market"));
            assertEquals(null, map.get("com.xiaomi.market.other"));
            assertEquals(-1, getallEngineType(downloadEngineRule));
        }

        downloadEngine.android = new ArrayList<>();
        downloadEngine.android.add("all");
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        map = getDownloadEngineConfig(downloadEngineRule);
        if (map == null) {
            assertNotNull(map);
        } else {
            assertEquals(1, map.size());
            assertEquals(false, map.get("all"));
            assertEquals(1, getallEngineType(downloadEngineRule));
        }
    }

    private Map<String,Boolean> getDownloadEngineConfig(DownloadEngineRule downloadEngineRule) {
        Map<String,Boolean> map = null;
        try {
            Field mUseXunleiEngine = DownloadEngineRule.class.getDeclaredField("mUseXunleiEngine");
            mUseXunleiEngine.setAccessible(true);
            map = (Map<String,Boolean>) mUseXunleiEngine.get(downloadEngineRule);
        } catch (Exception e) {

        }
        return map;
    }

    private int getallEngineType(DownloadEngineRule downloadEngineRule) {
        int type = -1;
        try {
            Field allEngineType = DownloadEngineRule.class.getDeclaredField("allEngineType");
            allEngineType.setAccessible(true);
            type = allEngineType.getInt(downloadEngineRule);
        } catch (Exception e) {

        }
        return type;
    }

    private String enbleXunleJson(boolean enable) {
        return "{\"useEngine\": " + enable + "}";
    }

    @Test
    public void useXunleiEngine() {
        downloadEngineRule.parseCloudAndConfig(null);
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));

        DownloadEngineConfig downloadEngine = new DownloadEngineConfig();
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));

        ArrayList<String> xunlei = new ArrayList<>();
        ArrayList<String> android = new ArrayList<>();
        downloadEngine.xunlei = xunlei;
        downloadEngine.android = android;
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));
        xunlei.add("all");
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));
        xunlei.add("all");
        android.add("all");
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));
        xunlei.clear();
        android.add("all");
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));

        xunlei.add("com.xiaomi.market");
        android.clear();
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market.other",enbleXunleJson(true)));
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market.other",enbleXunleJson(false)));

        xunlei.clear();
        android.add("com.xiaomi.market");
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market.other",enbleXunleJson(true)));
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market.other",enbleXunleJson(false)));

        xunlei.add("com.xiaomi.market");
        android.add("com.xiaomi.market");
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market.other",enbleXunleJson(true)));
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market.other",enbleXunleJson(false)));

        xunlei.clear();
        android.clear();
        downloadEngineRule.parseCloudAndConfig(downloadEngine);
        PrivacySettingHelper.setXunleiUsageOpen(false);
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",""));
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",null));
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market","{\"useEngine\":1}"));


        PrivacySettingHelper.setXunleiUsageOpen(true);
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(true)));
        assertEquals(false, downloadEngineRule.useXunleiEngine("com.xiaomi.market",enbleXunleJson(false)));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",""));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market",null));
        assertEquals(true, downloadEngineRule.useXunleiEngine("com.xiaomi.market","{\"useEngine\":1}"));
    }
}