plugins {
    id 'com.android.application'
    id 'prebuilt-snapshot-plugin'
    id 'com.miui.downloadaar'
//    id 'extract-apk-info-plugin'
}
def projectDir = '../../'
def lines = file("${projectDir}/README").readLines()
def versionCodeFromREADME = lines.get(0).split(':')[1]
def versionNameFromREADME = lines.get(1).split(':')[1]

android {
    namespace 'com.android.providers.downloads'
    compileSdkPreview "${prebuiltSnapshotPlugin.prebuiltCompileSdk}"
    defaultConfig {
        applicationId "com.android.providers.downloads"
        minSdkVersion 26
        targetSdkVersion 34
        versionCode versionCodeFromREADME.toInteger()
        versionName versionNameFromREADME
        multiDexEnabled false

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    signingConfigs {
        config {
            storeFile file(projectDir+"ui/phone/signature/media.jks")
            storePassword '123456'
            keyAlias 'media'
            keyPassword '123456'
        }
    }
    buildTypes {

        debug {
            signingConfig signingConfigs.config
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard.flags'
        }
        release {
            signingConfig signingConfigs.config
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), projectDir+'proguard.flags'
        }
    }

    compileOptions{
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            println "output def apk name:" + output.outputFile
            def outFile = output.outputFile
            def name = output.outputFile.name
            if (name.contains("debug")) {
                output.versionCodeOverride = **********
                output.versionNameOverride = "999.99.99.999999"
                println "debugVersionCode:${output.versionCodeOverride}"
                println "debugVersionName:${output.versionNameOverride}"
                outputFileName = "DownloadProvider_34_" + versionName + ".apk"
            }
            //gradle 7.x compileSdkPreview bug， 需要在release 时移除 android:testOnly="true"
            if (variant.buildType.name == 'release') {
                output.processManifest.doLast {
                    //build/intermediates/merged_manifests/phoneRelease/AndroidManifest.xml
                    def manifestFile = new File("${buildDir}/intermediates/merged_manifests/${variant.name}/AndroidManifest.xml")
                    def manifestText = manifestFile.getText('UTF-8')
                    manifestText = manifestText.replace('android:testOnly="true"', '')
                    manifestFile.write(manifestText, 'UTF-8')
                }
            }
            println "output fix apk name:" + outFile.parent + "/" + outputFileName
        }
    }

    sourceSets {
        main {
            java.srcDirs = [projectDir+'src']
            manifest.srcFile projectDir+'AndroidManifest.xml'
            println manifest.srcFile
            aidl.srcDirs = [projectDir+'src']
            renderscript.srcDirs = [projectDir+'src']
            res.srcDirs = [projectDir+'miui_res',projectDir+'res']
            assets.srcDirs = [projectDir+'assets']
            jniLibs.srcDirs = [projectDir+'libs']
        }
        androidTest {
            java.srcDirs = ["${projectDir}/tests2/src","${projectDir}/tests/src"]
            manifest.srcFile "${projectDir}/tests/AndroidManifest.xml"
        }
    }
}

//打自升级包，尾号+2
task packageAutoUpdate(dependsOn: ['assembleRelease']) {
    def newVersionCode = versionCodeFromREADME.toInteger() + 2
    def newVersionName = updateVersionName(versionNameFromREADME)
    android.applicationVariants.all { variant ->
        variant.outputs.each { output ->
            def taskName = gradle.getStartParameter().getTaskNames().toString()
            if (!taskName.contains('packageAutoUpdate')) {
               return
            }
            println 'packageAutoUpdate'
            if (variant.buildType.name == 'release') {
                output.versionCodeOverride = newVersionCode
                output.versionNameOverride = newVersionName
                println "autoUpdateVersionCode:${output.versionCodeOverride}"
                println "autoUpdateVersionName:${output.versionNameOverride}"
            }
        }
    }
}

static def updateVersionName(String version) {
    def str = version.substring(0, version.length()-1)
    return str+"2"
}


//businessName对应prebuilt_config.json中的"business_name"对应业务的值，如MiuiSystemUI、Settings，跟plugins同级，不需要[]，
//例子：businessName = "MiuiSystemUI";
miExtension {
    businessName = "DownloadProvider"
}
def miuixVersion(String name) {
    def json = new groovy.json.JsonSlurper().parseText(file("${rootDir}/../prebuilts_lib_beta.json").getText())
    def version
    json.libs.each {
        if (name.equals(it.name)) {
            version = it.version
        }
    }
    return version
}
dependencies {
//    apply from: 'build-ext.gradle'
    implementation fileTree(include: ['*.jar', '*.aar'], dir: projectDir + 'libs')
    compileOnly files("${prebuiltSnapshotPlugin.miuiFrameworkJar}")
//    compileOnly 'com.miui:core:dev-SNAPSHOT'
    compileOnly 'com.miui:system:dev-SNAPSHOT'
    implementation("com.google.guava:guava:30.1.1-android")
    implementation("miuix.appcompat:appcompat:${miuixVersion("appcompat")}")
    implementation "miuix.preference:preference:${miuixVersion("preference")}"
    implementation "miuix.graphics:graphics:${miuixVersion("graphics")}"
    implementation "miuix.nestedheader:nestedheader:${miuixVersion("nestedheader")}"
    implementation "miuix.recyclerview:recyclerview:${miuixVersion("recyclerview")}"
    implementation "miuix.autodensity:autodensity:${miuixVersion("autodensity")}"
    implementation "miuix.fileicon:fileicon:${miuixVersion("fileicon")}"
    implementation "androidx.startup:startup-runtime:${miuixVersion("startup-runtime")}"

    androidTestImplementation "androidx.test:core:1.5.0"
//    androidTestImplementation "androidx.test:espresso:espresso-core:3.5.1"
    androidTestImplementation "androidx.test.ext:junit:1.1.5"
    androidTestImplementation "androidx.test.ext:truth:1.5.0"
    androidTestImplementation "androidx.test:runner:1.5.2"
    androidTestUtil "androidx.test:orchestrator:1.4.2"
    androidTestImplementation "org.mockito:mockito-core:5.4.0"
    androidTestImplementation 'com.google.mockwebserver:mockwebserver:20130706'
    androidTestImplementation fileTree( dir: '../testlibs',include: ['*.jar'],)
//    implementation fileTree(dir: "miuilibs", include: ["*.jar", "*.aar"])
//    compileOnly fileTree(dir: "miuilibs/framework/libs", include: ["*.jar", "*.aar"])
}
