plugins {
    id 'com.android.library'
    id 'prebuilt-snapshot-plugin'
//    id 'extract-apk-info-plugin'
}
android {
    namespace 'com.android.providers.downloads.public_api_access_tests'
    compileSdkPreview "${prebuiltSnapshotPlugin.prebuiltCompileSdk}"

    defaultConfig {
        minSdk 24
        targetSdk 33

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    sourceSets {
        androidTest {
            java.srcDirs = ["${rootDir}/../tests/public_api_access/src"]
            manifest.srcFile "${rootDir}/../tests/public_api_access/AndroidManifest.xml"
        }
    }
}

dependencies {

    compileOnly files("${prebuiltSnapshotPlugin.miuiFrameworkJar}")
    androidTestImplementation "androidx.test:core:1.5.0"
//    androidTestImplementation "androidx.test:espresso:espresso-core:3.5.1"
    androidTestImplementation "androidx.test.ext:junit:1.1.5"
    androidTestImplementation "androidx.test.ext:truth:1.5.0"
    androidTestImplementation "androidx.test:runner:1.5.2"
    androidTestUtil "androidx.test:orchestrator:1.4.2"
    androidTestImplementation "org.mockito:mockito-core:5.4.0"
    androidTestImplementation 'com.google.mockwebserver:mockwebserver:20130706'
    androidTestImplementation fileTree( dir: '../testlibs',include: ['*.jar'],)
}