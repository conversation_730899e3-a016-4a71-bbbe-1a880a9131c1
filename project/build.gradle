buildscript {
    dependencies {
        configurations.all {
            resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
            resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
        }
        classpath "com.miui.prebuilt:prebuilt-snapshot-plugin:0.0.15-SNAPSHOT"
        classpath "com.miui.downloadaar:DownloadAARPlugin:1.5.4-SNAPSHOT"
        classpath "com.miui.extractApkInfo:extract-apk-info-plugin:0.0.2-SNAPSHOT"
    }
}

plugins {
    id 'com.android.application' version '7.4.1' apply false
    id 'com.android.library' version '7.4.1' apply false
}