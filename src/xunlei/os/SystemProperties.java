package xunlei.os;

import android.util.Log;

public class SystemProperties {
    private static final String TAG = "SystemProperties";

    public static String get(String key, String defValue) {
        try {
            String value = (String) Class.forName("android.os.SystemProperties")
                    .getMethod("get", String.class, String.class)
                    .invoke(null, key, defValue);
            return value;
        } catch (Exception e) {
            Log.d(TAG, "get exc", e);
        }
        return defValue;
    }

    public static boolean getBoolean(String key, boolean defValue) {
        try {
            Boolean value = (Boolean) Class.forName("android.os.SystemProperties")
                    .getMethod("getBoolean", String.class, Boolean.TYPE)
                    .invoke(null, key, defValue);
            return value;
        } catch (Exception e) {
            Log.d(TAG, "get exc", e);
        }
        return defValue;
    }

    public static int getInt(String key, int defValue) {
        try {
            Integer value = (Integer) Class.forName("android.os.SystemProperties")
                    .getMethod("getInt", String.class, Integer.TYPE)
                    .invoke(null, key, defValue);
            return value;
        } catch (Exception e) {
            Log.d(TAG, "get exc", e);
        }
        return defValue;
    }

    public static long getLong(String key, long defValue) {
        try {
            Long value = (Long) Class.forName("android.os.SystemProperties")
                    .getMethod("getLong", String.class, Long.TYPE)
                    .invoke(null, key, defValue);
            return value;
        } catch (Exception e) {
            Log.d(TAG, "get exc", e);
        }
        return defValue;
    }
}
