package com.android.providers.downloads.api.cloudcontrol;

import com.michael.corelib.internet.core.json.JsonProperty;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

public class DownloadEngineConfig implements Serializable {
    @JsonProperty("xunlei")
    public List<String> xunlei;

    @JsonProperty("android")
    public List<String> android;

    @Override
    public String toString() {
        return "DownloadEngineConfig{" +
                "xunlei=" + xunlei +
                ", android=" + android +
                '}';
    }
}
