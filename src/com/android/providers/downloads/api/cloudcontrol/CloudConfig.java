package com.android.providers.downloads.api.cloudcontrol;

import com.michael.corelib.internet.core.json.JsonProperty;

import java.util.List;

/**
 * Created by wangyong on 15/12/24.
 */
public class CloudConfig {

    @JsonProperty("file_size_up_limit")
    public int fileSizeLimit;

    @JsonProperty("file_download_process")
    public int fileDownloadPercent;

    @JsonProperty("trial_times_per_user")
    public int accelerateCount;

    @JsonProperty("trial_period")
    public int accelerateDration;

    @JsonProperty("reset_period")
    public int resetDuration;

    @JsonProperty("low_speed_time")
    public int lowSpeedDuration;

    @JsonProperty("low_speed")
    public int lowSpeed;

    @JsonProperty("accelerate_switch")
    public boolean accelerateSwitch;

    @JsonProperty("notify_recommed_switch")
    public boolean notifyRecommendSwitch;

    @JsonProperty("notify_recommed_recType")
    public String notifyRecommendRectype;

    @JsonProperty("notify_recommed_dayCount")
    public int notifyRecommendCount;

    @JsonProperty("notify_recommed_imei_switch")
    public String  notifyRecommendImeiSwitch;

    @JsonProperty("notify_recommed_network_type")
    public int  notifyRecommendNetworkType;

    @JsonProperty("notify_recommed_duration")
    public long  notifyRecommendduration;

    @JsonProperty("dev_show_activate_notify")
    public boolean isDevShowActivateNotify;

    @JsonProperty("stable_show_activate_notify")
    public boolean isStableShowActivateNotify;

    @JsonProperty("black_list_show_task")
    public String blackListShowTaskWithUiAndNotify;

    @JsonProperty("black_list_notify_filter")
    public String blackListNotifyFilter;

    @JsonProperty("data_limit_config")
    public String dataLimitConfig;

    @JsonProperty("jump_market_switch")
    public String jumpMarketSwitch;

    @JsonProperty("try_speed_up_config")
    public String trySpeedUpConfig;

    @JsonProperty("limit_speed_config")
    public String limitSpeedConfig;

    @JsonProperty("use_xunlei_engine")
    public int useXunleiEngine;

    @JsonProperty("use_xunlei_engine_inR")
    public int useXunleiEngineInR;

    @JsonProperty("jumpMarketByMiuiVersion")
    public String jumpMarketByMiuiVersion;

    @JsonProperty("scdn_white_list")
    public String scdnWhiteList;

    @JsonProperty("bigmem")
    public String bigmem;

    @JsonProperty("switch_trace_api")
    public String switchTraceApi;

    @JsonProperty("switch_trace_service_start")
    public String switchTraceServiceStart;

    @JsonProperty("switch_trace_process_start")
    public String switchTraceProcessStart;

    @JsonProperty("switch_trace_method_call")
    public String switchTraceMethodCall;

    @JsonProperty("token_fail_update_interval")
    public long tokenFailUpdateInterval;

    @JsonProperty("limit_speed_config2")
    public String limitSpeedConfig2;

    @JsonProperty("up_onetrack")
    public String upOneTrack;

    @JsonProperty("switch_conns_onetrack")
    public String switchConnsOnetrack;

    @JsonProperty("switch_conns_hub")
    public String switchConnsHub;

    @JsonProperty("switch_speedup2")
    public String switchSpeedup2;

    @JsonProperty("switch_check_file_size")
    public String switchCheckFileSize;

    @JsonProperty("switch_data_limit")
    public String switchDataLimit;

    @JsonProperty("download_engine")
    public DownloadEngineConfig downloadEngine;

    @JsonProperty("feature_switch")
    public List<String> featureSwitch;

    //云控外部能否设置持锁时间，默认true
    @JsonProperty("wakelock_exposed")
    public boolean wakelockExposed;
    @Override
    public String toString() {
        return "CloudConfig{" +
                "fileSizeLimit=" + fileSizeLimit +
                ", fileDownloadPercent=" + fileDownloadPercent +
                ", accelerateCount=" + accelerateCount +
                ", accelerateDration=" + accelerateDration +
                ", resetDuration=" + resetDuration +
                ", lowSpeedDuration=" + lowSpeedDuration +
                ", lowSpeed=" + lowSpeed +
                ", accelerateSwitch=" + accelerateSwitch +
                ", notifyRecommendSwitch=" + notifyRecommendSwitch +
                ", notifyRecommendRectype='" + notifyRecommendRectype + '\'' +
                ", notifyRecommendCount=" + notifyRecommendCount +
                ", notifyRecommendImeiSwitch='" + notifyRecommendImeiSwitch + '\'' +
                ", notifyRecommendNetworkType=" + notifyRecommendNetworkType +
                ", notifyRecommendduration=" + notifyRecommendduration +
                ", isDevShowActivateNotify=" + isDevShowActivateNotify +
                ", isStableShowActivateNotify=" + isStableShowActivateNotify +
                ", blackListShowTaskWithUiAndNotify='" + blackListShowTaskWithUiAndNotify + '\'' +
                ", blackListNotifyFilter='" + blackListNotifyFilter + '\'' +
                ", dataLimitConfig='" + dataLimitConfig + '\'' +
                ", jumpMarketSwitch='" + jumpMarketSwitch + '\'' +
                ", trySpeedUpConfig='" + trySpeedUpConfig + '\'' +
                ", limitSpeedConfig='" + limitSpeedConfig + '\'' +
                ", useXunleiEngine=" + useXunleiEngine +
                ", useXunleiEngineInR=" + useXunleiEngineInR +
                ", jumpMarketByMiuiVersion='" + jumpMarketByMiuiVersion + '\'' +
                ", scdnWhiteList='" + scdnWhiteList + '\'' +
                ", bigmem='" + bigmem + '\'' +
                ", switchTraceApi='" + switchTraceApi + '\'' +
                ", switchTraceServiceStart='" + switchTraceServiceStart + '\'' +
                ", switchTraceProcessStart='" + switchTraceProcessStart + '\'' +
                ", switchTraceMethodCall='" + switchTraceMethodCall + '\'' +
                ", tokenFailUpdateInterval='" + tokenFailUpdateInterval + '\'' +
                ", limitSpeedConfig2='" + limitSpeedConfig2 + '\'' +
                ", upOneTrack='" + upOneTrack + '\'' +
                ", switchSpeedup2='" + switchSpeedup2 + '\'' +
                ", switchCheckFileSize='" + switchCheckFileSize + '\'' +
                ", switchDataLimit='" + switchDataLimit + '\'' +
                ", downloadEngine='" + downloadEngine + '\'' +
                ", featureSwitch='" + featureSwitch + '\'' +
                ", wakelockExposed='" + wakelockExposed + '\'' +
                '}';
    }
}
