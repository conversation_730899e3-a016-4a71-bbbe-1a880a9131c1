package com.android.providers.downloads.ui.data.directmail;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.michael.corelib.internet.core.NetWorkException;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class DeeplinkRepository {
    private static String API_RELEASE_URL = "http://api.tw06.xlmc.sec.miui.com/deeplink/sign";

    public DeeplinResponse getDeeplink(Context context) throws NetWorkException, IOException {
        OkHttpClient okHttpClient = new OkHttpClient().newBuilder()
                .connectTimeout(2, TimeUnit.SECONDS)
                .callTimeout(2,TimeUnit.SECONDS)
                .readTimeout(2,TimeUnit.SECONDS)
                .build();
        String json = new Gson().toJson(new DeeplinkRequest());
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
        Request build = new Request.Builder().url(API_RELEASE_URL).post(body).build();
        Response response = okHttpClient.newCall(build).execute();
        String s = response.body().string();
        DeeplinResponse deeplinResponse = new Gson().fromJson(s, DeeplinResponse.class);
        return deeplinResponse;
    }

}
