package com.android.providers.downloads.ui.data;

import static com.android.providers.downloads.ui.utils.FileUtil.getDownloadType;
import static com.android.providers.downloads.xunlei.speedup.XLSpeedUpManager.TASK_SPEEDUP_RUNNING;

import android.content.Context;
import android.util.Log;

import com.android.providers.downloads.R;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.ui.ui.DownloadViewModel;
import com.android.providers.downloads.ui.ui.view.editmode.CheckState;
import com.android.providers.downloads.ui.utils.DateUtil;
import com.android.providers.downloads.ui.utils.SizeFormatUtil;
import com.xiaomi.android.app.downloadmanager.MiuiDownloadManager;

/**
 * Created by lich<PERSON> on 18/1/12.
 */
public class DownloadTask  {

    private static final String BT_EXTENSION_NAME = "torrent";
    public static final int STATUS_CODE_DEFAULT = 0;
    public static final int STATUS_CODE_PENDING = 1;
    public static final int STATUS_CODE_DOWNLOAD_DOWNLOADING = 2;
    public static final int STATUS_CODE_DOWNLOAD_PAUSED = 3;
    public static final int STATUS_CODE_DOWNLOAD_FAILED = 4;
    public static final int STATUS_CODE_DOWNLOAD_SUCCESS = 5;

    /**
     * 下载任务Id
     */
    public int downloadId;

    /**
     * 下载任务名，对应于 COLUMN_TITLE
     */
    public String title;

    /**
     * 任务状态
     */
    public int status;

    /**
     * 任务失败或者暂停原因
     */
    public int reason;


    /**
     * 任务总大小
     */
    public long totalBytes;

    /**
     * 对应于 COLUMN_MEDIA_TYPE
     */
    public String mimeType;

    /**
     * 对应于 COLUMN_LAST_MODIFIED_TIMESTAMP
     */
    public long lastModifyTime;


    /**
     * 对应于 COLUMN_LOCAL_FILENAME
     */
    public String localFileName;


    /**
     * 对应于 COLUMN_BYTES_DOWNLOADED_SO_FAR
     */
    public long currentBytes;


    /**
     * 对应于  ExtraDownloads.COLUMN_DOWNLOADING_CURRENT_SPEED
     */
    public long currentDownloadSpeed;


    /**
     * 对应于 ExtraDownloads.COLUMN_XL_ACCELERATE_SPEED
     */
    public long vipSpeed;

    /**
     * 对应于 ExtraDownloads.COLUMN_DOWNLOAD_SURPLUS_TIME
     */
    public long p2spSpeed;

    /**
     * 对应于 ExtraDownloads.COLUMN_TASK_FOR_THUMBNAIL
     */
    public String iconUrl;

    /**
     * 对应于  XLDownloadManager.COLUMN_URI
     */
    public String downloadUri;

    /**
     * 对应于 ExtraDownloads.COLUMN_TRYSPEEDUP_STATUS
     */
    public int vipSpeedUpStatus;

    /**
     * 对应于 COLUMN_APK_PACKGENAME
     */
    public String pkgName;

    /**
     * 对应于 COLUMN_NOTIFICATION_PACKAGE
     */
    public String notificationPkgName;

    /**
     * errormsg
     */
    public String errorMsg;


    /***
     * 对应 COLUMN_DESCRIPTION
     */
    public String description;

    public static final int PKG_STATUS_CODE_DEFAULT = 0;
    public static final int PKG_STATUS_CODE_PENDING = 1;
    public static final int PKG_STATUS_CODE_BEGIN_INSTALL = 1;
    public static final int PKG_STATUS_CODE_INSTALL_SUCCESS = 2;
    public static final int PKG_STATUS_CODE_INSTALL_FAILED = 3;
    public static final int PKG_STATUS_CODE_HAS_NOT_INSTALL = 4;

    public int appStatus = PKG_STATUS_CODE_DEFAULT;
    public int percent;
    public int protocolType;
    public boolean isM3u8Task() {
        return protocolType == XLDownloadCfg.DownloadType.M3U8.ordinal();
    }



    @Override
    public String toString() {
        return "DownloadTask{" +
                "downloadId=" + downloadId +
                ", title='" + title + '\'' +
                ", status=" + status +
                ", reason=" + reason +
                ", totalBytes=" + totalBytes +
                ", mimeType='" + mimeType + '\'' +
                ", lastModifyTime=" + lastModifyTime +
                ", localFileName='" + localFileName + '\'' +
                ", currentBytes=" + currentBytes +
                ", currentDownloadSpeed=" + currentDownloadSpeed +
                ", vipSpeed=" + vipSpeed +
                ", p2spSpeed=" + p2spSpeed +
                ", iconUrl='" + iconUrl + '\'' +
                ", downloadUri='" + downloadUri + '\'' +
                ", vipSpeedUpStatus=" + vipSpeedUpStatus +
                ", pkgName='" + pkgName + '\'' +
                ", notificationPkgName='" + notificationPkgName + '\'' +
                ", errorMsg='" + errorMsg + '\'' +
                ", description='" + description + '\'' +
                ", appStatus=" + appStatus +
                ", percent=" + percent +
                ", protocolType=" + protocolType +
                '}';
    }

    public void updateTask(DownloadTask bean) {
        if (bean == null) {
            return;
        }
        status = bean.status;
        title = bean.title;
        reason = bean.reason;
        lastModifyTime = bean.lastModifyTime;
        currentBytes = bean.currentBytes;
        currentDownloadSpeed = bean.currentDownloadSpeed;
        vipSpeed = bean.vipSpeed;
        p2spSpeed = bean.p2spSpeed;
        vipSpeedUpStatus = bean.vipSpeedUpStatus;
        errorMsg = bean.errorMsg;
        percent = bean.percent;
        protocolType = bean.protocolType;
    }



    public boolean isTorrentLink(String uri) {
        XLDownloadCfg.DownloadType type = XLDownloadCfg.DownloadType.UNKNOW;
        try {
            type = getDownloadType(uri);
        } catch (Exception e) {
            //Ignore exception
            Log.getStackTraceString(e);
        }

        return ((type == XLDownloadCfg.DownloadType.HTTP || type == XLDownloadCfg.DownloadType.HTTPS) && uri.endsWith(BT_EXTENSION_NAME)|| type == XLDownloadCfg.DownloadType.MAGNET);
    }

    public boolean isSuccess() {
        return status == STATUS_CODE_DOWNLOAD_SUCCESS;
    }

    public boolean isFail() {
        return status == STATUS_CODE_DOWNLOAD_FAILED;
    }

    public boolean isPending() {
        return status == STATUS_CODE_PENDING;
    }

    public boolean isDownloading() {
        return status == STATUS_CODE_DOWNLOAD_DOWNLOADING;
    }

    public boolean isPaused() {
        return status == STATUS_CODE_DOWNLOAD_PAUSED;
    }

    public boolean isPausedForWifi() {
        return reason == MiuiDownloadManager.PAUSED_QUEUED_FOR_WIFI;
    }

    public boolean isSpeedUping() {
        return vipSpeedUpStatus == TASK_SPEEDUP_RUNNING;
    }
}
