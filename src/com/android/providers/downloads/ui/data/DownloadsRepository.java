package com.android.providers.downloads.ui.data;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.text.TextUtils;
import android.util.SparseArray;
import android.util.SparseIntArray;

import androidx.annotation.NonNull;

import com.android.providers.downloads.config.XLConfig;
import com.xiaomi.android.app.downloadmanager.MiuiDownloads;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DownloadsRepository {
    public final static String TAG = "DownloadsRepository";
    private final int MSG_MARKET = 1000;
    private final int MSG_ADD_LISTENER = 1001;
    private final int MSG_REMOVE_LISTENER = 1002;
    private MiMarketDataSource mMiMarketDataSource;
    private DownloadManagerDataSource mDownloadManagerDataSource;
    private final Context mContext;
    private SparseArray<DownloadTask> mList = new SparseArray<DownloadTask>();
    private Map<String, DownloadTask> mPackageMap = new HashMap<>();

    private LoopPolicy mLoop;
    private long mLastUpdateTime;
    private long mMarketUpdateTime;
    private List<DownloadTaskChange> mDownloadTaskChanges = new ArrayList<>();
    private ContentObserver mDownloadDbObserver;
    private volatile Handler mHandler;
    private DownloadTaskGlobalLock mGlobalLock = new DownloadTaskGlobalLock();

    public DownloadsRepository(Context context) {
        mContext = context.getApplicationContext();
        mDownloadManagerDataSource = new DownloadManagerDataSource(context);
    }

    public DownloadTask getByPackageName(String packageName) {
        return mPackageMap.get(packageName);
    }

    public void observeDownloadTask(DownloadTaskChange downloadTaskChange) {
        synchronized (mGlobalLock) {
            Handler handler = getHandler();
            Message message = handler.obtainMessage();
            message.what = MSG_ADD_LISTENER;
            message.obj = downloadTaskChange;
            handler.sendMessage(message);
        }
    }

    public void unObserveDownloadTask(DownloadTaskChange downloadTaskChange) {
        synchronized (mGlobalLock) {
            if (mHandler != null) {
                Message message = mHandler.obtainMessage();
                message.what = MSG_REMOVE_LISTENER;
                message.obj = downloadTaskChange;
                mHandler.sendMessage(message);
            }
        }
    }

    private void loop() {
        if (mDownloadTaskChanges.size() > 0) {
            if (mLastUpdateTime != 0) {
                getDownloadTasks(mContext);
            }
            if (mLastUpdateTime != 0 || mMarketUpdateTime != 0) {
                handleDownloadTaskChange(mList);
            }
        } else {
            //停止looper
            stopLooper();
        }
        mLastUpdateTime = 0;
        mMarketUpdateTime = 0;
    }

    private void startLooper() {
        if (mDownloadTaskChanges.size() <= 0) {
            return;
        }
        if (mLoop == null) {
            XLConfig.LOGD(TAG, "startLooper");
            mLoop = new LoopPolicy();
            mLastUpdateTime = System.currentTimeMillis();
            mLoop.looper(getHandler().getLooper(), new Runnable() {
                @Override
                public void run() {
                    loop();
                }
            });
            registerDownloadObserver(mContext);
            startMarketLisenter(mDownloadTaskChanges);
        }
    }

    private void startMarketLisenter(List<DownloadTaskChange> downloadTaskChanges) {
        if (downloadTaskChanges == null) {
            return;
        }
        boolean need = false;
        for (DownloadTaskChange change:downloadTaskChanges) {
            if (change.showMarketStatus()) {
                need = true;
                break;
            }
        }
        XLConfig.LOGD(TAG, "sstartMarketLisenter: need=" + need);
        if (need) {
            mMiMarketDataSource = new MiMarketDataSource();
            mMiMarketDataSource.registerConnect(mContext);
            mMiMarketDataSource.observerStatus(new PackageStatusChange() {
                @Override
                public void onReceive(PackageStatusChangeEvent event) {
                    if (mHandler == null) {
                        return;
                    }
                    synchronized (mGlobalLock) {
                        Message msg = mHandler.obtainMessage();
                        msg.what = MSG_MARKET;
                        msg.obj = event;
                        mHandler.sendMessage(msg);
                    }
                }
            });
        }
    }

    private void stopLooper() {
        XLConfig.LOGD(TAG, "stopLooper: mLoop=" + mLoop);
        if (mLoop != null) {
            mLoop.quit();
            mLoop = null;
            mHandler.removeCallbacksAndMessages(null);
            if (mHandler != null && mHandler.getLooper() != null) {
                synchronized (mGlobalLock) {
                    mHandler.getLooper().quit();
                    mHandler = null;
                }
            }
            if (mDownloadDbObserver != null) {
                mContext.getContentResolver().unregisterContentObserver(mDownloadDbObserver);
                mDownloadDbObserver = null;
            }
            if (mMiMarketDataSource != null) {
                mMiMarketDataSource.unRegisterConnect(mContext);
                mMiMarketDataSource = null;
            }
        }
    }

    private void handleDownloadTaskChange(SparseArray list) {
        if (mDownloadTaskChanges != null && mDownloadTaskChanges.size() > 0) {
            for (DownloadTaskChange taskChange : mDownloadTaskChanges) {
                taskChange.onChange(list);
            }
        }
    }

    private void registerDownloadObserver(Context context) {
        if (mDownloadDbObserver == null) {
            mDownloadDbObserver = new ContentObserver(getHandler()) {

                @Override
                public void onChange(boolean selfChange) {
                    super.onChange(selfChange);
                    mLastUpdateTime = System.currentTimeMillis();
                }

                @Override
                public void onChange(boolean selfChange, Uri uri) {
                    super.onChange(selfChange, uri);
                    mLastUpdateTime = System.currentTimeMillis();
                }
            };
            context.getContentResolver().registerContentObserver(MiuiDownloads.Impl.ALL_DOWNLOADS_CONTENT_URI, true, mDownloadDbObserver);
        }
    }

    private Handler getHandler() {
        if (mHandler == null) {
            HandlerThread updateThread = new HandlerThread("DownloadsViewModel-UpdateThread");
            updateThread.start();
            mHandler = new Handler(updateThread.getLooper()) {
                @Override
                public void handleMessage(@NonNull Message msg) {
                    switch (msg.what) {
                        case MSG_MARKET:
                            PackageStatusChangeEvent event = (PackageStatusChangeEvent) msg.obj;
                            mMarketUpdateTime = System.currentTimeMillis();
                            handleMiMarketData(event);
                            break;
                        case MSG_ADD_LISTENER:
                            DownloadTaskChange downloadTaskChange = (DownloadTaskChange) msg.obj;
                            if (downloadTaskChange != null) {
                                mDownloadTaskChanges.add(downloadTaskChange);
                            }
                            startLooper();
                            break;
                        case MSG_REMOVE_LISTENER:
                            DownloadTaskChange taskChange = (DownloadTaskChange) msg.obj;
                            mDownloadTaskChanges.remove(taskChange);
                            break;
                        default:
                            break;
                    }
                }
            };
        }
        return mHandler;
    }

    private void getDownloadTasks(Context context) {
        SparseArray<DownloadTask> allDownload = mDownloadManagerDataSource.getAllDownload(context);
        SparseIntArray deletedList = new SparseIntArray();
        if (allDownload != null) {
            int size = mList.size();
            for (int i = 0; i < size; i++) {
                int downloadId = mList.keyAt(i);
                DownloadTask task = mList.valueAt(i);
                DownloadTask bean = allDownload.get(downloadId);
                if (bean == null) {
                    //task deteled，so remove
                    deletedList.put(i, downloadId);
                    mPackageMap.remove(task.pkgName);
                } else {
                    //task update
                    task.updateTask(bean);
                    allDownload.remove(downloadId);
                }
            }
            //task remove
            if (deletedList.size() > 0) {
                for (int i = 0; i < deletedList.size(); i++) {
                    mList.removeAt(deletedList.keyAt(i));
                }
            }
            //new task
            if (allDownload.size() > 0) {
                for (int i = 0; i < allDownload.size(); i++) {
                    DownloadTask bean = allDownload.valueAt(i);
                    mList.put(bean.downloadId, bean);
                    if (!TextUtils.isEmpty(bean.pkgName)) {
                        mPackageMap.put(bean.pkgName, bean);
                    }
                }
            }
        }
        XLConfig.LOGD(TAG, String.format("mList: %d,mPackageMap: %d", mList.size(), mPackageMap.size()));
    }

    private void handleMiMarketData(PackageStatusChangeEvent event) {
        if (event == null || TextUtils.isEmpty(event.pkgName)) {
            return;
        }
        XLConfig.LOGD(TAG, String.format("handleMiMarketData: %s", event.toString()));
        DownloadTask task = mPackageMap.get(event.pkgName);
        if (task != null) {
            if (event.statusCode == PackageStatusChangeEvent.STATUS_CODE_INSTALL_START) {
                task.appStatus = DownloadTask.PKG_STATUS_CODE_BEGIN_INSTALL;
            } else if (event.statusCode == PackageStatusChangeEvent.STATUS_CODE_PACKAGE_INSTALL) {
                task.appStatus = DownloadTask.PKG_STATUS_CODE_INSTALL_SUCCESS;
            } else if (event.statusCode == PackageStatusChangeEvent.STATUS_CODE_PACKAGE_UNINSTALL) {
                task.appStatus = DownloadTask.PKG_STATUS_CODE_HAS_NOT_INSTALL;
            } else if ((event.statusCode == PackageStatusChangeEvent.PROGRESS_STATUS_CONNECTING
                    || event.status == PackageStatusChangeEvent.PROGRESS_STATUS_PENDING)) {
                task.appStatus = DownloadTask.PKG_STATUS_CODE_PENDING;
            } else {
                task.appStatus = DownloadTask.PKG_STATUS_CODE_DEFAULT;
            }
            XLConfig.LOGD(TAG, String.format("handleMiMarketData: task=%s", task.toString()));
        }
    }

    public DownloadTask getTaskbyId(int id) {
        return mDownloadManagerDataSource.getTaskById(mContext,id);
    }
}