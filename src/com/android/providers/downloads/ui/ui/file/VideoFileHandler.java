package com.android.providers.downloads.ui.ui.file;

import static com.android.providers.downloads.ui.ui.file.OtherFileHandler.MIME_ALL;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.android.providers.downloads.R;
import com.android.providers.downloads.ui.data.DownloadTask;
import com.android.providers.downloads.ui.domain.DownloadUseCase;
import com.android.providers.downloads.ui.ui.DownloadViewModel;
import com.android.providers.downloads.util.MimeUtils;
import com.android.providers.downloads.util.OpenHelper;

import java.io.File;
import java.io.IOException;

public class VideoFileHandler extends FileHandler{
    public VideoFileHandler(DownloadViewModel viewModel, DownloadUseCase downloadUseCase, FileHandler next) {
        super(viewModel, downloadUseCase, next);
    }

    @Override
    protected boolean openDownloadedTask(DownloadTask task) {
        boolean isSuccess;

        try {
            String filePath = task.localFileName;
            Intent intent = new Intent(Intent.ACTION_VIEW);
            Uri localUri;
            Application application = mDownloadViewModel.getApplication();
            if (Build.VERSION.SDK_INT >= 30) {
                localUri = OpenHelper.filetoUri(new File(filePath),task.downloadId,intent);
                if (localUri == null) {
                    Toast.makeText(application, R.string.download_no_application, Toast.LENGTH_LONG).show();
                    return false;
                }
            } else {
                localUri = Uri.fromFile(new File(filePath));
            }
            if (isMediaViewerInstalled(application) && isVideoFormat(filePath)) {
                intent.setPackage("com.miui.mediaviewer");
                intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                intent.setDataAndType(localUri, "video/*");
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_GRANT_READ_URI_PERMISSION);
                mDownloadViewModel.startActivity(intent);
                isSuccess = true;
            } else {
                isSuccess = false;
            }
        }catch (Exception e) {
            isSuccess = false;
        }
        return isSuccess;
    }

    public boolean isMediaViewerInstalled(Context context) {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo pkgInfo = pm.getPackageInfo("com.miui.mediaviewer", 0);
            return pkgInfo != null;
        } catch (PackageManager.NameNotFoundException e) {
            //Ignore exception
        }
        return false;
    }

    //filePath：文件的绝对路径
    public boolean isVideoFormat(String filePath) {
        MediaExtractor extractor = new MediaExtractor();
        try {
            extractor.setDataSource(filePath);
            int trackCount = extractor.getTrackCount();
            for (int i = 0; i < trackCount; i++) {
                MediaFormat format = extractor.getTrackFormat(i);
                String mimeType = format.getString(MediaFormat.KEY_MIME);
                if (mimeType.startsWith("video/")) {
                    // 当前轨道是视频格式，可以用媒体查看器打开，
                    return true;
                }
            }
        } catch (IOException e) {
            Log.e("VideoFileHandler", "get VideoFormat error ");
        }
        return false;
    }
}
