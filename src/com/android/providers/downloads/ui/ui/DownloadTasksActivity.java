package com.android.providers.downloads.ui.ui;

import static miuix.appcompat.app.ActionBar.STATE_COLLAPSE;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Rect;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.ActionMode;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.android.providers.downloads.R;
import com.android.providers.downloads.ui.data.DownloadTask;
import com.android.providers.downloads.ui.ui.adapter.DividerItemView;
import com.android.providers.downloads.ui.ui.adapter.DownloadedItemView;
import com.android.providers.downloads.ui.ui.adapter.DownloadingItemView;
import com.android.providers.downloads.ui.ui.adapter.EmpthItemView;
import com.android.providers.downloads.ui.ui.adapter.MutilAdapter;
import com.android.providers.downloads.ui.ui.adapter.TitleItemView;
import com.android.providers.downloads.ui.ui.view.editmode.CheckState;
import com.android.providers.downloads.ui.ui.view.editmode.CheckableManager;
import com.android.providers.downloads.ui.utils.ManifestUtils;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.StrictModeCompcat;
import com.android.providers.downloads.util.XLUtil;
import com.michael.corelib.coreutils.NetworkUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import miui.provider.ExtraNetwork;
import miuix.appcompat.app.ActionBar;
import miuix.appcompat.app.AlertDialog;
import miuix.appcompat.app.AppCompatActivity;
import miuix.appcompat.app.ProgressDialog;
import miuix.core.util.MiuixUIUtils;
import miuix.popupwidget.widget.GuidePopupWindow;
import miuix.recyclerview.card.CardDefaultItemAnimator;
import miuix.recyclerview.card.CardItemDecoration;
import miuix.theme.token.ContainerToken;

public class DownloadTasksActivity extends BaseActivity {

    private final static String TAG = "DownloadTasksActivity";
    private RecyclerView mRecycleView;
    private MutilAdapter mAdapter;
    private CheckableManager mCheckableManager;
    private ImageView mIconView;
    private DownloadViewModel mDownloadViewModel;
    private ProgressDialog mDeleteDialog;
    private GuidePopupWindow mRecallTip;
    private RecallTipViewModel mRecallTipViewModel;
    private View mScrollView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (Helpers.isPad()) {
            setTheme(R.style.ThemeXDayNight_Pad);
        }
        super.onCreate(savedInstanceState);
        boolean pkgInstalled = XLUtil.isPkgInstalled(this, "com.android.providers.downloads.ui");
        if (pkgInstalled) {
            try {
                Intent intent = getIntent();
                String action = intent.getAction();
                Intent newIntent = new Intent(intent);
                newIntent.setComponent(null);
                newIntent.setPackage("com.android.providers.downloads.ui");
                newIntent.setAction(action+".next");
                showActivity(newIntent);
            } catch (Exception e){
                Log.w(TAG,String.format("startActivity fail: %s",e.toString()));
            } finally {
                finish();
            }

        }
        onNavBarStyleChanged(false);
        setContentView(R.layout.activity_downloadtasks);
        mScrollView = findViewById(R.id.list_header);
        registerCoordinateScrollView(mScrollView);
        ViewModelProvider.AndroidViewModelFactory factory = ViewModelProvider.AndroidViewModelFactory.getInstance(getApplication());
        mDownloadViewModel = new ViewModelProvider(this, factory).get(DownloadViewModel.class);
        mRecallTipViewModel = new ViewModelProvider(this,factory).get(RecallTipViewModel.class);
        mIconView = new ImageView(this);
        int padding = getResources().getDimensionPixelSize(R.dimen.icon_recall_padding);
        mIconView.setPadding(padding,padding,padding,padding);
        mIconView.setOnClickListener(new NoShakeClickListener(1000) {
            @Override
            protected void onSingleClick(View v) {
                OpenMiniCard();
            }
        });

        mIconView.setImageResource(R.drawable.icon_recall);
        getAppCompatActionBar().setEndView(mIconView);

        mCheckableManager = new CheckableManager(this, new CheckableManager.CheckListener() {


            @Override
            public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                onNavBarStyleChanged(true);
                mAdapter.notifyDataSetChanged();
                return super.onCreateActionMode(mode, menu);
            }

            @Override
            public void onDestroyActionMode(ActionMode mode) {
                onNavBarStyleChanged(false);
                mAdapter.notifyDataSetChanged();
                super.onDestroyActionMode(mode);

            }

            @Override
            public void onDeleteClick(ActionMode actionMode, MenuItem menuItem) {
                mDownloadViewModel.deleteChecked(mCheckableManager.getCheckeds());
                mCheckableManager.stopActionMode();

            }

            @Override
            public void onCheckedChanged(CheckState checkState) {
                int i = mAdapter.getItems().indexOf(checkState);
                mAdapter.notifyItemChanged(i);
            }

        });

        mRecallTipViewModel.mShowTipMessage.observe(this, new Observer<Event<Boolean>>() {
            @Override
            public void onChanged(Event<Boolean> event) {
                Boolean show = event.getContentIfNotHandler();
                if (show == null) {
                    return;
                }
                if (show) {
                    showRecallTipPop();
                }
            }
        });
        mRecycleView = (RecyclerView) findViewById(R.id.recycler_view);
        mRecycleView.setItemAnimator(null);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
        mRecycleView.setLayoutManager(layoutManager);
        mRecycleView.addItemDecoration(new CardItemDecoration(this));
        RecyclerView.ItemDecoration itemDecoration = mRecycleView.getItemDecorationAt(0);
        if (itemDecoration instanceof CardItemDecoration) {
            int padd = (int) (getExtraHorizontalPadding()
                    + ContainerToken.PADDING_BASE_DP * 3 * getResources().getDisplayMetrics().density);
            ((CardItemDecoration) itemDecoration).setCardMarginStart(padd);
            ((CardItemDecoration) itemDecoration).setCardMarginEnd(padd);
        }
        mRecycleView.setItemAnimator(new CardDefaultItemAnimator());
        mAdapter = new MutilAdapter();
        mAdapter.updateGroupInfo();
        mAdapter.register(new DownloadingItemView.DownloadingItemViewFactory());
        mAdapter.register(new DownloadedItemView.DownloadedItemViewFactory());
        mAdapter.register(new EmpthItemView.EmpthItemViewFactory());
        mAdapter.register(new TitleItemView.TitleItemViewFactory(mCheckableManager));
        mAdapter.register(new DividerItemView.DividerItemViewFactory());

        mRecycleView.setAdapter(mAdapter);

        mDownloadViewModel.getViewList(this).observe(this, new Observer<List<Object>>() {
            @Override
            public void onChanged(List<Object> objects) {
                ArrayList<CheckState> list = new ArrayList<>();
                for (Object o: objects){
                   if (o instanceof CheckState) {
                       list.add((CheckState) o);
                   }
                }
                mCheckableManager.bindCheckable(list);
                mAdapter.setItems(objects);
                mAdapter.notifyDataSetChanged();
            }
        });
        mDownloadViewModel.getDeleteProgressEvent().observe(this, new Observer<Event<DeleteProgressEvent>>() {
            @Override
            public void onChanged(Event<DeleteProgressEvent> eventEvent) {
                showProgressDialog(eventEvent);
//                mCheckableManager.unbindCheckable();
            }
        });
        mDownloadViewModel.getDeleteTaskEvent().observe(this, new Observer<Event<List<DownloadTask>>>() {
            @Override
            public void onChanged(Event<List<DownloadTask>> listEvent) {
                showDeleteDialog(listEvent);
            }
        });
        mDownloadViewModel.getTaskEvent().observe(this, new Observer<Event<DownloadViewModel.DownloadTaskEvent>>() {
            @Override
            public void onChanged(Event<DownloadViewModel.DownloadTaskEvent> downloadTaskEvent) {

                DownloadViewModel.DownloadTaskEvent event = downloadTaskEvent.getContentIfNotHandler();
                if (event == null) {
                    return;
                }
                DownloadTask task = event.task;
                switch (event.type) {
                    case NET_FAIL:
                        showDisabledNetDialog(task);
                        break;
                    case OPEN_FILE_FAIL:
                        showOpenFailDialog(task);
                        break;
                    default:
                        break;
                }
            }

        });
        mDownloadViewModel.getActivityStarterEvent().observe(this, new Observer<Event<Intent>>() {
            @Override
            public void onChanged(Event<Intent> intentEvent) {
                Intent intent = intentEvent.getContentIfNotHandler();
                if (intent != null) {
                    showActivity(intent);
                }
            }
        });
        mRecallTipViewModel.mMarketDeeplinkEvent.observe(this, new Observer<Event<Intent>>() {
            @Override
            public void onChanged(Event<Intent> intentEvent) {
                Intent intent = intentEvent.getContentIfNotHandler();
                if (intent != null) {
                    showActivity(intent);
                }
            }
        });
        mRecallTipViewModel.mNetErrorEvent.observe(this, new Observer<Event<Boolean>>() {
            @Override
            public void onChanged(Event<Boolean> event) {
                Boolean show = event.getContentIfNotHandler();
                if (show != null) {
                    showNetError();
                }
            }
        });
    }
    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        int margin = getResources().getDimensionPixelSize(miuix.theme.R.dimen.miuix_theme_content_padding_start);
        setMargin(mRecycleView, margin, margin);
    }

    @Override
    protected void onResume() {
        super.onResume();
        mDownloadViewModel.reportMiddlePage(getIntent());

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unregisterCoordinateScrollView(mScrollView);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        mRecallTipViewModel.initMessageStatus();
    }

    private void showRecallTipPop() {
        if (mRecallTip == null) {
            mRecallTip = new GuidePopupWindow(this);
            mRecallTip.setArrowMode(GuidePopupWindow.ARROW_TOP_RIGHT_MODE);
            mRecallTip.setGuideText(R.string.recall_tip);
        }
        mRecallTip.show(mIconView, -mIconView.getMeasuredWidth()-10, 10, false);
    }

    private void showNetError() {
        Toast.makeText(this,R.string.toast_net_error,Toast.LENGTH_SHORT).show();
    }

    private void showActivity(Intent intent) {
        try {
            StrictModeCompcat.disableDeathOnFileUriExposure();
            startActivity(intent);
        }catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, R.string.download_no_application, Toast.LENGTH_LONG).show();
        } finally {
            StrictModeCompcat.enableDeathOnFileUriExposure();
        }
    }

    private void OpenMiniCard() {
        if (!NetworkUtils.isNetworkAvailable(getApplicationContext())) {
            showNetError();
            return;
        }
        mRecallTipViewModel.getDeeplin();

    }

    @Nullable
    private DownloadTask verityTaskNull(Event<DownloadTask> downloadTaskEvent) {
        DownloadTask task = downloadTaskEvent.getContentIfNotHandler();
        if (task == null) {
            return null;
        }
        return task;
    }

    public void showDeleteDialog(Event<List<DownloadTask>> event) {
        List<DownloadTask> list = event.getContentIfNotHandler();
        if (list == null) {
            return;
        }
        boolean showDelFileCheckBox = false;
        for (DownloadTask task : list) {
            if (task.isSuccess() && !TextUtils.isEmpty(task.localFileName)) {
                File file = new File(task.localFileName);
                if (file.exists()) {
                    showDelFileCheckBox = true;
                    break;
                }
            }
        }
        showDelFileCheckBox = ManifestUtils.isSupportOnlyDeleteDownloadInfo(this) ? showDelFileCheckBox : false;
        int size = list.size();
        String message;
        if (size > 1) {
            int resId = R.plurals.dialog_confirm_delete_downloads_message;
            message = getResources().getQuantityString(resId, size, size);
        } else {
            int resId = R.string.dialog_confirm_delete_the_download_item_message;
            message = getResources().getString(resId);
        }
        AlertDialog.Builder dialog = new AlertDialog.Builder(this, BuildUtils.isLessMiui15() ? miuix.appcompat.R.style.AlertDialog_Theme_DayNight : miuix.appcompat.R.style.AlertDialog_Theme_DayNight_Danger)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .setIconAttribute(android.R.attr.alertDialogIcon)
                .setTitle(R.string.delete_download)
                .setMessage(message)
                .setNegativeButton(android.R.string.cancel, null)
                .setPositiveButton(R.string.dialog_delete_positive,
                        new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                boolean delFile = ((AlertDialog)dialog).isChecked();
                                mDownloadViewModel.delete(list, delFile);

                            }
                        });
        if (showDelFileCheckBox) {
            dialog.setCheckBox(false,getString(R.string.dialog_confirm_delete_checkbox_message));
        }
        AlertDialog alertDialog = dialog.show();
        alertDialog.getMessageView().setTextAlignment(showDelFileCheckBox ? View.TEXT_ALIGNMENT_TEXT_START : View.TEXT_ALIGNMENT_CENTER);
    }

    public static class DeleteProgressEvent {
        public DeleteProgressEvent(boolean showProgress, int sucesseDeletedCount) {
            this.showProgress = showProgress;
            this.sucesseDeletedCount = sucesseDeletedCount;
        }

        public boolean showProgress;
        public int sucesseDeletedCount;
    }

    private void showProgressDialog(Event<DeleteProgressEvent> show) {
        DeleteProgressEvent showProgress = show.getContentIfNotHandler();
        if (showProgress == null) {
            return;
        }
        if (showProgress.showProgress) {
            mDeleteDialog = new ProgressDialog(this);
            mDeleteDialog.setMessage(getString(
                    R.string.download_info_start_deleted_tasks));
            mDeleteDialog.setIndeterminate(true);
            mDeleteDialog.setCancelable(false);
            mDeleteDialog.show();
        } else {
            if (mDeleteDialog != null && mDeleteDialog.isShowing()) {
                mDeleteDialog.cancel();
                mDeleteDialog = null;
            }
            int count = showProgress.sucesseDeletedCount;
            if (count > 0) {
                int resId = R.plurals.download_info_count_deleted_tasks;
                String tip = getResources().getQuantityString(resId, count, count);
                Toast.makeText(DownloadTasksActivity.this, tip, Toast.LENGTH_SHORT).show();
            }

        }
    }
    public static final String MI_GAME_CENTER_PACKAGE = "com.xiaomi.gamecenter";
    public static final String MI_MARKET_PACKAGE = "com.xiaomi.market";

    private void showOpenFailDialog(DownloadTask downloadTaskInfo) {
        boolean reLoad = true;
        String notiPkg = downloadTaskInfo.notificationPkgName;
        if (TextUtils.equals(MI_GAME_CENTER_PACKAGE, notiPkg) || TextUtils.equals(MI_MARKET_PACKAGE, notiPkg)) {
            reLoad = false;
        }
        showFailedDialog(this,downloadTaskInfo,
                R.string.dialog_file_missing_title,
                getString(R.string.dialog_file_missing_body), reLoad);
    }


    private void showFailedDialog(Context context,DownloadTask task, int title, String dialogBody, boolean isRedownload) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context)
                .setTitle(context.getString(title))
                .setMessage(dialogBody)
                .setNegativeButton(R.string.delete_download, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        mDownloadViewModel.delete(task,true);
                    }
                });
        if (isRedownload) {
            builder.setPositiveButton(R.string.retry_download, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    mDownloadViewModel.restartDownload(task);
                }
            });
        }
        builder.show();
    }

    private AlertDialog showDisabledNetDialog(DownloadTask task) {
        String downloadDpPkg = "com.android.providers.downloads";
        boolean wifiRestrictDownloadManager = ExtraNetwork.isWifiRestrict(this, downloadDpPkg);
        boolean mobileRestrictDownloadManager = ExtraNetwork.isMobileRestrict(this, downloadDpPkg);
        String packageName = wifiRestrictDownloadManager || mobileRestrictDownloadManager ? downloadDpPkg : task.pkgName;

        String appName = getAppNameByPkg(this, packageName);
        String msg = getResources().getString(R.string.dialog_msg_disabled_net,
                TextUtils.isEmpty(appName) ? "" : String.format("(%s)", appName));
        return new AlertDialog.Builder(this)
                .setTitle(R.string.dialog_title_disabled_net)
                .setMessage(msg)
                .setNegativeButton(R.string.dialog_btn_ok_disabled_net, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        Intent intent = new Intent("miui.intent.action.NETWORKASSISTANT_FIREWALL");
                        showActivity(intent);
                        dialog.dismiss();
                    }
                })
                .setPositiveButton(R.string.dialog_button_cancel, null)
                .show();
    }

    public static String getAppNameByPkg(Context context, String pkg) {
        String appName = null;
        try {
            PackageManager packageManager = context.getApplicationContext().getPackageManager();
            ApplicationInfo applicationInfo = packageManager.getApplicationInfo(pkg, 0);
            appName = (String) packageManager.getApplicationLabel(applicationInfo);
        } catch (PackageManager.NameNotFoundException e) {
            //Ignore exception
        }
        if (TextUtils.isEmpty(appName)) {
        }
        return appName;
    }

}
