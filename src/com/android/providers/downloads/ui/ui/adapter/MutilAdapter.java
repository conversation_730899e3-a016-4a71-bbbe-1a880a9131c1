package com.android.providers.downloads.ui.ui.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.android.providers.downloads.ui.data.ViewBean;

import java.util.ArrayList;
import java.util.List;

import miuix.recyclerview.card.CardGroupAdapter;

public class MutilAdapter<T extends ViewBean> extends CardGroupAdapter<MutilViewHolder> {

    private List<T> mList = new ArrayList<>();
    private List<Factory> mFactorys = new ArrayList<>();

    @NonNull
    @Override
    public MutilViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int viewType) {
        Factory factory = mFactorys.get(viewType);
        LayoutInflater inflater = LayoutInflater.from(viewGroup.getContext());
        return factory.create(inflater,viewGroup,viewType);
    }

    @Override
    public void onBindViewHolder(@NonNull MutilViewHolder mutilViewHolder, int i) {
        Object o = mList.get(i);
        mutilViewHolder.onBindViewHolder(i,o);
    }

    @Override
    public void setHasStableIds() {
        setHasStableIds(true);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemViewGroup(int position) {
        ViewBean value = mList.get(position);
        if (value==null) {
            return GROUP_ID_NONE;
        }
        return value.groupId;
    }

    @Override
    public int getItemViewType(int position) {
        Object o = mList.get(position);
        int index = -1;
        for (int i = 0; i < mFactorys.size(); i++) {
            Factory factory = mFactorys.get(i);
            if (factory.match(o)) {
                index = i;
                break;
            }
        }
        return index;
    }

    @Override
    public int getItemCount() {
        if (mList == null) {
            return 0;
        }
        return mList.size();
    }

    public void register(Factory factory) {
        mFactorys.add(factory);
    }

    public void setItems(List<T> objects) {
        mList.clear();
        mList.addAll(objects);
    }

    public List<T> getItems() {
        return mList;
    }
}
