package com.android.providers.downloads.ui.ui.adapter;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.android.providers.downloads.R;
import com.android.providers.downloads.ui.ui.TitleModel;
import com.android.providers.downloads.ui.ui.view.editmode.CheckableManager;

public class TitleItemView extends MutilViewHolder<TitleModel> implements View.OnClickListener {

    private final TextView mTitle;
    public final TextView mClearBtn;

    public static class TitleItemViewFactory implements Factory<TitleItemView> {
        private final CheckableManager mCheckManager;

        public TitleItemViewFactory(CheckableManager checkableManager) {
            mCheckManager = checkableManager;
        }

        @Override
        public TitleItemView create(LayoutInflater inflater, ViewGroup viewGroup, int viewType) {
            return new TitleItemView(mCheckManager,inflater.inflate(R.layout.item_title,viewGroup,false));
        }

        @Override
        public boolean match(Object value) {
            if (value instanceof TitleModel) {
                return true;
            }
            return false;
        }
    }
    @Override
    public void onBindViewHolder(int position, TitleModel data) {
        setTitle(data.getTitle());
        mClearBtn.setVisibility(data.isClearShow() && data.enable ? View.VISIBLE : View.GONE);
        boolean enable = !mChoiceMode.isActionMode() && data.enable ;
        mClearBtn.setEnabled(enable);
        if (enable) {
            mClearBtn.setTextColor(mClearBtn.getResources().getColor(R.color.text_headline_color));
        } else {
            mClearBtn.setTextColor(mClearBtn.getResources().getColor(R.color.text_disable));
        }

        mClearBtn.setTag(data);
        mClearBtn.setOnClickListener(this);
    }

    private final CheckableManager mChoiceMode;

    public TitleItemView(CheckableManager mChoiceMode,View view) {
        super(view);
        this.mChoiceMode = mChoiceMode;
        mTitle = (TextView) itemView.findViewById(R.id.title);
        mClearBtn = (TextView) itemView.findViewById(R.id.tv_clear);
    }

    public void setTitle(String title) {
        mTitle.setText(title);
    }

    @Override
    public void onClick(View v) {
        if (mChoiceMode.isActionMode()) {
            return;
        }
        TitleModel tag = (TitleModel) v.getTag();
        tag.clearAllDownloadedTask();
    }

}
