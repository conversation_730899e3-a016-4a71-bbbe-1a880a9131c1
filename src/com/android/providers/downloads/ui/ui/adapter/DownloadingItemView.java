package com.android.providers.downloads.ui.ui.adapter;

import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_DOWNLOAD_DOWNLOADING;
import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_DOWNLOAD_FAILED;
import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_DOWNLOAD_PAUSED;
import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_DOWNLOAD_SUCCESS;
import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_PENDING;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.android.providers.downloads.R;
import com.android.providers.downloads.ui.data.DownloadTask;
import com.android.providers.downloads.ui.ui.NoShakeClickListener;
import com.android.providers.downloads.ui.ui.view.DownloadProgressBar;
import com.android.providers.downloads.ui.ui.view.editmode.CheckState;
import com.android.providers.downloads.ui.utils.SizeFormatUtil;
import com.android.providers.downloads.ui.utils.SpeedUpImpl;
import com.android.providers.downloads.util.ImageLoader;
import com.xiaomi.android.app.downloadmanager.MiuiDownloadManager;

public class DownloadingItemView extends MutilViewHolder<DownloadStatus> implements View.OnLongClickListener, View.OnClickListener {

    public DownloadingItemView(@NonNull View itemView) {
        super(itemView);
        Context context = itemView.getContext();
        mTitleView = (TextView) itemView.findViewById(R.id.downloading_title);
        mActionButton = (TextView) itemView.findViewById(R.id.action_button);
        mIconView = (ImageView) itemView.findViewById(R.id.download_icon);
        mTvsize = (TextView) itemView.findViewById(R.id.size_info);
        mLine1Right = (TextView) itemView.findViewById(R.id.date_status_info_new);
        mLine2Right = (TextView) itemView.findViewById(R.id.date_status_info);
        mProgressBarH = (DownloadProgressBar) itemView.findViewById(R.id.progressbar);
        mCheckBox = (CheckBox) itemView.findViewById(android.R.id.checkbox);
        initStatusStr(context);
    }


    public static class DownloadingItemViewFactory implements Factory<DownloadingItemView> {

        @Override
        public DownloadingItemView create(LayoutInflater inflater, ViewGroup viewGroup, int viewType) {
            return new DownloadingItemView(inflater.inflate(R.layout.item_downloading,viewGroup,false));
        }

        @Override
        public boolean match(Object value) {
            if (value instanceof DownloadStatus) {
                if (!((DownloadStatus) value).isSuccess()) {
                    return true;
                }
            }
            return false;
        }

    }

    @Override
    public void onBindViewHolder(int position, DownloadStatus data) {

        changeEditMode(data.isActionEditor());
        setChecked(data.isChecked());
        setIcon(data);
        setTitle(data.getTitle());
        setDownloadSize(data);
        updateSpeedView(data, this);
        updateProgress(data.mTask, this);
        refreshDownloadsStatus(this.mActionButton, data.mTask.status, data.mTask.reason);
        itemView.setTag(data);
        itemView.setOnLongClickListener(this);
        itemView.setOnClickListener(this);
        mActionButton.setTag(data);
        mActionButton.setOnClickListener(new NoShakeClickListener() {
            @Override
            protected void onSingleClick(View v) {
                DownloadingItemView.this.onClick(v);
            }
        });
    }

    @Override
    public boolean onLongClick(View v) {
        Object tag = v.getTag();
        if (tag == null) {
            return false;
        }
        if (!(tag instanceof CheckState)) {
            return false;
        }
        CheckState state = (CheckState) tag;
        if (!state.isActionEditor()) {
            state.startActionMode();
        }
        if (tag instanceof CheckState) {
            ((CheckState) tag).toggle();
        }
        return true;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.action_button) {
            Object tag = v.getTag();
            if (tag == null) {
                return;
            }
            if (!(tag instanceof DownloadStatus)) {
                return;
            }
            DownloadStatus task = (DownloadStatus) tag;
            task.onTaskClick();
        } else {
            Object tag = v.getTag();
            if (tag == null) {
                return;
            }
            if (tag instanceof CheckState) {
                ((CheckState) tag).toggle();
            }
        }
    }

    private final TextView mTitleView;
    private final ImageView mIconView;
    private final TextView mTvsize;
    private final TextView mLine1Right;
    private final TextView mLine2Right;
    private final DownloadProgressBar mProgressBarH;
    private final TextView mActionButton;
    private final CheckBox mCheckBox;
    private String mDownloadErrorInsufficientSpace;
    private String mDownloadFileNotExists;
    private String mDownloadError;
    private String mDownloadSuccess;
    private String mDownloadWaitConnect;
    private String mDownloadRuning;
    private String mDownloadPausedByApp;
    private String mDownloadInsufficientSpace;
    private String mDownloadQueuedForWifi;
    private String mDownloadWaitingForNetwork;
    private String mDownloadWaiting2Retry;
    private String mDownloadPausedUnknown;
    private String mStatusStrPaused;
    private String mStatusStrContinue;
    private String mStatusStrFail;

    private void initStatusStr(Context context) {
        mDownloadErrorInsufficientSpace = context.getString(R.string.download_error_insufficient_space);
        mDownloadFileNotExists = context.getString(R.string.download_error_download_file_not_exists);
        mDownloadError = context.getString(R.string.download_error);
        mDownloadSuccess = context.getString(R.string.download_success);
        mDownloadWaitConnect = context.getString(R.string.download_wait_connect);
        mDownloadRuning = context.getString(R.string.download_running);
        mDownloadPausedByApp = context.getString(R.string.paused_by_app);
        mDownloadInsufficientSpace = context.getString(R.string.paused_insufficient_space);
        mDownloadQueuedForWifi = context.getString(R.string.paused_queued_for_wifi);
        mDownloadWaitingForNetwork = context.getString(R.string.paused_waiting_for_network);
        mDownloadWaiting2Retry = context.getString(R.string.paused_waiting_to_retry);
        mDownloadPausedUnknown = context.getString(R.string.paused_unknown);

        mStatusStrPaused = context.getString(R.string.download_status_paused);
        mStatusStrContinue = context.getString(R.string.download_status_continue);
        mStatusStrFail = context.getString(R.string.download_status_fail);
    }

    public void setTitle(String title) {
        mTitleView.setText(title);
    }

    public void setIcon(DownloadStatus task) {
        ImageLoader.loadTaskIcon(mIconView, task);
    }

    public void setDownloadSize(DownloadStatus task) {
        mTvsize.setText(task.showSize);
    }

    private void updateTextAppearance(TextView textView, String str) {
        if (textView == null) {
            return;
        }
        textView.setVisibility(View.VISIBLE);
        textView.setText(str);
    }

    private void updateTextAppearance(TextView textView, String str, int color) {
        if (textView == null) {
            return;
        }
        textView.setVisibility(View.VISIBLE);
        textView.setText(str);
        textView.setTextColor(color);
    }

    private void updateSpeedView(DownloadStatus downloadStatus, DownloadingItemView downloadViewholder) {
        DownloadTask DownloadStatusItem = downloadStatus.mTask;

        TextView line1Right = downloadViewholder.mLine1Right;
        TextView line2Right = downloadViewholder.mLine2Right;
        Context context = line1Right.getContext();
        int status = DownloadStatusItem.status;
        int reason = DownloadStatusItem.reason;
        if (MiuiDownloadManager.STATUS_SUCCESSFUL == status) {
            line1Right.setVisibility(View.GONE);
            line2Right.setVisibility(View.GONE);
        } else if (MiuiDownloadManager.STATUS_PENDING == status) {
            line1Right.setVisibility(View.GONE);
            String info = context.getString(R.string.download_wait_connect);
            updateTextAppearance(line2Right, info);
        } else if (MiuiDownloadManager.STATUS_RUNNING == status) {

            long currentSpeed = DownloadStatusItem.currentDownloadSpeed;
            long vipSpeed = DownloadStatusItem.vipSpeed;
            long p2sSpeed = DownloadStatusItem.p2spSpeed;
            long currentBytes = DownloadStatusItem.currentBytes;
            long upSpeed = 0;
            if (currentSpeed == 0 && currentBytes == 0) {
                line1Right.setVisibility(View.GONE);
                String info = context.getString(R.string.download_wait_connect);
                updateTextAppearance(line2Right, info);
            } else {
                // have show vip speed before
                if (vipSpeed > 0) {
                    upSpeed = (vipSpeed + p2sSpeed);
                } else {
                    smoothSpeed(currentSpeed, DownloadStatusItem);
                }
                if (currentSpeed >= 0) {
                    updateTextAppearance(line2Right, downloadStatus.showSpeed);
                } else {
                    line2Right.setVisibility(View.GONE);
                }
                if (upSpeed > 0) {
                    String speedStr = getSpeedString(line1Right.getContext(), upSpeed, true);
                } else {
                    line1Right.setVisibility(View.GONE);
                }
            }
        } else {
            line1Right.setVisibility(View.GONE);
            String info = getStatusString(status, reason);
            if (status == MiuiDownloadManager.STATUS_FAILED) {
            } else {
                updateTextAppearance(line2Right, info);
            }
        }
    }

    private String getSpeedString(Context context, long speed, boolean vipflag) {
        String speedtext = SizeFormatUtil.formatFileSize(context, speed) + "/s";
        if (vipflag) {
            speedtext = "+" + speedtext;
        }
        return speedtext;
    }

    private long smoothSpeed(long currentSpeed, DownloadTask task) {
        long upSpeed = 0;
        if (SpeedUpImpl.isInVipMode() || task.isSpeedUping()) {
            //为了防止vip加速过程中没有速度
            upSpeed = currentSpeed > 0 ? (1 + currentSpeed % 9) * 1024 : 0;
        }
        return upSpeed;
    }

    private void updateProgress(DownloadTask DownloadStatusItem, DownloadingItemView downloadViewholder) {
        if (MiuiDownloadManager.STATUS_SUCCESSFUL == DownloadStatusItem.status) {
            return;
        }
        if (DownloadStatusItem.isM3u8Task()) {
            int percent = DownloadStatusItem.percent;
            float progressAmount = percent / 100f;
            downloadViewholder.mProgressBarH.setProgress(progressAmount);
            return;
        }
        // set progress
        long totalBytes = DownloadStatusItem.totalBytes;
        if (totalBytes < 0) {
            totalBytes = 0L;
        }

        long currentBytes = DownloadStatusItem.currentBytes;
        float progressAmount = 0;
        if (totalBytes > 0 && currentBytes > 0) {
            progressAmount = (float) (currentBytes * 100 / totalBytes) / 100f;
        }
        //防止currentBytes过小而没有进度
        if (progressAmount < 0.01f && currentBytes > 0) {
            progressAmount = 0.01f;
        }
        downloadViewholder.mProgressBarH.setProgress(progressAmount);
    }

    private String getStatusString(int status, int reason) {
        switch (status) {
            case STATUS_CODE_DOWNLOAD_FAILED:
                switch (reason) {
                    case MiuiDownloadManager.ERROR_INSUFFICIENT_SPACE:
                        return mDownloadErrorInsufficientSpace;
                    case MiuiDownloadManager.ERROR_DOWNLOAD_FILE_NOT_EXISTS:
                        return mDownloadFileNotExists;
                    default:
                        return mDownloadError;
                }
            case STATUS_CODE_DOWNLOAD_SUCCESS:
                return mDownloadSuccess;
            case STATUS_CODE_PENDING:
                return mDownloadWaitConnect;
            case STATUS_CODE_DOWNLOAD_DOWNLOADING:
                return mDownloadRuning;
            case STATUS_CODE_DOWNLOAD_PAUSED:
                switch (reason) {
                    case MiuiDownloadManager.PAUSED_BY_APP:
                        return mDownloadPausedByApp;
                    case MiuiDownloadManager.PAUSE_INSUFFICIENT_SPACE:
                        return mDownloadInsufficientSpace;
                    case MiuiDownloadManager.PAUSED_QUEUED_FOR_WIFI:
                        return mDownloadQueuedForWifi;
                    case MiuiDownloadManager.PAUSED_WAITING_FOR_NETWORK:
                        return mDownloadWaitingForNetwork;
                    case MiuiDownloadManager.PAUSED_WAITING_TO_RETRY:
                        return mDownloadWaiting2Retry;
                    default:
                        return mDownloadPausedUnknown;
                }
            default:
                break;
        }
        return "";
    }

    private void refreshDownloadsStatus(TextView statusView, int status, int reason) {
        String statusStr = "";
        switch (status) {
            case STATUS_CODE_DOWNLOAD_FAILED:
                statusStr = mStatusStrFail;
                break;
            case STATUS_CODE_PENDING:
            case STATUS_CODE_DOWNLOAD_DOWNLOADING:
                statusStr = mStatusStrPaused;
                break;
            case STATUS_CODE_DOWNLOAD_PAUSED:
                if (reason == MiuiDownloadManager.PAUSED_BY_APP) {
                    statusStr = mStatusStrContinue;
                } else if (reason == MiuiDownloadManager.PAUSED_WAITING_TO_RETRY) {
                    statusStr = mStatusStrFail;
                } else {
                    statusStr = mStatusStrContinue;
                }
                break;
            default:
                break;
        }
        CharSequence oldText = statusView.getText();
        if (!TextUtils.equals(oldText, statusStr)) {
            statusView.setText(statusStr);
        }
    }

    public void changeEditMode(boolean editMode) {
        if (editMode) {
            mActionButton.setVisibility(View.GONE);
            mCheckBox.setVisibility(View.VISIBLE);
        } else {
            mActionButton.setVisibility(View.VISIBLE);
            mCheckBox.setVisibility(View.GONE);
        }
    }

    public void setChecked(boolean checked) {
        if (mCheckBox.isChecked() != checked) {
            mCheckBox.setChecked(checked);
        }
    }
}
