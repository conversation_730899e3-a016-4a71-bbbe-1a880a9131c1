package com.android.providers.downloads.ui.ui.adapter;

import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_DOWNLOAD_DOWNLOADING;
import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_DOWNLOAD_FAILED;
import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_DOWNLOAD_PAUSED;
import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_DOWNLOAD_SUCCESS;
import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_PENDING;
import static com.android.providers.downloads.xunlei.speedup.XLSpeedUpManager.TASK_SPEEDUP_RUNNING;

import android.content.Context;
import android.util.Log;

import com.android.providers.downloads.R;
import com.android.providers.downloads.ui.data.DownloadTask;
import com.android.providers.downloads.ui.ui.DownloadViewModel;
import com.android.providers.downloads.ui.ui.view.editmode.CheckState;
import com.android.providers.downloads.ui.utils.DateUtil;
import com.android.providers.downloads.ui.utils.SizeFormatUtil;
import com.xiaomi.android.app.downloadmanager.MiuiDownloadManager;

public class DownloadStatus extends CheckState {
    public final DownloadTask mTask;
    private final DownloadViewModel mDownloadViewModel;
    public String showSize;
    public String showSpeed;

    public DownloadStatus(DownloadTask task,DownloadViewModel downloadViewModel) {
        mTask = task;
        mDownloadViewModel = downloadViewModel;
        showSize = getDownloadSize(downloadViewModel.getApplication());
        showSpeed = getSpeedStr(downloadViewModel.getApplication());
    }

    public DownloadTask getTask() {
        return mTask;
    }

    @Override
    public int getCheckId() {
        if (mTask == null) {
            return -1;
        }
        return mTask.downloadId;
    }

    public String getTitle() {
        if (mTask == null) {
            return "";
        }
        return mTask.title;
    }

    public String getLocalFileName() {
       if (mTask == null) {
           return "";
       }
       return mTask.localFileName;
    }

    public String getIconUrl() {
        if (mTask == null) {
            return "";
        }
        return mTask.iconUrl;
    }

    public boolean isSuccess() {
        if (mTask == null) {
            return false;
        }
        return mTask.status == STATUS_CODE_DOWNLOAD_SUCCESS;
    }

    public boolean isFail() {
        if (mTask == null) {
            return false;
        }
        return mTask.status == STATUS_CODE_DOWNLOAD_FAILED;
    }

    public boolean isPending() {
        if (mTask == null) {
            return false;
        }
        return mTask.status == STATUS_CODE_PENDING;
    }

    public boolean isDownloading() {
        if (mTask == null) {
            return false;
        }
        return mTask.status == STATUS_CODE_DOWNLOAD_DOWNLOADING;
    }

    public boolean isPaused() {
        if (mTask == null) {
            return false;
        }
        return mTask.status == STATUS_CODE_DOWNLOAD_PAUSED;
    }

    public boolean isPausedForWifi() {
        if (mTask == null) {
            return false;
        }
        return mTask.reason == MiuiDownloadManager.PAUSED_QUEUED_FOR_WIFI;
    }

    public boolean isSpeedUping() {
        if (mTask == null) {
            return false;
        }
        return mTask.vipSpeedUpStatus == TASK_SPEEDUP_RUNNING;
    }

    public String getDownloadSize(Context context) {
        if (mTask == null) {
            return "";
        }
        int status = mTask.status;
        long currentBytes = mTask.currentBytes;
        long totalBytes = mTask.totalBytes;
        String sizeText = "";
        if (totalBytes >= 0) {
            if (status == STATUS_CODE_DOWNLOAD_SUCCESS) {
                try {
                    sizeText = SizeFormatUtil.formatFileSize(context, totalBytes);
                    String info = DateUtil.getDateString(context, mTask.lastModifyTime);
                    sizeText = sizeText + " | " + info;
                } catch (Exception e) {
                    //Ignore exception
                    Log.getStackTraceString(e);
                }
            } else if (status != -1) {
                if (totalBytes > 0) {
                    //sizeText = "10/100M";
                    sizeText = SizeFormatUtil.formatFileSize(context, currentBytes) + "/" + SizeFormatUtil.formatFileSize(context, totalBytes);
                } else {
                    String test = context.getString(R.string.download_filesize_unknown);
                    sizeText = SizeFormatUtil.formatFileSize(context, currentBytes) + "/" + test;
                }
            }
        } else {
            String test = context.getString(R.string.download_filesize_unknown);
            sizeText = SizeFormatUtil.formatFileSize(context, currentBytes) + "/" + test;
        }
        return sizeText;
    }

    public String getSpeedStr(Context context){
        if (mTask == null) {
            return "";
        }
        return SizeFormatUtil.formatFileSize(context, mTask.currentDownloadSpeed) + "/s";
    }

    public void onTaskClick() {
        if (mDownloadViewModel != null) {
            mDownloadViewModel.onTaskButtonClick(mTask);
        }
    }
}
