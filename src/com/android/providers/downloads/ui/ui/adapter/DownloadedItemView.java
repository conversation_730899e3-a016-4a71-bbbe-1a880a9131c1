package com.android.providers.downloads.ui.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import com.android.providers.downloads.R;
import com.android.providers.downloads.ui.data.DownloadTask;
import com.android.providers.downloads.ui.ui.NoShakeClickListener;
import com.android.providers.downloads.ui.ui.view.editmode.CheckState;
import com.android.providers.downloads.util.ImageLoader;


public class DownloadedItemView extends MutilViewHolder<DownloadStatus> implements View.OnLongClickListener, View.OnClickListener {



    public static class DownloadedItemViewFactory implements Factory<DownloadedItemView> {

        @Override
        public DownloadedItemView create(LayoutInflater inflater, ViewGroup viewGroup, int viewType) {
            return new DownloadedItemView(inflater.inflate(R.layout.item_downloaded, viewGroup, false));
        }

        @Override
        public boolean match(Object value) {
            if (value instanceof DownloadStatus) {
                if (((DownloadStatus) value).isSuccess()) {
                    return true;
                }
            }
            return false;
        }
    }

    public DownloadedItemView(View view) {
        super(view);
        mIvApp = (ImageView) itemView.findViewById(R.id.download_icon);
        mTvdownloadedTitle = (TextView) itemView.findViewById(R.id.downloaded_title);
        mTvsize = (TextView) itemView.findViewById(R.id.size_info);
        mLine2Right = (TextView) itemView.findViewById(R.id.date_status_info);
        mCheckBox = (CheckBox) itemView.findViewById(android.R.id.checkbox);
        mActionBtn = itemView.findViewById(R.id.action_button);
    }

    @Override
    public void onBindViewHolder(int position, DownloadStatus data) {

        changeEditMode(data.isActionEditor());
        setChecked(data.isChecked());
        ImageLoader.loadTaskIcon(this.mIvApp, data);
        mTvdownloadedTitle.setText(data.getTitle());
        setDownloadSize(data);
        itemView.setTag(data);
        itemView.setOnLongClickListener(this);
        itemView.setOnClickListener(this);
        mActionBtn.setTag(data);
        mActionBtn.setOnClickListener(new NoShakeClickListener() {
            @Override
            protected void onSingleClick(View v) {
                DownloadedItemView.this.onClick(v);
            }
        });
    }

    @Override
    public boolean onLongClick(View v) {
        Object tag = v.getTag();
        if (tag == null) {
            return false;
        }
        if (!(tag instanceof CheckState)) {
            return false;
        }
        CheckState state = (CheckState) tag;
        if (!state.isActionEditor()) {
            state.startActionMode();
        }
        if (tag instanceof CheckState) {
            ((CheckState) tag).toggle();
        }
        return true;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.action_button) {
            Object tag = v.getTag();
            if (tag == null) {
                return;
            }
            if (!(tag instanceof DownloadStatus)) {
                return;
            }
            DownloadStatus task = (DownloadStatus) tag;
            task.onTaskClick();
        } else {
            Object tag = v.getTag();
            if (tag == null) {
                return;
            }
            if (tag instanceof CheckState) {
                ((CheckState) tag).toggle();
            }
        }
    }
    public final ImageView mIvApp;
    private final CheckBox mCheckBox;
    public final View mActionBtn;
    public TextView mTvdownloadedTitle;
    public final TextView mTvsize;
    public final TextView mLine2Right;


    public void setDownloadSize(DownloadStatus task) {
        mTvsize.setText(task.showSize);
    }

    public void changeEditMode(boolean editMode) {
        if (editMode) {
            mActionBtn.setVisibility(View.GONE);
            mCheckBox.setVisibility(View.VISIBLE);
        } else {
            mActionBtn.setVisibility(View.VISIBLE);
            mCheckBox.setVisibility(View.GONE);
        }
    }

    public void setChecked(boolean checked) {
        if (mCheckBox.isChecked() != checked) {
            mCheckBox.setChecked(checked);
        }
    }

}
