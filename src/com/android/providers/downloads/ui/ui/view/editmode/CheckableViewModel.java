package com.android.providers.downloads.ui.ui.view.editmode;

import android.app.Application;
import android.util.SparseArray;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.MutableLiveData;

import com.android.providers.downloads.ui.ui.Event;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class CheckableViewModel extends AndroidViewModel {

    private SparseArray<CheckState> mCheckedIds = new SparseArray<>();
    private MutableLiveData<Event<CheckState>> changeCheckEvent = new MutableLiveData();
    private SparseArray<CheckState> mAllCheckedIds = new SparseArray<>();
    private boolean isAllCheckState = false;
    public MutableLiveData<Event<Boolean>> actoionMode = new MutableLiveData<>();
    private boolean mIsEditorMode = false;

    public CheckableViewModel(@NonNull Application application) {
        super(application);
    }

    public void setChecked(CheckState checkState) {
        boolean change = false;
        CheckState oldChecked = mCheckedIds.get(checkState.getCheckId());
        if (oldChecked == null && checkState.isChecked()) {
            mCheckedIds.put(checkState.getCheckId(), checkState);
            change = true;
        } else if (oldChecked != null && !checkState.isChecked()) {
            mCheckedIds.remove(checkState.getCheckId());
            change = true;
        }
        if (change) {
            changeCheckEvent.setValue(new Event<>(checkState));
        }
    }

    public void bindCheckable(List<CheckState> list) {
        unBindCheckable();
        if (list == null) {
            return;
        }

        Set<Integer> checkIds = new HashSet<Integer>();
        for (int i = 0; i < mCheckedIds.size(); i++) {
            checkIds.add(mCheckedIds.keyAt(i));
        }
        mCheckedIds.clear();
        for (CheckState state : list) {
            bindCheckable(checkIds,state);
        }

    }

    private void bindCheckable(Set<Integer> mCheckedList,CheckState checkState) {
        if (checkState != null) {
            checkState.bindCheckableManager(this);
            int id = checkState.getCheckId();
            CheckState state = mAllCheckedIds.get(id);
            if (state == null) {
                mAllCheckedIds.put(checkState.getCheckId(), checkState);
            }
            if (isAllCheckState) {
                checkState.setChecked(true);
                mCheckedIds.put(id, checkState);
            } else {
                boolean checked = mCheckedList.contains(id);
                if (checked) {
                    checkState.setChecked(true);
                    mCheckedIds.put(id, checkState);
                } else {
                    checkState.setChecked(false);
                }
            }
        }
    }

    public MutableLiveData<Event<CheckState>> getChangeCheckEvent() {
        return changeCheckEvent;
    }

    public SparseArray<CheckState> getCheckeds() {
        return mCheckedIds;
    }

    public SparseArray<CheckState> getAllCheckedIds() {
        return mAllCheckedIds;
    }

    public void setAllChecked() {
        isAllCheckState = !isAllCheckState;
        for (int i = 0; i < mAllCheckedIds.size(); i++) {
            CheckState checkState = mAllCheckedIds.valueAt(i);
            checkState.setChecked(isAllCheckState);
            if (isAllCheckState) {
                mCheckedIds.put(mAllCheckedIds.keyAt(i), checkState);
            } else {
                mCheckedIds.remove(mAllCheckedIds.keyAt(i));
            }
        }
    }

    public void reset() {
        isAllCheckState = false;
        mCheckedIds.clear();
        for (int i = 0; i < mAllCheckedIds.size(); i++) {
            mAllCheckedIds.valueAt(i).setChecked(false);
        }
    }

    public void unBindCheckable() {
        for (int i = 0; i < mAllCheckedIds.size(); i++) {
            mAllCheckedIds.valueAt(i).unBindCheckableManager();
        }
        mAllCheckedIds.clear();
    }

    public void startActionMode() {
        actoionMode.setValue(new Event<>(true));
    }

    public void onExitActionMode() {
        mIsEditorMode = false;
    }

    public boolean isActionEditor() {
        return mIsEditorMode;
    }

    public void onEnterActionMode() {
        mIsEditorMode = true;
    }
}