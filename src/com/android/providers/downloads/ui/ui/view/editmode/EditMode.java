package com.android.providers.downloads.ui.ui.view.editmode;


import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.view.ActionMode;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;

import com.android.providers.downloads.R;

import miuix.view.EditActionMode;

public class <PERSON><PERSON><PERSON> implements ActionMode.Callback {
    private ActionMode mActionMode;

    protected Context mContext;
    private MultiChoiceModeListener mMultiChoiceModeListener;
    private MenuItem mDeleteItem;

    protected EditMode(Context context) {
        mContext = context;
    }

    public void setMultiChoiceModeListener(MultiChoiceModeListener multiChoiceModeListener) {
        mMultiChoiceModeListener = multiChoiceModeListener;
    }

    @Override
    public boolean onCreateActionMode(ActionMode mode, Menu menu) {
        enterEditMode(mode,menu);
        if (mMultiChoiceModeListener != null) {
            mMultiChoiceModeListener.onCreateActionMode(mode,menu);
        }
        return true;
    }

    @Override
    public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
        if (mMultiChoiceModeListener != null) {
            mMultiChoiceModeListener.onPrepareActionMode(mode,menu);
        }
        return false;
    }

    @Override
    public boolean onActionItemClicked(ActionMode actionMode, MenuItem menuItem) {
        boolean handle = false;
        int itemId = menuItem.getItemId();
        if (itemId == android.R.id.button1) {
            exitEditMode(actionMode);
            handle = true;
            if (mMultiChoiceModeListener != null) {
                mMultiChoiceModeListener.onCloseClick(actionMode,menuItem);
            }
        } else if (itemId == android.R.id.button2) {
            if (mMultiChoiceModeListener != null) {
                mMultiChoiceModeListener.onAllClick(actionMode,menuItem);
            }
            handle = true;
        } else if (itemId == R.id.delete_download) {
            if (mMultiChoiceModeListener != null) {
                mMultiChoiceModeListener.onDeleteClick(actionMode,menuItem);
            }
            handle = true;
        }
        return handle;
    }

    @Override
    public void onDestroyActionMode(ActionMode mode) {
        if (mMultiChoiceModeListener != null) {
            mMultiChoiceModeListener.onDestroyActionMode(mode);
        }
        exitEditMode(mode);

    }

    protected void enterEditMode(ActionMode mode, Menu menu) {
        if (mode == null) {
            return;
        }
        mActionMode = mode;
        updateChoiseState(0,0);
        updateCancelButton();
        MenuInflater inflater = ((Activity)mContext).getMenuInflater();
        inflater.inflate(R.menu.download_edit_mode_menu, menu);
        mDeleteItem = menu.findItem(R.id.delete_download);
        mDeleteItem.setEnabled(false);
    }

    public boolean isEditMode() {
        return mActionMode != null;
    }

    private void exitEditMode(ActionMode actionMode) {
        if (actionMode == null) {
            return;
        }
        actionMode.finish();
        mActionMode = null;
    }

    public void exitEditMode() {
        exitEditMode(mActionMode);
    }

    public boolean isActionMode() {
        return mActionMode != null;
    }

    public void updateChoiseState(int choiseCount,int allCount) {
        ActionMode actionMode = mActionMode;
        if (actionMode == null) {
            return;
        }
        final Resources r = mContext.getResources();
        String title;
        if (choiseCount == 0) {
            title = r.getString(R.string.miuix_appcompat_select_item);
        } else {
            final String format = r.getQuantityString(R.plurals.miuix_appcompat_items_selected, choiseCount);
            title = String.format(format, choiseCount);
        }
        actionMode.setTitle(title);

        updateSelButton(choiseCount,allCount);
        if (mDeleteItem != null) {
            if(choiseCount > 0) {
                mDeleteItem.setEnabled(true);
            } else {
               mDeleteItem.setEnabled(false);
            }
        }

    }

    private void updateSelButton(int choiseCount, int allCount) {
        boolean allChecked = choiseCount >= allCount;
        int drawableId = !allChecked ?
                R.drawable.action_mode_title_button_select_all
                : R.drawable.action_mode_title_button_deselect_all;
        EditActionMode actionMode = (EditActionMode) mActionMode;
        if (actionMode != null) {
            actionMode.setButton(android.R.id.button2, "", drawableId);
        }
    }

    private void updateCancelButton() {
        EditActionMode actionMode = (EditActionMode) mActionMode;
        if (actionMode != null) {
            int drawableId = R.drawable.action_mode_title_button_cancel;
            actionMode.setButton(android.R.id.button1, "", drawableId);
        }
    }

    public interface MultiChoiceModeListener extends ActionMode.Callback {
        void onCloseClick(ActionMode actionMode, MenuItem menuItem);
        void onAllClick(ActionMode mode, MenuItem menuItem);
        void onDeleteClick(ActionMode actionMode, MenuItem menuItem);
    }
}