package com.android.providers.downloads.ui.ui.view.editmode;

import static androidx.lifecycle.Lifecycle.Event.ON_STOP;

import android.util.SparseArray;
import android.view.ActionMode;
import android.view.Menu;
import android.view.MenuItem;

import androidx.activity.ComponentActivity;
import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.android.providers.downloads.ui.ui.Event;

import java.util.List;

public class CheckableManager implements EditMode.MultiChoiceModeListener {

    private final EditMode mEditMode;
    private final CheckableViewModel mCheckableViewModel;
    private final ComponentActivity mActivity;
    private CheckListener mCheckListener;

    public CheckableManager(ComponentActivity activity, CheckListener listener) {
        mCheckListener = listener;
        mActivity = activity;
        mActivity.getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
               if (event == ON_STOP) {
                   stopActionMode();
               }
            }
        });
        mEditMode = new EditMode(activity);
        mEditMode.setMultiChoiceModeListener(this);
        ViewModelProvider.AndroidViewModelFactory factory = ViewModelProvider.AndroidViewModelFactory.getInstance(activity.getApplication());
        mCheckableViewModel = new ViewModelProvider(activity, factory).get(CheckableViewModel.class);
        mCheckableViewModel.getChangeCheckEvent().observe(activity, new Observer<Event<CheckState>>() {
            @Override
            public void onChanged(Event<CheckState> event) {
                CheckState state = event.getContentIfNotHandler();
                if (mCheckListener != null && state != null) {
                    mCheckListener.onCheckedChanged(state);
                }
                updateState();
            }
        });
        mCheckableViewModel.actoionMode.observe(activity, new Observer<Event<Boolean>>() {
            @Override
            public void onChanged(Event<Boolean> event) {
                Boolean start = event.getContentIfNotHandler();
                if (start == null) {
                    return;
                }
                if (start) {
                    startActionMode();
                }
            }
        });
    }

    public void bindCheckable(List<CheckState> list) {
        mCheckableViewModel.bindCheckable(list);
        updateState();
    }

    public SparseArray<CheckState> getCheckeds() {
        return mCheckableViewModel.getCheckeds();
    }

    public void startActionMode() {
        mActivity.startActionMode(mEditMode);
    }

    public void stopActionMode() {
        mEditMode.exitEditMode();
    }

    public boolean isActionMode() {
        return mCheckableViewModel.isActionEditor();
    }

    public void updateState() {
        mEditMode.updateChoiseState(mCheckableViewModel.getCheckeds().size(), mCheckableViewModel.getAllCheckedIds().size());
    }

    @Override
    public void onCloseClick(ActionMode actionMode, MenuItem menuItem) {
        if (mCheckListener != null) {
            mCheckListener.onCloseClick(actionMode, menuItem);
        }
    }

    @Override
    public void onAllClick(ActionMode mode, MenuItem menuItem) {
        mCheckableViewModel.setAllChecked();
    }

    @Override
    public void onDeleteClick(ActionMode actionMode, MenuItem menuItem) {

        if (mCheckListener != null) {
            mCheckListener.onDeleteClick(actionMode, menuItem);
        }
    }

    @Override
    public boolean onCreateActionMode(ActionMode mode, Menu menu) {
        mCheckableViewModel.onEnterActionMode();
        if (mCheckListener != null) {
            mCheckListener.onCreateActionMode(mode,menu);
        }

        return true;
    }

    @Override
    public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
        if (mCheckListener != null) {
            mCheckListener.onPrepareActionMode(mode,menu);
        }
        return false;
    }

    @Override
    public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
        if (mCheckListener != null) {
            mCheckListener.onActionItemClicked(mode,item);
        }
        return false;
    }

    @Override
    public void onDestroyActionMode(ActionMode mode) {
        if (mCheckListener != null) {
            mCheckListener.onDestroyActionMode(mode);
        }
        mCheckableViewModel.reset();
        mCheckableViewModel.onExitActionMode();
    }

    public abstract static class CheckListener implements EditMode.MultiChoiceModeListener {
        public abstract void onCheckedChanged(CheckState checkState);

        @Override
        public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
            return false;
        }

        @Override
        public boolean onCreateActionMode(ActionMode mode, Menu menu) {
            return false;
        }

        @Override
        public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
            return false;
        }

        @Override
        public void onDestroyActionMode(ActionMode mode) {

        }

        @Override
        public void onAllClick(ActionMode mode, MenuItem menuItem) {

        }

        @Override
        public void onCloseClick(ActionMode actionMode, MenuItem menuItem) {

        }
    }
}