package com.android.providers.downloads.ui.ui;

import android.util.Log;
import android.view.View;

import miuix.appcompat.app.ActionBar;
import miuix.internal.util.ReflectUtil;

public class ActionBarUtils {

    private static final String TAG = "ActionBarUtils";
    public static final int STATE_COLLAPSE = 0;//折叠状态
    public static final int STATE_EXPAND = 1;//展开状态

    public static boolean setActionBarEndView(ActionBar actionBar, View view) {
        try {
            ReflectUtil.callObjectMethod(actionBar, "setEndView", new Class[]{View.class}, view);
            return true;
        } catch (Exception e) {
            Log.w(TAG, "reflect error when " + (e != null ? e.getMessage() : ""));
        }
        return false;
    }

    public static boolean setResizable(ActionBar actionBar, boolean isResize) {
        try {
            ReflectUtil.callObjectMethod(actionBar, "setResizable", new Class[]{boolean.class}, isResize);
            return true;
        } catch (Exception e) {
            Log.w(TAG, "reflect error when " + (e != null ? e.getMessage() : ""));
        }
        return false;
    }

    public static void setExpandState(ActionBar actionBar, int state) {
        try {
            ReflectUtil.callObjectMethod(actionBar, "setExpandState", new Class[]{int.class}, state);
        } catch (Exception e) {
            Log.w(TAG, "reflect error when " + (e != null ? e.getMessage() : ""));
        }
    }
}

