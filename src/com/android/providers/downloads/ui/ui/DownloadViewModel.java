package com.android.providers.downloads.ui.ui;


import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_DOWNLOAD_SUCCESS;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.util.SparseArray;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.android.providers.downloads.ui.data.DownloadTask;
import com.android.providers.downloads.ui.data.DownloadTaskChange;
import com.android.providers.downloads.ui.data.DownloadsRepository;
import com.android.providers.downloads.ui.data.MarketReleaseUtils;
import com.android.providers.downloads.ui.domain.DownloadUseCase;
import com.android.providers.downloads.ui.domain.TraceReportUi;
import com.android.providers.downloads.ui.ui.adapter.DownloadStatus;
import com.android.providers.downloads.ui.ui.download.DownloadTaskHandler;
import com.android.providers.downloads.ui.ui.download.DownloadingTaskHandler;
import com.android.providers.downloads.ui.ui.download.FailTaskHandler;
import com.android.providers.downloads.ui.ui.download.PauseTaskHandler;
import com.android.providers.downloads.ui.ui.download.PendingTaskHandler;
import com.android.providers.downloads.ui.ui.download.SuccessTaskHandler;
import com.android.providers.downloads.ui.ui.view.editmode.CheckState;

import java.util.ArrayList;
import java.util.List;

public class DownloadViewModel extends BaseViewModel {

    public void reportMiddlePage(Intent intent) {
       asyncWork(new Runnable() {
           @Override
           public void run() {
               TraceReportUi.reportMiddlePage(IntentFromUtils.getSourceFromIntent(intent));
           }
       });
    }

    public static class DownloadTaskEvent {
        public static enum Events {
            OPEN_FILE_FAIL,
            NET_FAIL,
            OPEN_FILE
        }
        public Events type;
        public DownloadTask task;

        public DownloadTaskEvent(Events type, DownloadTask task) {
            this.type = type;
            this.task = task;
        }
    }
    private MutableLiveData<List<Object>> mList;
    private DownloadsRepository downloadsRepository;
    private DownloadTaskChange downloadTaskChange;
    private DownloadListStrategy downloadListStrategy = new DownloadListStrategy();
    private MutableLiveData<Event<DownloadTasksActivity.DeleteProgressEvent>> mDeleteProgressEvent = new MutableLiveData<>();
    private MutableLiveData<Event<List<DownloadTask>>> mDeleteTaskEvent = new MutableLiveData<Event<List<DownloadTask>>>();
    private DownloadUseCase mDownloadUseCase = new DownloadUseCase(MarketReleaseUtils.createMiuiDownloadManager(getApplication()));
    private MutableLiveData<Event<DownloadTaskEvent>> mTaskEvent = new MutableLiveData();
    private MutableLiveData<Event<Intent>> mActivityStarterEvent = new MutableLiveData<>();
    private DownloadTaskHandler mDownloadTaskHandler;
    public DownloadViewModel(@NonNull Application application) {
        super(application);
        PendingTaskHandler pendingTaskHandler = new PendingTaskHandler(this, mDownloadUseCase, null);
        FailTaskHandler failTaskHandler = new FailTaskHandler(this, mDownloadUseCase, pendingTaskHandler);
        PauseTaskHandler pauseTaskHandler = new PauseTaskHandler(this, mDownloadUseCase, failTaskHandler);
        SuccessTaskHandler successTaskHandler = new SuccessTaskHandler(this, mDownloadUseCase, pauseTaskHandler);
        mDownloadTaskHandler = new DownloadingTaskHandler(this,mDownloadUseCase, successTaskHandler);
        downloadsRepository = new DownloadsRepository(application);
    }

    public MutableLiveData<Event<Intent>> getActivityStarterEvent() {
        return mActivityStarterEvent;
    }

    public void startActivity(Intent intent) {
        mActivityStarterEvent.postValue(new Event<>(intent));
    }

    public MutableLiveData<Event<DownloadTaskEvent>> getTaskEvent() {
        return mTaskEvent;
    }

    public void showOpenFail(DownloadTask task) {
        mTaskEvent.setValue(new Event<>(new DownloadTaskEvent(DownloadTaskEvent.Events.OPEN_FILE_FAIL,task)));
    }
    public void showNetFail(DownloadTask task) {
        mTaskEvent.setValue(new Event<>(new DownloadTaskEvent(DownloadTaskEvent.Events.NET_FAIL,task)));
    }

    public MutableLiveData<List<Object>> getViewList(Context context) {
        if (mList == null) {
            mList = new MutableLiveData<List<Object>>() {
                @Override
                protected void onActive() {
                    initLoopDownloadTasks(context);
                }

                @Override
                protected void onInactive() {
                    unLoopDownloadTasks();
                }
            };
        }
        return mList;
    }

    private void initLoopDownloadTasks(Context context) {
        if (downloadTaskChange == null) {
            downloadTaskChange = new DownloadTaskChange() {
                @Override
                public void onChange(SparseArray<DownloadTask> tasks) {
                    List<Object> list = downloadListStrategy.layout(DownloadViewModel.this, tasks);
                    mList.postValue(list);
                }

                @Override
                public boolean showMarketStatus() {
                    return false;
                }
            };
            downloadsRepository.observeDownloadTask(downloadTaskChange);
        }
    }

    private void unLoopDownloadTasks() {
        if (downloadTaskChange != null) {
            downloadsRepository.unObserveDownloadTask(downloadTaskChange);
            downloadTaskChange = null;
        }
    }

    public MutableLiveData<Event<DownloadTasksActivity.DeleteProgressEvent>> getDeleteProgressEvent() {
        return mDeleteProgressEvent;
    }

    /**
     * 删除下载任务
     *
     * @param task
     * @param delFile
     */
    public void delete(DownloadTask task, boolean delFile) {
        if (task == null) {
            return;
        }
        mDeleteProgressEvent.setValue(new Event<>(new DownloadTasksActivity.DeleteProgressEvent(true, -1)));
        asyncWork(new Runnable() {
            @Override
            public void run() {

                int count = 0;
                int v = mDownloadUseCase.delete(getApplication(),task,delFile);
                if (v > 0) {
                    count++;
                }

                mDeleteProgressEvent.postValue(new Event<>(new DownloadTasksActivity.DeleteProgressEvent(false, count)));
            }
        });
    }

    /**
     * 删除下载任务
     *
     * @param list
     * @param delFile
     */
    public void delete(List<DownloadTask> list, boolean delFile) {
        TraceReportUi.reportNormalClick("delete");
        if (list == null) {
            return;
        }
        mDeleteProgressEvent.setValue(new Event<>(new DownloadTasksActivity.DeleteProgressEvent(true, -1)));
        asyncWork(new Runnable() {
            @Override
            public void run() {

                int count = 0;
                for (DownloadTask task : list) {
                    int v = mDownloadUseCase.delete(getApplication(),task,delFile);
                    if (v > 0) {
                        count++;
                    }
                }

                mDeleteProgressEvent.postValue(new Event<>(new DownloadTasksActivity.DeleteProgressEvent(false, count)));
            }
        });
    }

    /**
     * 重新开始下载
     *
     * @param task
     */
    public void restartDownload(DownloadTask task) {
        if (task == null) {
            return;
        }
        asyncWork(new Runnable() {
            @Override
            public void run() {
                mDownloadUseCase.restartDownload(getApplication(),task);
            }
        });
    }

    public void deleteChecked(SparseArray<CheckState> checkedStates) {
        if (checkedStates == null) {
            return;
        }
        ArrayList<DownloadTask> downloadTasks = new ArrayList<>();
        for (int i = 0; i < checkedStates.size(); i++) {
            CheckState state = checkedStates.valueAt(i);
            if (state instanceof DownloadStatus) {
                downloadTasks.add(((DownloadStatus) state).mTask);
            }
        }
        mDeleteTaskEvent.setValue(new Event<>(downloadTasks));
    }

    public void clearAllDownloadedTask() {
        TraceReportUi.reportNormalClick("all_empty");
        ArrayList<DownloadTask> downloadTasks = new ArrayList<>();
        List<Object> items = mList.getValue();
        if (items == null) {
            return;
        }
        for (Object task : items) {
            if (task instanceof DownloadStatus && ((DownloadStatus) task).isSuccess()) {
                downloadTasks.add(((DownloadStatus) task).mTask);
            }
        }
        if (downloadTasks.size() > 0) {
            mDeleteTaskEvent.setValue(new Event<>(downloadTasks));
        }
    }

    public MutableLiveData<Event<List<DownloadTask>>> getDeleteTaskEvent() {
        return mDeleteTaskEvent;
    }

    public void onTaskButtonClick(DownloadTask task) {
        TraceReportUi.reportNormalClick(task.isSuccess() ? "open" : "downloading");
        if (mDownloadTaskHandler != null) {
            mDownloadTaskHandler.handle(task);
        }
    }

    public boolean openDownloadTask(int id) {
        DownloadTask task = downloadsRepository.getTaskbyId(id);
        boolean open = false;
        if (task.isSuccess()) {
            SuccessTaskHandler successTaskHandler = new SuccessTaskHandler(this, mDownloadUseCase, null);
            successTaskHandler.handle(task);
            open = true;
        }
        return open;
    }

}
