package com.android.providers.downloads.ui.ui;

import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_FULL_SENSOR;
import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_SENSOR;
import static android.provider.MiuiSettings.Global.FORCE_FSG_NAV_BAR;

import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.core.view.WindowInsetsCompat;

import com.android.providers.downloads.R;
import com.android.providers.downloads.util.Helpers;

import miuix.appcompat.app.AppCompatActivity;
import miuix.appcompat.internal.view.menu.BottomMenuMode;

public abstract class BaseActivity extends AppCompatActivity {
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        adaptScreenOrientation();

        super.onCreate(savedInstanceState);
        if (Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
            getWindow().getAttributes().layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES;
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        adaptScreenOrientation();
    }

    protected void adaptScreenOrientation() {
        if (Helpers.isPad() || Helpers.isFold(getApplicationContext())) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR);
        } else {
            setRequestedOrientation(SCREEN_ORIENTATION_PORTRAIT);
        }
    }

    public static void setMargin(View view, int left, int right) {
        if (view != null) {
            int top = view.getPaddingTop();
            int bottom = view.getPaddingBottom();
            view.setPadding(left, top, right, bottom);
        }
    }
    public void onNavBarStyleChanged(boolean editorMode) {
        boolean threeButton = isThreeButton();
        Window window = getWindow();

        // Set transparent colors for status and navigation bars
        window.setStatusBarColor(Color.TRANSPARENT);
        window.setNavigationBarColor(Color.TRANSPARENT);

        // Use modern WindowCompat instead of deprecated setDecorFitsSystemWindows
        if (threeButton || editorMode) {
            // For three-button navigation or editor mode, let system handle window fitting
            WindowCompat.setDecorFitsSystemWindows(window, true);
        } else {
            // For gesture navigation, handle insets manually
            WindowCompat.setDecorFitsSystemWindows(window, false);
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
        }

        // Use modern WindowInsetsController instead of deprecated SYSTEM_UI_FLAG_*
        WindowInsetsControllerCompat windowInsetsController =
            WindowCompat.getInsetsController(window, window.getDecorView());

        if (threeButton || editorMode) {
            // For three-button navigation, show system bars normally
            windowInsetsController.show(WindowInsetsCompat.Type.systemBars());
            if (threeButton) {
                // Set light navigation bar for three-button mode
                windowInsetsController.setAppearanceLightNavigationBars(true);
            }
        } else {
            // For gesture navigation, enable edge-to-edge with light appearance
            windowInsetsController.setAppearanceLightNavigationBars(true);
            windowInsetsController.setAppearanceLightStatusBars(true);
            // Note: We don't hide system bars here as it's handled by the layout flags
        }
    }

    boolean isThreeButton() {
        return Settings.Global.getInt(getContentResolver(), FORCE_FSG_NAV_BAR, 0) == 0;
    }

}
