package com.android.providers.downloads.ui.ui;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.xiaomi.android.app.downloadmanager.MiuiDownloadManager;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/10/30.
 */

public class IntentFromUtils {
    public static final String EXTRA_SHORTCUT = "shortcut";
    public static final String SHORTCUT_SEARCH = "shortcut_search";
    private static final String EXTRA_KEY_PACKAGENAME = "intent_extra_application_packagename";
    private static final String EXTRA_KEY_COME_FROM = "come_from";
    private final static String ACTION_GLOBAL_SEARCH = "miui.intent.action.globalsearch.downloadprovider";
    public final static String ACTION_MAIN = "android.intent.action.MAIN";
    public final static String ACTION_GOTO_NEW_DOWNLOAD = "miui.intent.action.GOTO_NEW_DOWNLOAD";
    public final static String ACTION_GOTO_ACCOUNT = "miui.intent.action.GOTO_ACCOUNT";
    public final static String ACTION_VIEW_RANK = "miui.intent.action.VIEW_RANK";
    public final static String ACTION_GOTO_SEARCH_HOME = "miui.intent.action.GOTO_SEARCH_HOME";
    public static final String COME_FROM_DOWNLOAD = "download";
    public static final String COME_FROM_BROWSER = "browser";
    public static final String COME_FROM_SHORTCUT = "shortcut";
    public static final String COME_FROM_NOTIFY = "notification_bar";
    public static final String COME_FROM_FORCE_TOUCH = "force_touch";
    public static final String COME_FROM_MIUI_SEARCH = "miui_search";//miui全局搜索

    public static final String FROM_PKG_BROWSER = "com.android.browser";
    public static final String FROM_PKG_MIUI_SEARCH = "com.android.quicksearchbox";

    public static String getSourceFromIntent(Intent intent) {
        if (intent == null) {
            return "other";
        }
        Bundle extras = intent.getExtras();
        String action = intent.getAction();
        Log.d("IntentFromUtils", "Activity action: " + (action == null ? "null" : action));
        if (extras != null && extras.size() > 0) {
            for (String key : extras.keySet()) {
                Log.d("IntentFromUtils", "Activity extras " + (key + ":" + extras.get(key)));
            }
        }
        String comeFrom = null;
        if (intent.getAction() == null) {
            comeFrom = COME_FROM_DOWNLOAD;
        }

        //分析全局搜索的intent
        flattenIntentData(intent);

        String shortcut = intent.getStringExtra(EXTRA_SHORTCUT);
        if (SHORTCUT_SEARCH.equals(shortcut)) {
            comeFrom = COME_FROM_SHORTCUT;
        }

        //接入全局搜索
        String comfrom = intent.getStringExtra(EXTRA_KEY_COME_FROM);

        if (!TextUtils.isEmpty(comfrom)) {
            comeFrom = comfrom;
        }

        String openAction = intent.getAction();
        if (MiuiDownloadManager.ACTION_VIEW_DOWNLOADS_LIST.equals(openAction)) {
            String curFromPkgName = intent.getStringExtra(EXTRA_KEY_PACKAGENAME);
            if (FROM_PKG_BROWSER.equals(curFromPkgName)) {
                comeFrom = COME_FROM_BROWSER;
            } else if (FROM_PKG_MIUI_SEARCH.equals(curFromPkgName)) {
                comeFrom = COME_FROM_MIUI_SEARCH;
            } else {
                comeFrom = curFromPkgName;
            }
        }
        if (ACTION_MAIN.equals(openAction)) {
            String curFromPkgName = intent.getStringExtra(EXTRA_KEY_PACKAGENAME);
            if (TextUtils.isEmpty(curFromPkgName)) {
                if (FROM_PKG_MIUI_SEARCH.equals(getFromReferrer(intent))) {
                    comeFrom = COME_FROM_MIUI_SEARCH;
                } else {
                    comeFrom = COME_FROM_DOWNLOAD;
                }
            } else {
                if (FROM_PKG_BROWSER.equals(curFromPkgName)) {
                    comeFrom = COME_FROM_BROWSER;
                } else if (FROM_PKG_MIUI_SEARCH.equals(curFromPkgName)) {
                    comeFrom = COME_FROM_MIUI_SEARCH;
                } else {
                    comeFrom = curFromPkgName;
                }
            }
        }

        if (ACTION_GOTO_NEW_DOWNLOAD.equals(openAction) ||
                ACTION_GOTO_SEARCH_HOME.equals(openAction) ||
                ACTION_VIEW_RANK.equals(openAction)) {
            comeFrom = COME_FROM_FORCE_TOUCH;
        }

        if (TextUtils.isEmpty(comeFrom)) {
            comeFrom = COME_FROM_NOTIFY;
        }

        return comeFrom;
    }

    private static String getFromReferrer(Intent intent) {
        //eg:android.intent.extra.REFERRER=android-app://com.android.quicksearchbox
        Uri referrer = intent.getParcelableExtra(Intent.EXTRA_REFERRER);
        if (referrer != null) {
            String auth = referrer.getAuthority();
            return auth;
        }
        return null;
    }

    /**
     * 分析全局搜索的intent
     *
     * @param intent
     */
    private static void flattenIntentData(Intent intent) {
        if (intent == null) {
            return;
        }
        if (TextUtils.equals(intent.getAction(), ACTION_GLOBAL_SEARCH)) {
            Uri uri = intent.getData();
            if (null == uri) {
                return;
            }
            intent.putExtra(EXTRA_KEY_COME_FROM, COME_FROM_MIUI_SEARCH);

            String typeRec = uri.getQueryParameter("extra_type");
            if ("vip".equals(typeRec)) {
                intent.setAction(ACTION_GOTO_ACCOUNT);
            } else if ("rank".equals(typeRec)) {
                intent.setAction(ACTION_VIEW_RANK);
            } else if ("new".equals(typeRec)) {
                intent.setAction(ACTION_GOTO_NEW_DOWNLOAD);
            } else {
                intent.setAction(ACTION_MAIN);
            }
        }
    }

}
