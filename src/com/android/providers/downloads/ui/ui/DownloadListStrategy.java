package com.android.providers.downloads.ui.ui;

import static com.android.providers.downloads.ui.data.DownloadTask.STATUS_CODE_DOWNLOAD_SUCCESS;

import static miuix.recyclerview.card.CardGroupAdapter.GROUP_TYPE_BODY;

import android.app.Application;
import android.util.SparseArray;

import com.android.providers.downloads.R;
import com.android.providers.downloads.ui.data.DownloadTask;
import com.android.providers.downloads.ui.ui.adapter.DownloadStatus;
import com.android.providers.downloads.ui.utils.DateUtil;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class DownloadListStrategy {
    private final Comparator<DownloadStatus> mComparator;
    private final long mStartTimeOfToday;

    public DownloadListStrategy() {
        mComparator = new Comparator<DownloadStatus>() {
            @Override
            public int compare(DownloadStatus o1, DownloadStatus o2) {
                return o2.getCheckId() - o1.getCheckId();
            }
        };
        mStartTimeOfToday = DateUtil.getStartOfToday().getTime();
    }

    public List<Object> layout(DownloadViewModel downloadViewModel,SparseArray<DownloadTask> tasks) {
        Application app = downloadViewModel.getApplication();
        ArrayList<Object> objects = new ArrayList<>();
        ArrayList<DownloadStatus> downloadingTasks = new ArrayList<>();
        ArrayList<DownloadStatus> downloadedTasks = new ArrayList<>();
        for (int i = 0; i < tasks.size(); i++) {
            DownloadTask downloadTask = tasks.valueAt(i);
            DownloadStatus status = new DownloadStatus(downloadTask, downloadViewModel);
            status.groupId = GROUP_TYPE_BODY;
            if (downloadTask.status == STATUS_CODE_DOWNLOAD_SUCCESS) {
                if (downloadTask.lastModifyTime >= mStartTimeOfToday) {
                    downloadedTasks.add(status);
                }
            } else {
                downloadingTasks.add(status);
            }
        }
        downloadingTasks.sort(mComparator);
        downloadedTasks.sort(mComparator);
        objects.add(new TitleModel(app.getString(R.string.downloading), false,false,downloadViewModel));
        if (downloadingTasks.size() > 0) {
            objects.addAll(downloadingTasks);
        } else {
            objects.add(new EmpthModel());
        }
        objects.add(new DividerModel());
        objects.add(new TitleModel(app.getString(R.string.today_download), downloadedTasks.size() > 0,true,downloadViewModel));
        if (downloadedTasks.size() > 0) {
            objects.addAll(downloadedTasks);
        } else {
            objects.add(new EmpthModel());
        }

        return objects;
    }

}
