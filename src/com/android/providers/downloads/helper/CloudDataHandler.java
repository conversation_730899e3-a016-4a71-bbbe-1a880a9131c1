package com.android.providers.downloads.helper;

import android.content.Context;

import com.android.providers.downloads.api.cloudcontrol.CloudConfig;
import com.android.providers.downloads.api.cloudcontrol.CloudConfigurationRequest;
import com.android.providers.downloads.api.cloudcontrol.CloudConfigurationResponse;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.util.PropertieUtil;
import com.michael.corelib.coreutils.CustomThreadPool;
import com.michael.corelib.internet.InternetClient;
import com.michael.corelib.internet.core.NetworkResponse;
import com.michael.corelib.internet.core.RequestBase;
import com.michael.corelib.internet.core.util.JsonUtils;

public class CloudDataHandler {
    public void loadConfigFromSdcard() {
        CustomThreadPool.asyncWork(new Runnable() {
            @Override
            public void run() {
                String cloudConfig= PropertieUtil.loadDataFormFile();
                CloudConfigurationResponse response = JsonUtils.parse(cloudConfig, CloudConfigurationResponse.class);
                saveCloudConfig(response);
            }
        });
    }

    public void loadConfigFromNet(Context context) {
        final CloudConfigurationRequest cloudConfigurationRequest = new CloudConfigurationRequest();
        InternetClient.getInstance(context).postRequest(cloudConfigurationRequest, new InternetClient.NetworkCallback<CloudConfigurationResponse>() {

            @Override
            public void onSuccess(RequestBase<CloudConfigurationResponse> accelerateConfigResponseRequestBase, CloudConfigurationResponse accelerateConfigResponse) {
                saveCloudConfig(accelerateConfigResponse);
            }

            @Override
            public void onFailed(RequestBase<CloudConfigurationResponse> accelerateConfigResponseRequestBase, NetworkResponse networkResponse) {
            }
        });
    }

    private void saveCloudConfig(CloudConfigurationResponse response){
        if(response != null && response.data != null){
            CloudConfig cloudConfigrationData = JsonUtils.parse(response.data, CloudConfig.class);
            if(cloudConfigrationData == null ||  cloudConfigrationData.fileSizeLimit <= 0){
                return;
            }
            CloudConfigPreference cloudConfig = CloudConfigPreference.getInstance();
            cloudConfig.setAccelerateFileSizeLimit(cloudConfigrationData.fileSizeLimit);
            cloudConfig.setAccelerateFilePercent(cloudConfigrationData.fileDownloadPercent);
            cloudConfig.setAccelerateCount(cloudConfigrationData.accelerateCount);
            cloudConfig.setAccelerateDration(cloudConfigrationData.accelerateDration);
            cloudConfig.setResetDuration(cloudConfigrationData.resetDuration);
            cloudConfig.setLowSpeedDuraion(cloudConfigrationData.lowSpeedDuration);
            cloudConfig.setLowSpeedLimit(cloudConfigrationData.lowSpeed);
            cloudConfig.setLastUpdateTime(System.currentTimeMillis());
            cloudConfig.setUpdateDelayTime(response.nextUpdateTime);
            cloudConfig.setAcceleteSwitch(cloudConfigrationData.accelerateSwitch);
            cloudConfig.setNotifyRecommedSwitch(cloudConfigrationData.notifyRecommendSwitch);
            cloudConfig.setNotifyRecommedRecType(cloudConfigrationData.notifyRecommendRectype);
            cloudConfig.setNotifyRecommedDayCount(cloudConfigrationData.notifyRecommendCount);
            cloudConfig.setNotifyRecommedImeiSwitch(cloudConfigrationData.notifyRecommendImeiSwitch);
            cloudConfig.setNotifyRecommedNetworkType(cloudConfigrationData.notifyRecommendNetworkType);
            cloudConfig.setNotifyRecommedDuration(cloudConfigrationData.notifyRecommendduration);
            cloudConfig.setDevShowActivateNotify(cloudConfigrationData.isDevShowActivateNotify);
            cloudConfig.setStableShowActivateNotify(cloudConfigrationData.isStableShowActivateNotify);
            cloudConfig.setBlackListShowTask(cloudConfigrationData.blackListShowTaskWithUiAndNotify);
            cloudConfig.setBlackListNotifyFilter(cloudConfigrationData.blackListNotifyFilter);
            cloudConfig.setJumpMarketSwitch(cloudConfigrationData.jumpMarketSwitch);
            cloudConfig.setTrySpeedUp(cloudConfigrationData.trySpeedUpConfig);
            cloudConfig.setLimitSpeedConfig(cloudConfigrationData.limitSpeedConfig);
            cloudConfig.setUseXunleiEngine(cloudConfigrationData.useXunleiEngine);
            cloudConfig.setUseXunleiEngineInR(cloudConfigrationData.useXunleiEngineInR);
            cloudConfig.setJumpMarketByMiuiVersion(cloudConfigrationData.jumpMarketByMiuiVersion);
            cloudConfig.setScdnWhite(cloudConfigrationData.scdnWhiteList);
            cloudConfig.setBigMemJson(cloudConfigrationData.bigmem);
            cloudConfig.setTraceApiSwitch(cloudConfigrationData.switchTraceApi);
            cloudConfig.setTraceProcessStartSwitch(cloudConfigrationData.switchTraceProcessStart);
            cloudConfig.setTraceServiceStartSwitch(cloudConfigrationData.switchTraceServiceStart);
            cloudConfig.setTokenFailUpdateInterval(cloudConfigrationData.tokenFailUpdateInterval);
            cloudConfig.setTraceMethodSwitch(cloudConfigrationData.switchTraceMethodCall);
            cloudConfig.setLimitSpeedConfig2(cloudConfigrationData.limitSpeedConfig2);
            cloudConfig.setUpOneTrack(cloudConfigrationData.upOneTrack);
            cloudConfig.enableConnsForOneTack(cloudConfigrationData.switchConnsOnetrack);
            cloudConfig.enableConnsForHub(cloudConfigrationData.switchConnsHub);
            cloudConfig.setSpeedupSwitch(cloudConfigrationData.switchSpeedup2);
            cloudConfig.setDataLimitSwitch(cloudConfigrationData.switchDataLimit);
            cloudConfig.setCheckFileSizeSwitch(cloudConfigrationData.switchCheckFileSize);
            cloudConfig.setDownloadEngine(cloudConfigrationData.downloadEngine);
            cloudConfig.setFeatureSwitchs(cloudConfigrationData.featureSwitch);
            cloudConfig.setWakelockExposed(cloudConfigrationData.wakelockExposed);
            if (XLConfig.isDebug()) {
                XLConfig.LOGD(CloudConfigPreference.TAG, "saveCloudConfig  >>" + cloudConfigrationData.toString());
            }
        }
    }

}
