package com.android.providers.downloads.helper;

import android.text.TextUtils;
import android.util.ArraySet;

import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.setting.CloudConfigPreference;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import miuix.core.util.SystemProperties;

/**
 * 功能云控开关
 * 云控格式为
 * 1,0,1,1,0
 * <p>
 * 一个功能开关为,号分隔的一位
 * 1 功能打开
 * 0 功能关闭
 * 为空 使用程序默认值
 */
public class FeatureSwitch {

    private static final int FLAG_FEATURE_HTTPS_CHECK = 0;
    private static final int FLAG_FEATURE_CONSCRYPT = 1;
    private static final int FLAG_FEATURE_HTTPS_HOST = 2;
    private static final int FLAG_FEATURE_AUTHTYPE = 3;
    private static final int FLAG_FEATURE_THROW_HTTPS_FAIL = 4;
    private static final int FLAG_FEATURE_REPORT_CERTIFICATE = 5;
    private static final int FLAG_FEATURE_FILE_OPT = 6;

    private static Map<String, String> featureMaps;
    private static final String ALL = "all";

    public static boolean enableHttpsVerity(String packageName) {
        return enableFeature(packageName,FLAG_FEATURE_HTTPS_CHECK, false);
    }

    public static boolean enableConscrypt(String packageName) {
        return enableFeature(packageName,FLAG_FEATURE_CONSCRYPT, true);
    }

    public static boolean enableCheckHttpsHost(String packageName) {
        return enableFeature(packageName,FLAG_FEATURE_HTTPS_HOST, true);
    }

    public static boolean enableCheckHttpsAuthtype(String packageName) {
        return enableFeature(packageName,FLAG_FEATURE_AUTHTYPE, false);
    }
    public static boolean enableThrowHttpsFail(String packageName) {
        return enableFeature(packageName,FLAG_FEATURE_THROW_HTTPS_FAIL, false);
    }

    public static boolean enableRepportCertificate(String packageName) {
        return enableFeature(packageName,FLAG_FEATURE_REPORT_CERTIFICATE, false);
    }

    public static boolean enableFileOpt() {
        return enableFeature(ALL,FLAG_FEATURE_FILE_OPT, false);
    }

    private static boolean enableFeature(String packageName, int position, boolean defaultSwitch) {
        initFeatureMap();
        boolean retSwitch = defaultSwitch;
        String realPackageName = TextUtils.isEmpty(packageName) ? ALL : packageName;
        String featureSwitch;
        if (featureMaps.containsKey(realPackageName)) {
            featureSwitch = featureMaps.get(realPackageName);
        } else {
            featureSwitch = featureMaps.get(ALL);
        }
        if (!TextUtils.isEmpty(featureSwitch)) {
            String[] features = featureSwitch.trim().split(",");
            if (features.length > position) {
                String feature = features[position];
                if ("1".equals(feature)) {
                    retSwitch = true;
                } else if ("0".equals(feature)) {
                    retSwitch = false;
                }
            }
        }
        return retSwitch;
    }

    public synchronized static void resetFeature() {
       featureMaps = null;
       initFeatureMap();
    }

    private synchronized static void initFeatureMap() {
        if (featureMaps != null) {
            return;
        }
        featureMaps = new HashMap<String, String>();
        Set<String> featureSwitchs = CloudConfigPreference.getInstance().getFeatureSwitchs();

        //测试用
        if (XLConfig.isDebug()) {
            String downloadsFeatureSwitch = SystemProperties.get("downloads_feature_switch");
            if (!TextUtils.isEmpty(downloadsFeatureSwitch)) {
                featureSwitchs = new ArraySet<>();
                featureSwitchs.add(downloadsFeatureSwitch);
            }
        }
        if (featureSwitchs != null && featureSwitchs.size() > 0) {
            for (String featureSwitch : featureSwitchs) {
                String[] items = featureSwitch.split(" ");
                String feature = null;
                String packagName = ALL;
                if (items.length > 0) {
                    feature = items[0];
                }
                if (items.length > 1) {
                    packagName = items[1];
                }
                if (!TextUtils.isEmpty(feature)) {
                    featureMaps.put(packagName, feature);
                }
            }
        }
    }
}
