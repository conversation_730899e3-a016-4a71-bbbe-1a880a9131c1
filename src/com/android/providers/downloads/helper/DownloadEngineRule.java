package com.android.providers.downloads.helper;

import android.text.TextUtils;

import com.android.providers.downloads.api.cloudcontrol.DownloadEngineConfig;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.setting.PrivacySettingHelper;
import com.android.providers.downloads.util.DownloadExtra2;
import com.android.providers.downloads.utils.LogUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DownloadEngineRule {
    private volatile static DownloadEngineRule mInstance;

    public static DownloadEngineRule getInstance() {
        if (mInstance == null) {
            synchronized (DownloadEngineRule.class) {
                if (mInstance == null) {
                    mInstance = new DownloadEngineRule();
                }
            }
        }
        return mInstance;
    }

    private DownloadEngineRule() {
        parseCloudAndConfig(CloudConfigPreference.getInstance().getDownloadEngine());
    }

    private final int TYPE_DEFAULT = -1;
    private final int TYPE_USE_XUNLEI = 0;
    private final int TYPE_USE_ANDROID = 1;
    private final Map<String, Boolean> mUseXunleiEngine = new HashMap<>();
    private int allEngineType = TYPE_DEFAULT;

    public void parseCloudAndConfig(DownloadEngineConfig downloadEngine) {
        reset();
        if (downloadEngine == null) {
            return;
        }
        try {
            List<String> xunlei = downloadEngine.xunlei;
            List<String> android = downloadEngine.android;
            if (xunlei != null && xunlei.size() > 0) {
                for (int i = 0; i < xunlei.size(); i++) {
                    String pkg = xunlei.get(i);
                    if (!TextUtils.isEmpty(pkg)) {
                        mUseXunleiEngine.put(pkg, true);
                    }
                    if ("all".equals(pkg)) {
                        allEngineType = TYPE_USE_XUNLEI;
                    }
                }
            }
            if (android != null && android.size() > 0) {
                for (int i = 0; i < android.size(); i++) {
                    String pkg = android.get(i);
                    if (allEngineType != TYPE_USE_XUNLEI) {
                        if ("all".equals(pkg)) {
                            allEngineType = TYPE_USE_ANDROID;
                        }
                        if (!TextUtils.isEmpty(pkg) && !mUseXunleiEngine.containsKey(pkg)) {
                            mUseXunleiEngine.put(pkg, false);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.forceD("useEngine:" + e.toString());
        }
    }

    private void reset() {
        mUseXunleiEngine.clear();
        allEngineType = -1;
    }

    //    priority:cfg > app > ui switch
    public boolean useXunleiEngine(String pkg, String appCfg) {
        boolean useXunlei = true;
        if (allEngineType == TYPE_USE_XUNLEI) {
            useXunlei = true;
        } else if (allEngineType == TYPE_USE_ANDROID) {
            useXunlei = false;
        } else if (mUseXunleiEngine.containsKey(pkg)) {
            useXunlei = mUseXunleiEngine.get(pkg);
        } else {
            //use app cfg
            int useEnsgine = DownloadExtra2.getUseEngine(appCfg);
            if (useEnsgine == 1 || useEnsgine == 0) {
                useXunlei = useEnsgine == 1;
            } else {
                //use ui cfg
                useXunlei = PrivacySettingHelper.isXunleiUsageOpen();
            }
        }
        return useXunlei;
    }

}
