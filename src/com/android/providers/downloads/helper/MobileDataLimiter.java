package com.android.providers.downloads.helper;

import android.content.Context;
import android.util.Log;

import com.android.providers.downloads.api.MobileDataResponse;
import com.android.providers.downloads.api.MobileDataLimitRequest;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.michael.corelib.internet.InternetClient;
import com.michael.corelib.internet.core.NetworkResponse;
import com.michael.corelib.internet.core.RequestBase;


public class MobileDataLimiter {
    public void loadConfigFromNet(Context context) {
        if (!CloudConfigPreference.getInstance().enableDataLimit()) {
            return;
        }
        if (!shouldUpdate()) {
            return;
        }
        final MobileDataLimitRequest request = new MobileDataLimitRequest(context);
        InternetClient.getInstance(context).postRequest(request, new InternetClient.NetworkCallback<MobileDataResponse>() {

            @Override
            public void onSuccess(RequestBase<MobileDataResponse> requestBase, MobileDataResponse mobileDataResponse) {
                CloudConfigPreference cloudConfig = CloudConfigPreference.getInstance();
                if (mobileDataResponse != null) {
                    cloudConfig.updateDataLimitLastTime(System.currentTimeMillis());
                    MobileDataResponse.Config values = mobileDataResponse.getValues();
                    if (values != null && values.getDataLimitConfig() != null) {
                        cloudConfig.setDataLimitDelayTime(values.getNextUpdateTime());
                        cloudConfig.setDataLimitConfig(values.getDataLimitConfig());
                    } else {
                        Log.i(MobileDataLimiter.class.getSimpleName(),"error MobileDataResponse.Config = null");
                    }
                } else {
                    Log.i(MobileDataLimiter.class.getSimpleName(),"error mobileDataResponse = null");
                }
            }

            @Override
            public void onFailed(RequestBase<MobileDataResponse> requestBase, NetworkResponse networkResponse) {
            }
        });
    }

    public boolean shouldUpdate() {
        CloudConfigPreference cloudConfig = CloudConfigPreference.getInstance();
        long curTime = System.currentTimeMillis();
        long lastTime = cloudConfig.getDataLimitLastTime();
        long duration = curTime - lastTime;
        if (lastTime == 0 || duration < 0 || duration > cloudConfig.getDataLimitDelayTime()) {
            return true;
        }
        return false;
    }
}
