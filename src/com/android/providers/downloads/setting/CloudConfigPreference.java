package com.android.providers.downloads.setting;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.ArraySet;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.alarm.LimitSpeed;
import com.android.providers.downloads.api.cloudcontrol.DownloadEngineConfig;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.helper.DownloadEngineRule;
import com.android.providers.downloads.helper.FeatureSwitch;
import com.android.providers.downloads.kcg.utils.KCGLog;
import com.android.providers.downloads.kcg.utils.KcgHelper;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.DataUsageHelper;
import com.android.providers.downloads.util.DownloadExtra2;
import com.android.providers.downloads.util.XlDownloadManagerHelper;
import com.android.providers.downloads.utils.LogUtil;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by wangyong on 15/12/24.
 */
public class CloudConfigPreference {
    public static final String TAG = "CloudConfig";
    private static final String SP_NAME = "CloudConfigSharePreference";

    private static final long HOURS_24 = ((long) 24) * 60 * 60 * 1000;

    private volatile static CloudConfigPreference mInstance;

    private Context mContext;

    public static CloudConfigPreference getInstance(){

        if(mInstance == null){
            synchronized (CloudConfigPreference.class){
                if(mInstance == null){
                    mInstance = new CloudConfigPreference();
                }
            }
        }

        return mInstance;
    }

    private CloudConfigPreference() {
    }

    private SharedPreferences getSharePreference() {
        Context context = DownloadApplication.getGlobalApplication();
        return context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
    }

    private SharedPreferences.Editor getEditor() {
        return getSharePreference().edit();
    }

    private static final String KEY_FILE_SIZE_LIMIT = "key_file_size_limit";

    public void setAccelerateFileSizeLimit(int fileSizeLimit){
        getEditor().putInt(KEY_FILE_SIZE_LIMIT, fileSizeLimit).commit();
    }

    public int getAccelerateFileSizeLimit(){
        return getSharePreference().getInt(KEY_FILE_SIZE_LIMIT, 200);
    }

    private static final String KEY_FILE_DOWNLOAD_PERCENT = "key_file_download_percent";

    public void setAccelerateFilePercent(int percent){
        getEditor().putInt(KEY_FILE_DOWNLOAD_PERCENT, percent).commit();
    }

    public int getAccelerateFilePercent(){
        return getSharePreference().getInt(KEY_FILE_DOWNLOAD_PERCENT, 40);
    }


    private static final String KEY_ACCELERATE_COUNT = "key_accelerate_count";

    public void setAccelerateCount(int accelerateCount){
        getEditor().putInt(KEY_ACCELERATE_COUNT, accelerateCount).commit();
    }

    public int getAccelerateCount(){
        return getSharePreference().getInt(KEY_ACCELERATE_COUNT, 3);
    }

    private static final String KEY_ACCELERATE_DURAION = "key_accelerate_duration";

    public void setAccelerateDration(int accelerateDration){
        getEditor().putInt(KEY_ACCELERATE_DURAION, accelerateDration).commit();
    }

    public int getAccelerateDration(){
        return getSharePreference().getInt(KEY_ACCELERATE_DURAION, 60);
    }

    private static final String KEY_RESET_DURATION = "key_reset_duration";

    public void setResetDuration(int resetDuration){
        getEditor().putInt(KEY_RESET_DURATION, resetDuration).commit();
    }

    public int getResetDuration(){
        return getSharePreference().getInt(KEY_RESET_DURATION, 1);
    }

    private static final String KEY_LOW_SPEED_DURATION = "key_low_speed_duration";

    public void setLowSpeedDuraion(int lowSpeedDuraion){
        getEditor().putInt(KEY_LOW_SPEED_DURATION, lowSpeedDuraion).commit();
    }

    public int getLowSpeedDuration(){
        return getSharePreference().getInt(KEY_LOW_SPEED_DURATION, 10);
    }

    private static final String KEY_LOW_SPEED_LIMIT = "key_low_speed_limit";

    public void setLowSpeedLimit(int limit){
        getEditor().putInt(KEY_LOW_SPEED_LIMIT, limit).commit();
    }


    public int getLowSpeedLimit(){
        return getSharePreference().getInt(KEY_LOW_SPEED_LIMIT, 50);
    }


    private static final String KEY_CLOUND_VERSION = "key_clound_version";

    public void setCloundVersion(String version){
        getEditor().putString(KEY_CLOUND_VERSION, version).commit();
    }

    public String getCloudVersion(){
        return getSharePreference().getString(KEY_CLOUND_VERSION, "");
    }

    private static final String KEY_LAST_UPDATE_TIME = "key_last_update_time";

    public void setLastUpdateTime(long time){
        getEditor().putLong(KEY_LAST_UPDATE_TIME, time).commit();
    }

    public long getlastUpdateTime(){
        return getSharePreference().getLong(KEY_LAST_UPDATE_TIME, 0);
    }


    private static  final String KEY_UPDATE_DELAY_TIME = "key_update_delay_time";

    public void setUpdateDelayTime(long time){
        getEditor().putLong(KEY_UPDATE_DELAY_TIME, time).commit();
    }

    public long getUpdateDelayTime(){
        return getSharePreference().getLong(KEY_UPDATE_DELAY_TIME, 0);
    }

    private static  final String KEY_UPDATE_ACCELETE_SWITCH = "key_accelete_switch";

    public void setAcceleteSwitch(boolean  mSwitch){
        getEditor().putBoolean(KEY_UPDATE_ACCELETE_SWITCH, mSwitch).commit();
    }

    public boolean getAcceleteSwitch(){
        return getSharePreference().getBoolean(KEY_UPDATE_ACCELETE_SWITCH, true);
    }


    private static  final String KEY_NOTIFY_RECOMMEND_SWITCH = "key_notify_recommed_switch";

    public void setNotifyRecommedSwitch(boolean  mSwitch){
        getEditor().putBoolean(KEY_NOTIFY_RECOMMEND_SWITCH, mSwitch).commit();
    }

    public boolean getNotifyRecommedSwitch() {
        SharedPreferences preference = getSharePreference();
        if (preference == null) {
            return false;
        }
        return preference.getBoolean(KEY_NOTIFY_RECOMMEND_SWITCH, false);
    }

    private static  final String KEY_NOTIFY_RECOMMEND_RECTYPE = "key_notify_recommed_rectype";

    public void setNotifyRecommedRecType(String  recType){
        if(TextUtils.isEmpty(recType)){
            return;
        }
        getEditor().putString(KEY_NOTIFY_RECOMMEND_RECTYPE, recType).commit();
    }

    public String getNotifyRecommedRecType(){
        return getSharePreference().getString(KEY_NOTIFY_RECOMMEND_RECTYPE, "own");
    }


    private static  final String KEY_NOTIFY_RECOMMEND_DAY_COUNT = "key_notify_recommed_dayCount";

    public void setNotifyRecommedDayCount(int dayCount){
        getEditor().putInt(KEY_NOTIFY_RECOMMEND_DAY_COUNT, dayCount).commit();
    }

    public int getNotifyRecommedDayCount(){
        return getSharePreference().getInt(KEY_NOTIFY_RECOMMEND_DAY_COUNT, 1);
    }

    private static  final String KEY_NOTIFY_RECOMMEND_IMEI_SWITCH= "key_notify_recommed_imei_switch";

    public void setNotifyRecommedImeiSwitch(String  imeiSwitch){
        if(TextUtils.isEmpty(imeiSwitch)){
            return;
        }
        getEditor().putString(KEY_NOTIFY_RECOMMEND_IMEI_SWITCH, imeiSwitch).commit();
    }

    public String getNotifyRecommedImeiSwitch(){
        return getSharePreference().getString(KEY_NOTIFY_RECOMMEND_IMEI_SWITCH, "");
    }

    private static  final String KEY_NOTIFY_RECOMMEND_NETWORK_TYPE= "key_notify_recommed_network_type";

    public void setNotifyRecommedNetworkType(int  networkType){
        if(networkType == 0){
            return;
        }
        getEditor().putInt(KEY_NOTIFY_RECOMMEND_NETWORK_TYPE, networkType).commit();
    }

    public int getNotifyRecommedNetworkType(){
        return getSharePreference().getInt(KEY_NOTIFY_RECOMMEND_NETWORK_TYPE,1);
    }


    private static  final String KEY_NOTIFY_RECOMMEND_DURATION= "key_notify_recommed_duration";

    public void setNotifyRecommedDuration(long  networkDuration){
        if(networkDuration == 0){
            return;
        }
        getEditor().putLong(KEY_NOTIFY_RECOMMEND_DURATION, networkDuration).commit();
    }

    public long getNotifyRecommedDuration(){
        return getSharePreference().getLong(KEY_NOTIFY_RECOMMEND_DURATION,10);
    }

    private static  final String KEY_DEV_SHOW_ACTIVATE_NOTIFY= "key_dev_show_activate_notify";

    public void setDevShowActivateNotify(boolean show) {
        getEditor().putBoolean(KEY_DEV_SHOW_ACTIVATE_NOTIFY, show).commit();
    }

    public boolean isDevShowActivateNotify() {
        return getSharePreference().getBoolean(KEY_DEV_SHOW_ACTIVATE_NOTIFY, false);
    }

    private static final String KEY_STABLE_SHOW_ACTIVATE_NOTIFY = "key_stable_show_activate_notify";

    public void setStableShowActivateNotify(boolean show) {
        getEditor().putBoolean(KEY_STABLE_SHOW_ACTIVATE_NOTIFY, show).commit();
    }

    public boolean isStableShowActivateNotify() {
        return getSharePreference().getBoolean(KEY_STABLE_SHOW_ACTIVATE_NOTIFY, false);
    }

    private static final String KEY_BLACK_LIST_SHOW_TASK = "key_black_list_show_task";
    public void setBlackListShowTask(String jsonPkgs) {
        try {
            if (!TextUtils.isEmpty(jsonPkgs)) {
                JSONArray blackArray = new JSONArray(jsonPkgs);
                mBlackListShowTaskWithUiAndNotify.clear();
                for (int i = 0; i < blackArray.length(); i++) {
                    String pkg = (String) blackArray.get(i);
                    mBlackListShowTaskWithUiAndNotify.add(pkg);
                }
            } else {
                mBlackListShowTaskWithUiAndNotify.clear();
            }
            XLConfig.LOGD(String.format("setBlackListShowTask jsonPkgs(%s) pkgs(%s)", String.valueOf(jsonPkgs),
                    mBlackListNotifyFilter.toString()));
            getEditor().putString(KEY_BLACK_LIST_SHOW_TASK, jsonPkgs).commit();
        } catch (Exception e) {
            XLConfig.LOGD_INFO(String.format("setBlackListShowTask ex json(%s)", String.valueOf(jsonPkgs)), e);
        }
    }

    public String getBlackListShowTask() {
        return getSharePreference().getString(KEY_BLACK_LIST_SHOW_TASK, "");
    }

    private static final String KEY_BLACK_LIST_NOTIFY_FILTER = "key_black_list_notify_filter";

    public void setBlackListNotifyFilter(String jsonPkgs) {
        try {
            if (!TextUtils.isEmpty(jsonPkgs)) {
                JSONArray blackArray = new JSONArray(jsonPkgs);
                mBlackListNotifyFilter.clear();
                for (int i = 0; i < blackArray.length(); i++) {
                    String pkg = (String) blackArray.get(i);
                    mBlackListNotifyFilter.add(pkg);
                }
            } else {
                mBlackListNotifyFilter.clear();
            }
            XLConfig.LOGD(String.format("setBlackListNotifyFilter jsonPkgs(%s) pkgs(%s)", String.valueOf(jsonPkgs),
                    mBlackListNotifyFilter.toString()));
            getEditor().putString(KEY_BLACK_LIST_NOTIFY_FILTER, jsonPkgs).commit();
        } catch (Exception e) {
            XLConfig.LOGD_INFO(String.format("setBlackListNotifyFilter ex json(%s)", String.valueOf(jsonPkgs)), e);
        }
    }

    public String getBlackListNotifyFilter() {
        return getSharePreference().getString(KEY_BLACK_LIST_NOTIFY_FILTER, "");
    }

    private HashSet<String> mBlackListShowTaskWithUiAndNotify = new HashSet<String>();
    private HashSet<String> mBlackListNotifyFilter = new HashSet<String>();

    public boolean isShowTaskWithUiAndNotification(String pkg) {
        if (TextUtils.isEmpty(pkg)) {
            return false;
        }
        return mBlackListShowTaskWithUiAndNotify.contains(pkg);
    }

    public boolean isInterceptDownloadNotify(String pkg) {
        if (TextUtils.isEmpty(pkg)) {
            return false;
        }
        return mBlackListNotifyFilter.contains(pkg);
    }

    private static final String KEY_DATA_LIMIT_CONFIG = "key_data_limit_config_new";

    public void setDataLimitConfig(String jsonConfig) {
        if (jsonConfig == null) {
            return;
        }

        try {
            getEditor().putString(KEY_DATA_LIMIT_CONFIG, jsonConfig).commit();
            DataUsageHelper.loadDataLimit();
            LogUtil.logD(DataUsageHelper.TAG, "setDataLimitConfig" + DataUsageHelper.dumpConfig());
        } catch (Exception e) {
            LogUtil.logD(DataUsageHelper.TAG, String.format("setDataLimitConfig fail(%s)", jsonConfig), e);
        }
    }

    public String getDataLimitConfig() {
        return getSharePreference().getString(KEY_DATA_LIMIT_CONFIG, null);
    }

    private static  final String KEY_JUMP_MARKET_SWITCH= "key_jump_market_switch";

    public void setJumpMarketSwitch(String  jumpSwitch) {
        //jumpSwitch = "1,1,1,1,1,1,1,1,1,0";

        if (TextUtils.isEmpty(jumpSwitch)) {
            String oldValue=getJumpMarketSwitch();
            if(TextUtils.isEmpty(oldValue)){

            }else{
                getEditor().remove(KEY_JUMP_MARKET_SWITCH).commit();
            }
            return;
        }
        String[] jswitch = jumpSwitch.split(",");
        if (jswitch.length != 10) {
            LogUtil.forceD(String.format("setJumpMarketSwitch fail", jumpSwitch));
            return;
        }
        getEditor().putString(KEY_JUMP_MARKET_SWITCH, jumpSwitch).commit();
    }

    public String getJumpMarketSwitch(){
        return getSharePreference().getString(KEY_JUMP_MARKET_SWITCH, "");
    }

    private static final String KEY_TRY_SPEED_UP = "key_try_speed_up";

    public void setTrySpeedUp(String switchConfig) {
        if (TextUtils.isEmpty(switchConfig)) {
            String oldValue = getTrySpeedUp();
            if (!TextUtils.isEmpty(oldValue)) {
                getEditor().remove(KEY_TRY_SPEED_UP).commit();
            }
            return;
        }
        try {
            if (switchConfig.length() != 3) {
                throw new Exception("switchConfig length must be 3");
            }
            for (int i = 0; i < switchConfig.length(); i++) {
                char charAt = switchConfig.charAt(i);
                if (charAt != '0' && charAt != '1') {
                    throw new Exception("switchConfig value must be like 0 or 1");
                }
            }
            getEditor().putString(KEY_TRY_SPEED_UP, switchConfig).commit();
        } catch (Exception e) {
            LogUtil.logD(TAG, String.format("setTrySpeedUp fail(%s)", switchConfig), e);
        }
    }

    private String getTrySpeedUp() {
        return getSharePreference().getString(KEY_TRY_SPEED_UP, null);
    }

    /**
     * 非会员是否尝试加速
     *TODO https://jira.n.xiaomi.com/browse/XS-15154 修复安全问题
     * 将dcdn去掉，走高速试用
     * @return
     */
    public boolean isTrySpeedUp() {
//        String switchUpStr = getTrySpeedUp();
//        boolean switchUp = false;
//        if (BuildUtils.isAlphaVersion()) {
//            switchUp = getBoolean(0, switchUpStr);
//        } else if (BuildUtils.isDevVersion()) {
//            switchUp = getBoolean(1, switchUpStr);
//        } else if (BuildUtils.isStableVersion()) {
//            switchUp = getBoolean(2, switchUpStr);
//        }
        return false;
    }

    private static final String KEY_HOST_LAST_UPDATE_TIME = "key_host_last_update_time";

    public void saveHostLastUpdatetime(long lastUpdateTime) {
        getEditor().putLong(KEY_HOST_LAST_UPDATE_TIME, lastUpdateTime).commit();
    }

    public long getHostLastUpdatetime(long lastUpdateTime) {
        return getSharePreference().getLong(KEY_HOST_LAST_UPDATE_TIME, 0);
    }

    private boolean getBoolean(int index,String switchStr) {
        if (TextUtils.isEmpty(switchStr)) {
            return false;
        }
        if (index < 0 || index >= switchStr.length()) {
            return false;
        }
        return switchStr.charAt(index) == '1';
    }

    public void config(String method, String param) {
        if (TextUtils.equals(method, "setTrySpeedUp")) {
            setTrySpeedUp(param);
        } else {
            LogUtil.d("CloudConfigPreference", "without this method");
        }
    }

    private static final String KEY_LIMIT_SPEED = "key_limit_speed";
    public void setLimitSpeedConfig(String jsonConfig) {
        try {
            LimitSpeed.getInstance().setLimitTaskThrow(jsonConfig);
            getEditor().putString(KEY_LIMIT_SPEED, jsonConfig).commit();
        } catch (Exception e) {
            LogUtil.logD(LimitSpeed.TAG, String.format("setLimitSpeedConfig fail(%s)", jsonConfig), e);
        }
    }

    public String getLimitSpeedConfig() {
        return getSharePreference().getString(KEY_LIMIT_SPEED, null);
    }

    private static final String KEY_USE_XUNLEI_ENGINE = "key_use_xunlei_engine";

    public int getUseXunleiEngine() {
        int useXunleiEngine = -1;
        try {
            useXunleiEngine = getSharePreference().getInt(KEY_USE_XUNLEI_ENGINE, -1);
        } catch (Exception e) {
            LogUtil.forceD("getUseXunleiEngine: "+e.toString());
        }
        return useXunleiEngine;
    }

    public void setUseXunleiEngine(int useEngine) {
        //11-open,10-close,-1-close cfg
        if (useEngine == -1 || useEngine == 11 || useEngine == 10) {
            getEditor().putInt(KEY_USE_XUNLEI_ENGINE, useEngine).commit();
        }else if(useEngine == 0){//no cfg
            getEditor().remove(KEY_USE_XUNLEI_ENGINE).commit();
        }
    }


    private static final String KEY_DOWNLOAD_ENGINE = "key_download_engine";

    public DownloadEngineConfig getDownloadEngine(){
        String config = getSharePreference().getString(KEY_DOWNLOAD_ENGINE, null);
        DownloadEngineConfig downloadEngine = null;
        if (!TextUtils.isEmpty(config)) {
            try {
                downloadEngine = new Gson().fromJson(config, DownloadEngineConfig.class);
            } catch (Exception e) {
                LogUtil.forceD("getDownloadEngine: "+e.toString());
            }
        }
        return downloadEngine;
    }

    public void setDownloadEngine(DownloadEngineConfig downloadEngine) {
       if (downloadEngine == null) {
           DownloadEngineRule.getInstance().parseCloudAndConfig(null);
           getEditor().remove(KEY_DOWNLOAD_ENGINE).commit();
           return;
       }
       try {
           String config = new Gson().toJson(downloadEngine);
           getEditor().putString(KEY_DOWNLOAD_ENGINE, config).commit();
           DownloadEngineRule.getInstance().parseCloudAndConfig(downloadEngine);
       } catch (Exception e) {
           LogUtil.forceD("setDownloadEngine: "+e.toString());
       }
    }

    private static final String KEY_USE_XUNLEI_ENGINE_INR = "key_use_xunlei_engine_inr";
    public int getUseXunleiEngineInR() {
        int useXunleiEngine = -1;
        try {
            useXunleiEngine = getSharePreference().getInt(KEY_USE_XUNLEI_ENGINE_INR, -1);
        } catch (Exception e) {
            LogUtil.forceD("getUseXunleiEngineInR: "+e.toString());
        }
        return useXunleiEngine;
    }

    public void setUseXunleiEngineInR(int useEngine) {
        //11-open,10-close,-1-close cfg
        if (useEngine == -1 || useEngine == 11 || useEngine == 10) {
            getEditor().putInt(KEY_USE_XUNLEI_ENGINE_INR, useEngine).commit();
        }else if(useEngine == 0){//no cfg
            getEditor().remove(KEY_USE_XUNLEI_ENGINE_INR).commit();
        }
    }

    public  boolean useKcgEngine(String appCfg) {
        KCGLog.LogI(TAG, "useKcgEngine appCfg=" + appCfg);
        int kcgCfg = KcgHelper.getInstance().getKcgEngineCloudState();
        KCGLog.LogI(TAG, "useKcgEngine kcgCfg=" + kcgCfg);
        if (kcgCfg == 0 || kcgCfg == 1) {
            return kcgCfg == 1;
        }
        int useKcgEngine = DownloadExtra2.getUseKcgEngine(appCfg);
        KCGLog.LogI(TAG, "issIsKcgUsageOpen=" + KcgHelper.getInstance().issIsKcgUsageOpen() + ",useKcgEngine=" + useKcgEngine);
        return useKcgEngine == 1 && KcgHelper.getInstance().issIsKcgUsageOpen();
    }
    // END

    private static  final String KEY_JUMP_MARKET_BY_MIUIVERSION= "key_jump_market_by_miuiversion";
    public void setJumpMarketByMiuiVersion(String jumpMarketByMiuiVersion) {
        if (TextUtils.isEmpty(jumpMarketByMiuiVersion)) {
            String oldValue=getJumpMarketByMiuiVersion();
            if(TextUtils.isEmpty(oldValue)){

            }else{
                getEditor().remove(KEY_JUMP_MARKET_BY_MIUIVERSION).commit();
            }
            return;
        }
        if (jumpMarketByMiuiVersion.length() != 3) {
            LogUtil.forceD(String.format("setJumpMarketByMiuiVersion fail", jumpMarketByMiuiVersion));
            return;
        }
        getEditor().putString(KEY_JUMP_MARKET_BY_MIUIVERSION, jumpMarketByMiuiVersion).commit();
    }

    public String getJumpMarketByMiuiVersion() {
        return getSharePreference().getString(KEY_JUMP_MARKET_BY_MIUIVERSION, "");
    }

    private static final String KEY_SCDN_WHITE = "key_scdn_white";
    private HashSet<String> mScdnWhite;

    public void loadLocal() {
        setScdnWhite(getScdnWhite());
        setBigMemJson(getBigMem());
    }
    public void setScdnWhite(String jsonPkgs) {
        try {
            if (TextUtils.isEmpty(jsonPkgs)) {
                mScdnWhite = null;
                return;
            }
            if (mScdnWhite == null) {
                mScdnWhite = new HashSet<String>();
            }
            mScdnWhite.clear();
            JSONArray blackArray = new JSONArray(jsonPkgs);
            for (int i = 0; i < blackArray.length(); i++) {
                String pkg = (String) blackArray.get(i);
                mScdnWhite.add(pkg);
            }
            XLConfig.LOGD(String.format("setScdnWhite jsonPkgs(%s) pkgs(%s)", String.valueOf(jsonPkgs),
                    mScdnWhite.toString()));
            getEditor().putString(KEY_SCDN_WHITE, jsonPkgs).commit();
        } catch (Exception e) {
            XLConfig.LOGD_INFO(String.format("setScdnWhite ex json(%s)", String.valueOf(jsonPkgs)), e);
        }
    }

    public boolean useScdn(String pkg) {
        if (mScdnWhite == null) {
            return true;
        }
        if(mScdnWhite.contains("all")){
            return true;
        }
        return mScdnWhite.contains(pkg);
    }

    public boolean hasScdnCloud() {
        return mScdnWhite != null;
    }

    private String getScdnWhite() {
        return getSharePreference().getString(KEY_SCDN_WHITE, "");
    }

    private final String KEY_BIG_MEM = "big_mem";
    private final String KEY_DEV = "dev";
    private final String KEY_STABLE = "stable";

    /**
     * json格式如下
     * big_mem:{"dev":["all"],"stable":["lavender",...]}
     * @param jsonPkgs
     */
    public void setBigMemJson(String jsonPkgs) {
        try {
            int enterOrNot = 0;
            if (!TextUtils.isEmpty(jsonPkgs)) {
                JSONObject jsonObject = new JSONObject(jsonPkgs);
                if (jsonObject != null) {
                    HashMap<String, HashSet<String>> map = new HashMap<String, HashSet<String>>();
                    bigMemJsonToMap(jsonObject, map, KEY_DEV);
                    bigMemJsonToMap(jsonObject, map, KEY_STABLE);

                    //判断是否开启旗舰模式
                    String deviceName = BuildUtils.getDeviceInterName();
                    String bigMiuiVersion = BuildUtils.getBigMiuiVersion();
                    HashSet<String> devices = map.get(bigMiuiVersion);
                    if (devices != null && devices.size() > 0) {
                        if (devices.contains("all")) {
                            enterOrNot = 1;
                        } else if (devices.contains(deviceName)) {
                            enterOrNot = 1;
                        } else {
                            enterOrNot = 0;
                        }
                    }
                    XLConfig.LOGD(String.format("setBigMemJson jsonPkgs(%s)", String.valueOf(jsonPkgs)));
                }
            }
            setBigMem(jsonPkgs);
            XlDownloadManagerHelper.xlEnterUltimateSpeed(enterOrNot);
        } catch (Exception e) {
            XLConfig.LOGD_INFO(String.format("setBigMemJson ex json(%s)", String.valueOf(jsonPkgs)), e);
        }
    }

    private void bigMemJsonToMap(JSONObject jsonObject, HashMap<String, HashSet<String>> map, String key_stable) {
        JSONArray stableList = jsonObject.optJSONArray(key_stable);
        if (stableList != null && stableList.length() > 0) {
            HashSet<String> value = new HashSet<String>();
            for (int i = 0; i < stableList.length(); i++) {
                String d = stableList.optString(i);
                if (!TextUtils.isEmpty(d)) {
                    value.add(d);
                }
            }
            map.put(key_stable, value);
        }
    }

    public String getBigMem() {
        return getSharePreference().getString(KEY_BIG_MEM, "");
    }

    public void setBigMem(String jsonPkgs) {
        getEditor().putString(KEY_BIG_MEM, (jsonPkgs == null ? "" : jsonPkgs)).apply();
    }

    private final String KEY_API_SWITCH = "switch_api";
    private final String KEY_PROCESS_START_SWITCH = "switch_PROCESS_START";
    private final String KEY_SERVICE_START_SWITCH = "switch_SERVICE_START";
    private final String KEY_METHOD_CALL_SWITCH = "switch_method_call";
    private final String KEY_UP_ONETRACK = "up_onetrack";
    private final String KEY_CONNS_REPORT_ONETRACK_SWITCH = "switch_conns_report_onetrack";
    private final String KEY_CONNS_REPORT_HUB_SWITCH = "switch_conns_report_hub";
    public void setTraceApiSwitch(String switchStr) {
        getEditor().putString(KEY_API_SWITCH, (switchStr == null ? "" : switchStr)).apply();
    }

    public String traceApiSwitch(){
        return getSharePreference().getString(KEY_API_SWITCH, "");
    }

    public void setTraceServiceStartSwitch(String switchStr) {
        getEditor().putString(KEY_SERVICE_START_SWITCH, (switchStr == null ? "" : switchStr)).apply();
    }

    public String traceServiceStartSwitch(){
        return getSharePreference().getString(KEY_SERVICE_START_SWITCH, "");
    }

    public void setTraceProcessStartSwitch(String switchStr) {
        getEditor().putString(KEY_PROCESS_START_SWITCH, (switchStr == null ? "" : switchStr)).apply();
    }

    private String traceProcessStartSwitch(){
        return getSharePreference().getString(KEY_PROCESS_START_SWITCH, "");
    }

    public void setTraceMethodSwitch(String switchStr) {
        getEditor().putString(KEY_METHOD_CALL_SWITCH, (switchStr == null ? "" : switchStr)).apply();
    }

    private String traceMethodSwitch(){
        return getSharePreference().getString(KEY_METHOD_CALL_SWITCH, "");
    }

    public boolean traceMethodOpen(){
        return isSwitchOpen(traceMethodSwitch(),false);
    }

    public boolean traceProcessStartOpen(){
        return isSwitchOpen(traceProcessStartSwitch(),false);
    }

    public boolean traceServiceStartOpen(){
        return isSwitchOpen(traceServiceStartSwitch(),false);
    }

    public boolean traceApiOpen(){
        return isSwitchOpen(traceApiSwitch(),false);
    }

    public boolean isSwitchOpen(String switchStr,boolean defValue) {
        if (TextUtils.isEmpty(switchStr)) {
            return defValue;
        }
        if (switchStr.length() != 3) {
            return defValue;
        }
        if (TextUtils.equals("111", switchStr)) {
            return true;
        }
        try {
            if (BuildUtils.isAlphaVersion()) {
                return "1".equals(String.valueOf(switchStr.charAt(0)));
            } else if (BuildUtils.isDevVersion()) {
                return "1".equals(String.valueOf(switchStr.charAt(1)));
            } else if (BuildUtils.isStableVersion()) {
                return "1".equals(String.valueOf(switchStr.charAt(2)));
            }
        } catch (Exception e) {
            LogUtil.forceD("isSwitchOpen fail:" + switchStr);
        }
        return defValue;
    }

    private final String KEY_TOKEN_FAIL_UPDATE_INTERVAL = "token_fail_update_interval";
    private final static long DAY = 24*60*60;
    public long getTokenFailUpdateInterval(){
        return getSharePreference().getLong(KEY_TOKEN_FAIL_UPDATE_INTERVAL, DAY);
    }

    public void setTokenFailUpdateInterval(long updateInterval) {
        getEditor().putLong(KEY_TOKEN_FAIL_UPDATE_INTERVAL, updateInterval).apply();
    }

    private final String KEY_LIMIT_SPEED_CONFIG2 = "limit_speed_config2";
    public String getLimitSpeedConfig2(){
        return getSharePreference().getString(KEY_LIMIT_SPEED_CONFIG2, "");
    }

    public void setLimitSpeedConfig2(String jsonObj) {
        getEditor().putString(KEY_LIMIT_SPEED_CONFIG2, jsonObj).apply();
    }
    public void setUpOneTrack(String str) {
        getEditor().putString(KEY_UP_ONETRACK,str).apply();
    }

    public String getUpOneTrack() {
        return getSharePreference().getString(KEY_UP_ONETRACK,"");
    }

    public void enableConnsForOneTack(String str) {
        getEditor().putString(KEY_CONNS_REPORT_ONETRACK_SWITCH, str).apply();
    }

    public boolean trackConnsForOneTack() {
        String switchStr = getSharePreference().getString(KEY_CONNS_REPORT_ONETRACK_SWITCH, "");
        return isSwitchOpen(switchStr, true);
    }

    public void enableConnsForHub(String str) {
        getEditor().putString(KEY_CONNS_REPORT_HUB_SWITCH, str).apply();
    }

    public boolean trackConnsForHub() {
        String switchStr = getSharePreference().getString(KEY_CONNS_REPORT_HUB_SWITCH, "");
        return isSwitchOpen(switchStr, true);
    }

    public boolean traceOneTrackOpen() {
        return isSwitchOpen(getUpOneTrack(), true);
    }

    private final String KEY_SPEEDUP_ENABLE_SWITCH2 = "switch_speedup_enable2";

    public void setSpeedupSwitch(String switchStr) {
        getEditor().putString(KEY_SPEEDUP_ENABLE_SWITCH2, (switchStr == null ? "" : switchStr)).apply();
    }

    public String getSpeedupSwitch() {
        return getSharePreference().getString(KEY_SPEEDUP_ENABLE_SWITCH2, "");
    }

    public boolean enableSpeedup() {
        return isSwitchOpen(getSpeedupSwitch(), false);
    }

    private final String KEY_SWITCH_DATALIMIT = "switch_datalimit";
    public void setDataLimitSwitch(String switchStr) {
        getEditor().putString(KEY_SWITCH_DATALIMIT, (switchStr == null ? "" : switchStr)).apply();
    }

    public String getDataLimitSwitch() {
        return getSharePreference().getString(KEY_SWITCH_DATALIMIT, "");
    }

    public boolean enableDataLimit() {
        return isSwitchOpen(getDataLimitSwitch(), false);
    }

    private final String KEY_DATALIMIT_DELAYTIME = "datalimit_delaytime";
    public void setDataLimitDelayTime(String delayime) {
        getEditor().putString(KEY_DATALIMIT_DELAYTIME, delayime).apply();
    }

    public long getDataLimitDelayTime() {
        String timeStr = getSharePreference().getString(KEY_DATALIMIT_DELAYTIME, String.valueOf(24*60*60*1000));
        long time = 0;
        try {
            time = Long.parseLong(timeStr);
        } catch (Exception e){}
        return time;
    }

    private final String KEY_DATALIMIT_LASTTIME = "datalimit_lasttime";
    public void updateDataLimitLastTime(long time) {
        getEditor().putLong(KEY_DATALIMIT_LASTTIME, time).apply();
    }

    public long getDataLimitLastTime() {
        return getSharePreference().getLong(KEY_DATALIMIT_LASTTIME, 0);
    }

    private final String KEY_CHECK_FILE_SIZE_SWITCH = "switch_check_file_size";

    public void setCheckFileSizeSwitch(String switchStr) {
        getEditor().putString(KEY_CHECK_FILE_SIZE_SWITCH, (switchStr == null ? "" : switchStr)).apply();
    }

    private String getCheckFileSizeSwitch() {
        return getSharePreference().getString(KEY_CHECK_FILE_SIZE_SWITCH, "");
    }

    public boolean enableCheckFileSize() {
        return isSwitchOpen(getCheckFileSizeSwitch(), false);
    }

    private final String KEY_FEATURE_SWITCHS = "feature_switchs";
    public void setFeatureSwitchs(List<String> featureSwitchs) {
        ArraySet<String> strings = new ArraySet<>();
        if (featureSwitchs != null && featureSwitchs.size() > 0) {
            for(String featureSwitch : featureSwitchs) {
                strings.add(featureSwitch);
            }
        }
        getEditor().putStringSet(KEY_FEATURE_SWITCHS, strings).commit();
        FeatureSwitch.resetFeature();
    }

    public Set<String> getFeatureSwitchs() {
        return getSharePreference().getStringSet(KEY_FEATURE_SWITCHS,null);
    }

    private final String KEY_WAKELOCK_EXPOSED = "wakelock_exposed";
    public void setWakelockExposed(boolean wakelockExposed) {
        getEditor().putBoolean(KEY_WAKELOCK_EXPOSED, wakelockExposed).apply();
    }
    public boolean isWakelockExposed() {
        return getSharePreference().getBoolean(KEY_WAKELOCK_EXPOSED, false);
    }

    public  String toString() {
        StringBuffer sb = new StringBuffer();
        CloudConfigPreference cfPreference = getInstance();
        sb.append("fileSizeLimit=").append(cfPreference.getAccelerateFileSizeLimit())
                .append(",fileDownloadPercent=").append(cfPreference.getAccelerateFilePercent())
                .append(",accelerateCount=").append(cfPreference.getAccelerateCount())
                .append(",accelerateDration=").append(cfPreference.getAccelerateDration())
                .append(",resetDuration=").append(cfPreference.getResetDuration())
                .append(",lowSpeedDuration=").append(cfPreference.getLowSpeedDuration())
                .append(",lowSpeed=").append(cfPreference.getLowSpeedLimit())
                .append(",getAcceleteSwitch=").append(cfPreference.getAcceleteSwitch())
                .append(",notifyRecommendSwitch=").append(cfPreference.getNotifyRecommedSwitch())
                .append(",notifyRecommendRecType=").append(cfPreference.getNotifyRecommedRecType())
                .append(",getNotifyRecommedImeiSwitch=").append(cfPreference.getNotifyRecommedImeiSwitch())
                .append(",NotifyRecommendDayCount=").append(cfPreference.getNotifyRecommedDayCount())
                .append(",key_notify_recommed_duration=").append(cfPreference.getNotifyRecommedDuration())
                .append(",getNotifyRecommedNetworkType=").append(cfPreference.getNotifyRecommedNetworkType())
                .append(",isDevShowActivateNotify=").append(cfPreference.isDevShowActivateNotify())
                .append(",isStableShowActivateNotify=").append(cfPreference.isStableShowActivateNotify())
                .append(",getBlackListShowTask=").append(cfPreference.getBlackListShowTask())
                .append(",getBlackListNotifyFilter=").append(cfPreference.getBlackListNotifyFilter())
                .append(",getDataLimitConfig=").append(cfPreference.getDataLimitConfig())
                .append(",getJumpMarketSwitch=").append(cfPreference.getJumpMarketSwitch())
                .append(",getTrySpeedUp=").append(cfPreference.getTrySpeedUp())
                .append(",useXunleiEngine=").append(cfPreference.getUseXunleiEngine())
                .append(",useXunleiEngineInR=").append(cfPreference.getUseXunleiEngineInR())
                .append(",scdnWhite=").append(cfPreference.getScdnWhite())
                .append(",traceProcessStartSwitch=").append(cfPreference.traceProcessStartSwitch())
                .append(",traceServiceStartSwitch=").append(cfPreference.traceServiceStartSwitch())
                .append(",traceApiSwitch=").append(cfPreference.traceApiSwitch())
                .append(",traceMethodSwitch=").append(cfPreference.traceMethodSwitch())
                .append(",traceUpOneTrack=").append(cfPreference.getUpOneTrack())
                .append(",trackConnsForOneTack=").append(cfPreference.trackConnsForOneTack())
                .append(",trackConnsForHub=").append(cfPreference.trackConnsForHub())
                .append(",tokenFailUpdateInterva=").append(cfPreference.getTokenFailUpdateInterval())
                .append(",bigmem=").append(cfPreference.getBigMem())
                .append(",enableSpeedup=").append(cfPreference.enableSpeedup())
                .append(",enableCheckFileSize=").append(cfPreference.enableCheckFileSize())
                .append(",limitSpeedConfig2=").append(cfPreference.getLimitSpeedConfig2())
                .append(",feature_switchs=").append(cfPreference.getFeatureSwitchs());
        return sb.toString();
    }

    public static void toDesc() {
        CloudConfigPreference cfPreference = getInstance();
        XLConfig.LOGD("CloudControl", "current config  >>" + cfPreference.toString());
    }

}
