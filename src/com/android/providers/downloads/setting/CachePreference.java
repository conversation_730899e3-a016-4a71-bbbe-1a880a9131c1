package com.android.providers.downloads.setting;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.alarm.LimitSpeed;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.DataUsageHelper;
import com.android.providers.downloads.util.DownloadExtra2;
import com.android.providers.downloads.util.XLUtil;
import com.android.providers.downloads.util.XlDownloadManagerHelper;
import com.android.providers.downloads.utils.LogUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.HashSet;

public class CachePreference {
    public static final String TAG = "CachePreference";
    private static final String SP_NAME = "CachePreference";

    private static final String KEY_IMEI = "key_imei";
    private static final String KEY_DEVICEID = "key_deviceid";
    private static final String KEY_PHONEINFO_VERSION = "key_phoneinfo_version";

    private volatile static CachePreference mInstance;


    public static CachePreference getInstance(){

        if(mInstance == null){
            synchronized (CachePreference.class){
                if(mInstance == null){
                    mInstance = new CachePreference();
                }
            }
        }

        return mInstance;
    }

    private CachePreference() {
    }

    private SharedPreferences getSharePreference() {
        Context context = DownloadApplication.getGlobalApplication();
        return context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
    }

    private SharedPreferences.Editor getEditor() {
        return getSharePreference().edit();
    }

    public void saveImei(String imei) {
        SharedPreferences.Editor editor = getEditor();
        editor.putString(KEY_IMEI, imei);
        editor.apply();
        savePhoneInfoVersion();
    }

    public String getImei() {
        if (!isPhoneInfoVersionSame()) {
            return null;
        }
        SharedPreferences sharePreference = getSharePreference();
        if (sharePreference.contains(KEY_IMEI)) {
            return sharePreference.getString(KEY_IMEI,"");
        } else {
            return null;
        }
    }

    public void saveDeviceId(String id) {
        SharedPreferences.Editor editor = getEditor();
        editor.putString(KEY_DEVICEID, id);
        editor.apply();
        savePhoneInfoVersion();
    }

    public String getDeviceId() {
        if (!isPhoneInfoVersionSame()) {
            return null;
        }
        SharedPreferences sharePreference = getSharePreference();
        if (sharePreference.contains(KEY_DEVICEID)) {
            return sharePreference.getString(KEY_DEVICEID,"");
        } else {
            return null;
        }
    }

    private void savePhoneInfoVersion() {
        Context globalContext = DownloadApplication.getGlobalApplication();
        if (globalContext != null) {
            String versionCode = XLUtil.getAppVersionCode(globalContext.getPackageName());
            SharedPreferences.Editor editor = getEditor();
            editor.putString(KEY_PHONEINFO_VERSION, versionCode);
            editor.apply();
        }
    }

    private boolean isPhoneInfoVersionSame() {
        Context globalContext = DownloadApplication.getGlobalApplication();
        if (globalContext != null) {
            String versionCode = XLUtil.getAppVersionCode(globalContext.getPackageName());
            String v = getSharePreference().getString(KEY_PHONEINFO_VERSION, "");
            if (v.equals(versionCode)) {
                return true;
            }
        }
        return false;
    }


}
