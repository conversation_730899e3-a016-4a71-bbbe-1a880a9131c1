package com.android.providers.downloads.kcg;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.INetworkPolicyListener;
import android.net.Network;
import android.net.NetworkInfo;
import android.net.NetworkPolicyManager;
import android.net.TrafficStats;
import android.net.Uri;
import android.os.Process;
import android.os.SystemClock;
import android.provider.Downloads;
import android.security.NetworkSecurityPolicy;
import android.security.net.config.ApplicationConfig;
import android.telephony.SubscriptionPlan;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;

import com.android.providers.downloads.config.Constants;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.kcg.utils.KCGLog;
import com.android.providers.downloads.kcg.utils.KcgHelper;
import com.android.providers.downloads.service.DownloadNotifier;
import com.android.providers.downloads.service.DownloadInfo;
import com.android.providers.downloads.DownloadThread;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.SystemFacade;
import com.android.providers.downloads.exception.StopRequestException;
import com.android.providers.downloads.model.State;
import com.android.providers.downloads.statistics.TraceReport;
import com.android.providers.downloads.StorageManager;

import java.io.File;
import java.io.FileNotFoundException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

import com.android.providers.downloads.xunlei.speedup.XLSpeedUpManager;
//import com.ksyun.pp.DownloadConfig;
//import com.ksyun.pp.KCGConfig;
//import com.ksyun.pp.KCGService;
//import com.ksyun.pp.KCGDownload;
//import com.ksyun.pp.TaskInfo;

import miui.os.Build;

public class KCGDownloadThread extends DownloadThread implements KCGDownloadCfg {

    public static final String TAG = KCGDownloadThread.class.getSimpleName();

    protected final Context mContext;

    protected final long mId;
    protected File mDownloadDestFile;

//    private KCGDownload mKCGDownload;
//    private DownloadConfig mDownloadConfig;
    private String mTaskId;
    private State mState;
    //查询任务进度时回调
//    private TaskInfo mTaskInfo;
    private volatile boolean mTaskInfoInitFail = false;
    private volatile int mErrorId;

    private long loopStart = 0;// 统计方法调用时间用,记录轮询开始时间
    private boolean isReport = false;// 统计方法调用时间用,记录是否记录过

    /** Flag indicating that thread must be halted */
    protected volatile boolean mShutdownRequested;
    private int samplingInterval = 10;//用来控制进度日志采样输出
    private int count = 0;
    private boolean needShowProgressLog = false;

    public KCGDownloadThread(Context context, SystemFacade systemFacade, DownloadInfo info,
                             StorageManager storageManager, DownloadNotifier notifier) {
        super(context, systemFacade, info, storageManager, notifier);
        mContext = context;

        mId = info.mId;
        mTaskId = String.valueOf(mId);
        KCGLog.LogI(TAG, "new  KCGDownloadThread mId:" + mId + ",mTaskId:" + mTaskId);
        if (!KcgHelper.getInstance().isKCGEngineInit()) {
            KcgHelper.getInstance().initKCGEngine(mContext);
        }
//        mKCGDownload = KcgHelper.getInstance().getKCGDownloadIns();
//        mDownloadConfig = KcgHelper.getInstance().getKCGDownloadConfig();
    }

    protected Context getContext() {
        return mContext;
    }

    protected long getDownloadId() {
        return mInfo.mId;
    }

    protected String getTaskId() {
        return mTaskId;
    }

    @Override
    protected void preDownload() {
        super.preDownload();
    }

    @Override
    protected void executeDownload(State state) throws StopRequestException {
        if (state == null) {
            throwErrorException("executeDownload error", KCGDownloadCfg.TASK_NO_CREATE);
        }
        try {
            long start = System.currentTimeMillis();
            Statistics.printCallTime("executeDownload", start, start, mInfo.mId);
            if (state.mTotalBytes != 0 && state.mCurrentBytes == state.mTotalBytes) {
                KCGLog.LogI(TAG, "executeDownload Skipping initiating request for download "
                        + mInfo.mId + "; already completed");
                return;
            }
            mState = state;
            state.mContinuingDownload = state.mCurrentBytes > 0;
            NetworkInfo info = mSystemFacade.getActiveNetworkInfo();
            // only do this proc in mobile network and new task
            if (info != null && info.getType() == ConnectivityManager.TYPE_MOBILE
                    && !state.mContinuingDownload && mInfo.mBypassRecommendedSizeLimit == 0) {
                Long recommendedMaxBytesOverMobile = mSystemFacade.getRecommendedMaxBytesOverMobile();
                if (!((recommendedMaxBytesOverMobile != null && recommendedMaxBytesOverMobile >= DownloadInfo.MAX_BYTES_OVER_MOBILE))) {
                    KCGLog.LogE(Constants.TAG,
                            "DownloadThread executeDownload_kcg ---> checkFileSizeinMobile fun called!");
                    mIfMobileFileSizeChecked = true;
                    checkFileSizeinMobile(state);
                }
            }
            checkConnectivity(true);
            startTask(state);

            loopProgress(state);
        } catch (Exception ex) {
            if (ex instanceof StopRequestException) {
                throw new StopRequestException(((StopRequestException) ex).getFinalStatus(), ex.getMessage());
            } else {
                throw new StopRequestException(Downloads.Impl.STATUS_UNKNOWN_ERROR, "executeDownload error");
            }
        }
    }


    private void startTask(State state) throws StopRequestException {
        if (state.mRequestUri == null) {
            throwErrorException("Download url is null.", KCGDownloadCfg.TASK_URL_IS_NULL);
        }
        try {
            /*String path = "";
            if (mIfMobileFileSizeChecked || state.mContinuingDownload) {
                File downloadFile = new File(state.mFilename);
                path = downloadFile.getParent();
            }
            KCGLog.LogI(TAG, "startTask path=" + path + ",mContinuingDownload=" + state.mContinuingDownload);
            if (state.mContinuingDownload) {
                mKCGDownload.resumeTask(mTaskId, state.mRequestUri.toString(), mResumeTaskCallback);
            } else {
                processResponseHeaders(state, null);
                if (!TextUtils.isEmpty(state.mFilename)) {
                    File downloadFile = new File(state.mFilename);
                    path = downloadFile.getParent();
                }
                KCGLog.LogI(TAG, "startTask path=" + path + ",mFilename=" + state.mFilename);
                mKCGDownload.addTask(mTaskId, state.mRequestUri.toString(), state.mFilename, mDownloadConfig, mAddTaskCallback);
            }*/
        } catch (Exception ex) {
            if (ex instanceof StopRequestException) {
                throw new StopRequestException(((StopRequestException) ex).getFinalStatus(), ex.getMessage());
            } else {
                throw new StopRequestException(Downloads.Impl.STATUS_UNKNOWN_ERROR, "loopProgress error");
            }
        }
    }

    private void loopProgress(State state) throws StopRequestException {
        loopStart = System.currentTimeMillis();
        /*for (;;) {
            checkPausedOrCanceled(state);
            checkFileExists(mDownloadDestFile, state);
            try {
                Thread.sleep(1000);

                if (mTaskInfoInitFail) {
                    KcgHelper.getInstance().increaseDownloadErrorCount(mTaskId);
                    if (mErrorId == KCGDownloadCfg.KCG_NO_START_FAILED) {
                        KcgHelper.getInstance().resetKCGEngineInitStatus();
                        KcgHelper.getInstance().initKCGEngine(mContext);
                    }
                    break;
                }

                if(mInfo.mDeleted || mInfo.mStatus == Downloads.Impl.STATUS_CANCELED) {
                    mKCGDownload.deleteTask(mTaskId, false, mDeleteTaskCallback);
                } else {
                    mKCGDownload.queryTaskByID(mTaskId, mQueryTaskCallback);
                }

                if(mTaskInfo != null) {
                    reportTask(state, mTaskInfo);
                }
                reportProgress(state);

                if(mTaskInfo != null && (mTaskInfo.getState() == TaskInfo.FAILED || mTaskInfo.getState() ==  TaskInfo.DONE)) {
                    mDownloadDestFile = new File(mState.mFilename);
                    KCGLog.LogI(TAG, "loopProgress mDownloadDestFile=" + mDownloadDestFile.getAbsolutePath()
                            +",state=" + mTaskInfo.getState());
                    if (mTaskInfo.getState() == TaskInfo.FAILED && mInfo.mStatus != Downloads.Impl.STATUS_CANCELED && !mInfo.mDeleted) {
                        KcgHelper.getInstance().increaseDownloadErrorCount(mTaskId);
                        mKCGDownload.deleteTask(mTaskId, false, mDeleteTaskCallback);
                    } else if (mTaskInfo.getState() == TaskInfo.DONE) {
                        KcgHelper.getInstance().clearKcgDownloadErrorCount();
                        mKCGDownload.deleteTask(mTaskId, false, mDeleteTaskCallback);
                    }
                    if (!mState.mContinuingDownload && !mIfMobileFileSizeChecked) {
//                    processResponseHeaders(mState, null);
                        if (!BuildUtils.isTablet() && !Helpers.isEnglishEnv(mContext)
                                && !Helpers.isInternationalBuilder()) {
                            Intent intent = new Intent(XLConfig.ACTION_NOTIFICATION_WITHOUT_ENGINE);
                            intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                            mContext.sendBroadcast(intent);
                        }
                    }
                    break;
                }
                checkSpace(state,mTaskInfo);
            } catch (Exception e) {
                if (e instanceof StopRequestException) {
                    throw new StopRequestException(((StopRequestException) e).getFinalStatus(), e.getMessage());
                } else {
                    throw new StopRequestException(Downloads.Impl.STATUS_UNKNOWN_ERROR, "loopProgress error");
                }
            }
        }*/
    }

    protected void processResponseHeaders(State state, HttpURLConnection conn)
            throws StopRequestException {
        // TODO: fallocate the entire file if header gave us specific length

        try {
            if (conn != null) {
                readResponseHeaders(state, conn);
            }
            //对于下载库而言不需要生成下载中(*.download)的文件
            state.mFilename = Helpers.generateSaveFile(
                    mContext,
                    mInfo.mUri,
                    mInfo.mHint,
                    state.mContentDisposition,
                    state.mContentLocation,
                    state.mMimeType,
                    mInfo.mDestination,
                    state.mContentLength,
                    mStorageManager,
                    false,
                    needFixExtension(mInfo.mPackage));
            // correct mimetype
            correctMimeType(state);
            // update header values into database
            updateDatabaseFromHeaders(state);
            // now we get filename, and check space.
            mStorageManager.verifySpace(mInfo.mDestination, state.mFilename, state.mTotalBytes);
            // check connectivity again now that we know the total size
            checkConnectivity(true);
        } catch (StopRequestException stopException) {
            throw new StopRequestException(stopException.getFinalStatus(), stopException.getMessage());
        } catch (Exception ex) {
            throw new StopRequestException(Downloads.Impl.STATUS_UNKNOWN_ERROR, "processResponseHeaders error");
        }
    }

    @Override
    protected void stopTask() throws StopRequestException {
        super.stopTask();
    }

    @Override
    protected void stopTask(int finalStatus, int ret) throws StopRequestException {
        super.stopTask(finalStatus, ret);
        KCGLog.LogI(TAG, "stopTask finalStatus=" + finalStatus + ",ret=" + ret);
        //以下载库的错误码为主
        int finalRet = -1;
        if (finalStatus == XL_STATUS__FAIL) {
            finalRet = ret;
        } else {
            finalRet = finalStatus;
        }
        if (finalRet == 0) {//一些异常情况下没有已知的错误码的情况下，用原生的状态码上报
            finalRet = finalStatus;
        }
    }

    //为了方便统计，一定要有错误码,这个是自定义的错误码
    protected void throwErrorException(String msg, int errorCode) throws StopRequestException {
        throw new StopRequestException(XL_STATUS__FAIL, errorCode, String.format(msg + "(%d)", errorCode));
    }

    /*private void reportTask(State state, TaskInfo taskInfo) {
        if (state == null || taskInfo == null) {
            return;
        }
        state.mCurrentBytes = taskInfo.getFinishedSize();
        state.mTotalBytes = taskInfo.getSize();
        state.mDownloadingCurrentSpeed = taskInfo.getDownloadRateLastest();
        if (!isReport && state.mCurrentBytes > 0) {
            long end = System.currentTimeMillis();
            Statistics.printCallTime("reportTask", loopStart, end, mInfo.mId, "mTaskId=" + mTaskId);
            isReport = true;
        }
        if (!XLConfig.isDebug()) {
            needShowProgressLog = false;
            if (count == samplingInterval) {
                needShowProgressLog = true;
                count = 0;
            }
            count++;
        } else {
            needShowProgressLog = true;
        }
        if(needShowProgressLog) {
            StringBuilder logStr = new StringBuilder();
            logStr.append("loopProgress "
                    + " downloadId:" + getDownloadId()
                    + " taskid:" + taskInfo.getTaskid()
                    + ",mDownloadSize:" + taskInfo.getFinishedSize()
                    + ",filesize:" + taskInfo.getSize()
                    + ",taskstatus:" + taskInfo.getState()
                    + ",filepath:" + taskInfo.getFilepath()
                    + ",filepathTmp:" + taskInfo.getFilepathTmp()
                    + "name:" + taskInfo.getName()
                    + ",url:" + taskInfo.getUrl()
                    + ",progress:" + taskInfo.getProgress()
                    + ",errorcode:" + taskInfo.getErrorCode());
            logStr.append("\n");
            logStr.append(",downloadRateLastest:" + taskInfo.getDownloadRateLastest()
                    + ",downloadRate:" + taskInfo.getDownloadRate()
                    + ",dsratio:" + taskInfo.getDsratio());
            logStr.append("\n");
            logInfo(logStr.toString());
        }
    }*/

    /*protected void checkSpace(State state, TaskInfo taskInfo) throws StopRequestException {
        if (state == null || taskInfo == null) {
            return;
        }
        long needSpace = 0;
        if (state.mTotalBytes < 0) {
            needSpace = taskInfo.getSize();
        } else {
            needSpace = state.mTotalBytes - state.mCurrentBytes;
        }
        if (needSpace > 0) {
            mStorageManager.verifySpace(mInfo.mDestination, state.mFilename,
                    needSpace);
        }
    }*/

    /*private KCGDownload.Callback<String> mAddTaskCallback = new KCGDownload.Callback<String>() {

        @Override
        public void onSuccess(String taskID, String s2) {
            mTaskInfoInitFail = false;
            if (!Helpers.isTablet() && !Helpers.isEnglishEnv(mContext)
                    && !Helpers.isInternationalBuilder()) {
                Intent intent = new Intent("com.downloads.notification.action.init");
                intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                mContext.sendBroadcast(intent);
            }
        }

        @Override
        public void onFailure(String taskID, int errorId, String errorMessage) {
            KCGLog.LogE(TAG, "mAddTaskCallback onFailure taskID=" + taskID + ",errorId="+errorId+",errorMessage="+errorMessage);
            parseRetryAfterHeaders(mState, null);
            mTaskInfoInitFail = true;
            mErrorId = errorId;
        }
    };*/

    /*private KCGDownload.Callback<String> mPauseTaskCallback = new KCGDownload.Callback<String>() {

        @Override
        public void onSuccess(String taskID, String s2) {
            mTaskInfoInitFail = false;
        }

        @Override
        public void onFailure(String taskID, int errorId, String errorMessage) {
            KCGLog.LogE(TAG, "mPauseTaskCallback onFailure errorId=" + errorId + ",errorMessage="+ errorMessage + ",taskID="+taskID);
            mTaskInfoInitFail = true;
            mErrorId = errorId;
        }
    };*/

    /*private KCGDownload.Callback<String> mResumeTaskCallback = new KCGDownload.Callback<String>() {

        @Override
        public void onSuccess(String taskID, String s2) {
            mTaskInfoInitFail = false;
        }

        @Override
        public void onFailure(String taskID, int errorId, String errorMessage) {
            KCGLog.LogE(TAG, "mResumeTaskCallback onFailure errorId=" + errorId + ",errorMessage="+ errorMessage+ ",taskID="+taskID);
            mTaskInfoInitFail = true;
            mErrorId = errorId;
        }
    };*/

    /*private KCGDownload.Callback<String> mDeleteTaskCallback = new KCGDownload.Callback<String>() {

        @Override
        public void onSuccess(String taskID, String s2) {
        }

        @Override
        public void onFailure(String taskID, int errorId, String errorMessage) {
            KCGLog.LogE(TAG, "mDeleteTaskCallback onFailure errorId=" + errorId + ",errorMessage="+ errorMessage + ",taskID="+taskID);
        }
    };*/

    /*private KCGDownload.Callback<TaskInfo> mQueryTaskCallback = new KCGDownload.Callback<TaskInfo>() {

        @Override
        public void onSuccess(String taskID, TaskInfo taskInfo) {
            mTaskInfoInitFail = false;
            mTaskInfo = taskInfo;
        }

        @Override
        public void onFailure(String taskID, int errorId, String errorMessage) {
            mTaskInfoInitFail = true;
            KCGLog.LogE(TAG, "mQueryTaskCallback onFailure errorId=" + errorId + ",errorMessage="+ errorMessage + ",taskID="+taskID);
            mErrorId = errorId;
        }
    };*/

    @Override
    protected void checkStatePausedOrCanceled(State state) throws StopRequestException {
        synchronized (mInfo) {
            if (mInfo.mControl == Downloads.Impl.CONTROL_PAUSED) {
//                mKCGDownload.pauseTask(mTaskId, mPauseTaskCallback);
                throw new StopRequestException(Downloads.Impl.STATUS_PAUSED_BY_APP,
                        "download paused by owner");
            }
            if (mInfo.mStatus == Downloads.Impl.STATUS_CANCELED || mInfo.mDeleted) {
                throw new StopRequestException(Downloads.Impl.STATUS_CANCELED, "task_canceled");
            }

            if (mInfo.mStatus == Downloads.Impl.STATUS_QUEUED_FOR_WIFI) {
                throw new StopRequestException(Downloads.Impl.STATUS_QUEUED_FOR_WIFI,
                        mInfo.mErrorMsg);
            }

            if (mInfo.mStatus == Downloads.Impl.STATUS_WAITING_FOR_NETWORK) {
                throw new StopRequestException(Downloads.Impl.STATUS_WAITING_FOR_NETWORK,
                        mInfo.mErrorMsg);
            }
        }
    }

    protected void logDebug(String msg) {
        Log.d(TAG+"_KCG", "[" + mId + "] " + msg);
    }

    protected void logInfo(String msg) {
        Log.i(TAG+"_KCG", "[" + mId + "] " + msg);
    }

    protected void logWarning(String msg) {
        Log.w(TAG+"_KCG", "[" + mId + "] " + msg);
    }

    protected void logError(String msg, Throwable t) {
        Log.e(TAG+"_KCG", "[" + mId + "] " + msg, t);
    }

    protected void trackTaskStart(State state) {
        try {
            mDownloadFile = state.mFilename != null ? new File(state.mFilename) : null;
            long currentBytes = (mDownloadFile != null && mDownloadFile.exists()) ? mDownloadFile
                    .length()
                    : state.mCurrentBytes;

            String fileName = mDownloadFile != null ? mDownloadFile.getName() : "";
            mStartSize = currentBytes > 0 ? currentBytes : 0;
            mStartTime = System.currentTimeMillis();
            Statistics.trackKCGDownloadStart(mContext, state, true, fileName, mStartSize, wakelockTime);
        }catch (Exception e) {
            KCGLog.LogE(TAG, "trackTaskStart " + e.getMessage());
        }
    }

    protected void trackTaskStop(State state, int finalStatus, int soRet, String errorMsg) {
        try {
            mDownloadFile = state.mFilename != null ? new File(state.mFilename) : null;
            long fileSize = (mDownloadFile != null && mDownloadFile.exists()) ? mDownloadFile
                    .length() : state.mCurrentBytes;
            String fileName = mDownloadFile != null ? mDownloadFile.getName() : "";
            long currentBytes = fileSize > state.mCurrentBytes ? fileSize : state.mCurrentBytes;
            long downloadSize = currentBytes - mStartSize;
            long downloadTime = System.currentTimeMillis() - mStartTime;
            Statistics.trackKCGDownloadStop(mContext, state, true, fileName,
                    downloadTime, downloadSize, mStartSize, mInfo.mDeleted, finalStatus, soRet, errorMsg, wakelockTime);
        } catch (Exception e) {
            KCGLog.LogE(TAG, "trackTaskStop " + e.getMessage());
        }
    }
}
