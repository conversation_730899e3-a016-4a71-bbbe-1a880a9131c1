package com.android.providers.downloads.kcg.utils;

import android.util.Log;

public class KCGLog {
    public static final String TAG = "KCGDownload";
    private static boolean mIsLogOpen = true;

    public static void setLogOpen(boolean open) {
        mIsLogOpen = open;
    }
    public static void LogD(String tag, String message) {
        if (mIsLogOpen) {
            Log.d(TAG + "_" + tag, message);
        }
    }

    public static void LogI(String tag, String message) {
        if (mIsLogOpen) {
            Log.i(TAG + "_" + tag, message);
        }
    }

    public static void LogV(String tag, String message) {
        if (mIsLogOpen) {
            Log.v(TAG + "_" + tag, message);
        }
    }

    public static void LogE(String tag, String message) {
        Log.e(TAG + "_" + tag, message);
    }
}
