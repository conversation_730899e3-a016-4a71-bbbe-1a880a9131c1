package com.android.providers.downloads.utils;

import android.util.Log;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * Created by lxy on 2017/3/1.
 */
public class ReflectUtils {
    private static final String TAG = "ReflectUtils";

    public static class ReflAgent {

        private Class mClass;

        private Object mObject;

        private Object mResult;

        private ReflAgent() {

        }

        public static ReflAgent getClass(String clsStr) {
            ReflAgent reflAgent = new ReflAgent();
            try {
                reflAgent.mClass = Class.forName(clsStr);
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            }
            return reflAgent;
        }

        public static ReflAgent getObject(Object obj) {
            ReflAgent reflAgent = new ReflAgent();
            if (obj != null) {
                reflAgent.mObject = obj;
                reflAgent.mClass = obj.getClass();
            }
            return reflAgent;
        }

        public ReflAgent newObject(Class<?>[] parameterTypes, Object... values) {
            return this;
        }

        public ReflAgent call(String method, Class<?>[] parameterTypes, Object... values) {
            if (mObject != null) {
                mResult = callObjectMethod(mObject, method, parameterTypes, values);
            }
            return this;
        }

        public ReflAgent callStatic(String method, Class<?>[] parameterTypes, Object... values) {
            if (mClass != null) {
                mResult = callStaticMethod(mClass, method, parameterTypes, values);
            }
            return this;
        }

        public ReflAgent getStaticFiled(String field) {
            if (mClass != null) {
                mResult = getStaticField(mClass, field);
            }
            return this;
        }

        public ReflAgent getObjectFiled(String field) {
            if (mObject != null) {
                mResult = getObjectField(mObject, field);
            }
            return this;
        }


        public ReflAgent setResultToSelf() {
            mObject = mResult;
            mResult = null;
            return this;
        }

        public String stringResult() {
            if (mResult == null) {
                return null;
            }
            return mResult.toString();
        }

        public boolean booleanResult() {
            if (mResult == null) {
                return false;
            }
            return (Boolean) mResult;
        }

        public int intResult() {
            if (mResult == null) {
                return 0;
            }
            return (Integer) mResult;
        }

        public long longResult() {
            if (mResult == null) {
                return 0;
            }
            return (Long) mResult;
        }

    }

    public static Class<?> getClass(String name) throws ClassNotFoundException {
        return Class.forName(name);
    }

    public static <T> T callObjectMethod(Object target, String method, Class<T> returnType, Class<?>[] parameterTypes, Object... values) {
        try {
            return callObjectMethodOrThrow(target, method, returnType, parameterTypes, values);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "callObjectMethod error", e);
        }
        return null;
    }

    /**
     * @param target         调用目标对象
     * @param method         方法名称
     * @param parameterTypes 方法参数类型
     * @param returnType     返回类型
     * @param values         参数
     * @param <T>            反射调用返回值
     * @return
     * @throws NoSuchMethodException
     * @throws SecurityException
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws InvocationTargetException
     */
    public static <T> T callObjectMethodOrThrow(Object target, String method, Class<T> returnType, Class<?>[] parameterTypes, Object... values)
            throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
        Class<? extends Object> clazz = target.getClass();
        Method declaredMethod = clazz.getDeclaredMethod(method, parameterTypes);
        declaredMethod.setAccessible(true);
        return (T) declaredMethod.invoke(target, values);
    }

    public static Object callObjectMethod(Object target, String method, Class<?>[] parameterTypes, Object... values) {
        try {
            return callObjectMethodOrThrow(target, method, parameterTypes, values);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "callObjectMethod error", e);
        }
        return null;
    }


    public static Object callObjectMethodOrThrow(Object target, String method, Class<?>[] parameterTypes, Object... values)
            throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
        Class<? extends Object> clazz = target.getClass();
        Method declaredMethod = clazz.getDeclaredMethod(method, parameterTypes);
        declaredMethod.setAccessible(true);
        return declaredMethod.invoke(target, values);
    }

    public static Object callStaticMethod(Class<?> clazz, String method, Class<?>[] parameterTypes, Object... values) {
        try {
            return callStaticMethodOrThrow(clazz, method, parameterTypes, values);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "callStaticMethod error", e);
        }
        return null;
    }

    /**
     * 静态方法调用
     *
     * @param clazz
     * @param method
     * @param parameterTypes
     * @param values
     * @return
     * @throws NoSuchMethodException
     * @throws SecurityException
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws InvocationTargetException
     */
    public static Object callStaticMethodOrThrow(Class<?> clazz, String method, Class<?>[] parameterTypes, Object... values)
            throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
        Method declaredMethod = clazz.getDeclaredMethod(method, parameterTypes);
        declaredMethod.setAccessible(true);
        return declaredMethod.invoke(null, values);
    }

    public static void setObjectField(Object target, String field, Object value) {
        try {
            setObjectFieldOrThrow(target, field, value);
        } catch (Exception e) {
            Log.e(TAG, "setObjectField error", e);
        }
    }

    /**
     * 对象设置值
     *
     * @param target
     * @param field
     * @param value
     * @throws NoSuchFieldException
     * @throws SecurityException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static void setObjectFieldOrThrow(Object target, String field, Object value) throws NoSuchFieldException, SecurityException,
            IllegalArgumentException, IllegalAccessException {
        Class<? extends Object> clazz = target.getClass();
        Field declaredField = clazz.getDeclaredField(field);
        declaredField.setAccessible(true);
        declaredField.set(target, value);
    }

    public static Object getObjectField(Object target, String field) {
        try {
            return getObjectFieldOrThrow(target, field);
        } catch (Exception e) {
            Log.e(TAG, "getObjectField error", e);
        }
        return null;
    }

    /**
     * 对象获取值
     *
     * @param target
     * @param field
     * @return
     * @throws NoSuchFieldException
     * @throws SecurityException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static Object getObjectFieldOrThrow(Object target, String field) throws NoSuchFieldException, SecurityException,
            IllegalArgumentException, IllegalAccessException {
        Class<? extends Object> clazz = target.getClass();
        Field declaredField = clazz.getDeclaredField(field);
        declaredField.setAccessible(true);
        return declaredField.get(target);
    }

    public static <T> T getObjectField(Object target, String field, Class<T> returnType) {
        try {
            return getObjectFieldOrThrow(target, field, returnType);
        } catch (Exception e) {
            Log.e(TAG, "getObjectField error", e);
        }
        return null;
    }

    public static <T> T getObjectFieldOrThrow(Object target, String field, Class<T> returnType) throws NoSuchFieldException, SecurityException,
            IllegalArgumentException, IllegalAccessException {
        Class<? extends Object> clazz = target.getClass();
        Field declaredField = clazz.getDeclaredField(field);
        declaredField.setAccessible(true);
        return (T) declaredField.get(target);
    }

    public static void setStaticField(Class<?> clazz, String field, Object value) {
        try {
            setStaticFieldOrThrow(clazz, field, value);
        } catch (Exception e) {
            Log.e(TAG, "setStaticField error", e);
        }
    }

    /**
     * 静态变量设置值
     *
     * @param clazz
     * @param field
     * @param value
     * @throws NoSuchFieldException
     * @throws SecurityException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static void setStaticFieldOrThrow(Class<?> clazz, String field, Object value) throws NoSuchFieldException, SecurityException,
            IllegalArgumentException, IllegalAccessException {
        Field declaredField = clazz.getDeclaredField(field);
        declaredField.setAccessible(true);
        declaredField.set(null, value);
    }

    public static Object getStaticField(Class<?> clazz, String field) {
        try {
            return getStaticFieldOrThrow(clazz, field);
        } catch (Exception e) {
            Log.e(TAG, "getStaticField error", e);
        }
        return null;
    }

    /**
     * 静态变量获取值
     *
     * @param clazz
     * @param field
     * @return
     * @throws NoSuchFieldException
     * @throws SecurityException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     */
    public static Object getStaticFieldOrThrow(Class<?> clazz, String field) throws NoSuchFieldException, SecurityException,
            IllegalArgumentException, IllegalAccessException {
        Field declaredField = clazz.getDeclaredField(field);
        declaredField.setAccessible(true);
        return declaredField.get(null);
    }

    public static <T> T getStaticField(Class<?> clazz, String field, Class<T> returnType) {
        try {
            return getStaticFieldOrThrow(clazz, field, returnType);
        } catch (Exception e) {
            Log.e(TAG, "getStaticField error", e);
        }
        return null;
    }

    public static <T> T getStaticFieldOrThrow(Class<?> clazz, String field, Class<T> returnType) throws NoSuchFieldException,
            SecurityException,
            IllegalArgumentException, IllegalAccessException {
        Field declaredField = clazz.getDeclaredField(field);
        declaredField.setAccessible(true);
        return (T) declaredField.get(null);
    }

    public static int getReflactField(String className, String fieldName){
        int result = -1;
        try {
            Class<?> clz = Class.forName(className);
            Field field = clz.getField(fieldName);
            field.setAccessible(true);
            result = field.getInt(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
