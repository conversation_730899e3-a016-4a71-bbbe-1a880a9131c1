package com.android.providers.downloads.utils;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.util.SharePreferenceHelper;

import java.io.File;
import java.util.Arrays;
import java.util.HashMap;

import javax.xml.transform.Templates;

import xunlei.os.SystemProperties;

/**
 * Created by lxy on 2017/4/12.
 */
public class LogUtil {
    public static final String TAG = "DMS";
    private static boolean DEBUG_MANUAL = true;
    public static final String LOG_DIR = Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)
            ? Environment.getExternalStorageDirectory().getAbsolutePath() + "/.dlprovider"
            : null;
    public static String TAG_HTTP_UTIL = "HTTP_UTIL";
    public static String TAG_HOST_LOADER = "HOST_LOADER";
    public static String TAG_CLOUD_CONFIG_SD = "CLOUD_CONFIG_SD";
    public static String TAG_MI_STAT =  "MI_STAT_DP";
    public static String TAG_HUB_STAT =  "HUB_STAT_DP";
    public static  boolean IS_NOTIFY_TEST1 = new File(LOG_DIR, ".DP_NOTIFY_TEST1").exists();
    public static  boolean IS_NOTIFY_TEST2 = new File(LOG_DIR, ".DP_NOTIFY_TEST2").exists();
    public static  boolean IS_DEBUG_PICASSO = new File(LOG_DIR, ".DP_DEBUG_PICASSO").exists();
    public static  boolean IS_DEBUG_SPEEDUP = new File(LOG_DIR, ".debug_speedup").exists();
    public static  HashMap<String,Boolean> mTagMap =new HashMap<String,Boolean>();

    static {
        mapTag(TAG_HTTP_UTIL);
        mapTag(TAG_HOST_LOADER);
        mapTag(TAG_CLOUD_CONFIG_SD);
        mapTag(TAG_MI_STAT);
        mapTag(TAG_HUB_STAT);
    }

    private static void mapTag(String tag) {
        mTagMap.put(tag, new File(LOG_DIR, "." + tag).exists());
    }

    public static boolean debugTag(String tag) {
        Boolean value = mTagMap.get(tag);
        return value != null ? value.booleanValue() : false;
    }

    public static void d(String msg) {
        d(null, msg, null);
    }

    public static void d(String msg, Throwable e) {
        d(null, msg, e);
    }

    public static void d(String tag, String msg) {
        d(tag, msg, null);
    }

    public static void d(String tag, String msg, Throwable e) {
        XLConfig.LOGD(tag, msg, e);
    }

    public static void logD(String msg) {
        logD(null, msg, null);
    }

    public static void logD(String msg, Throwable e) {
        logD(null, msg, e);
    }

    public static void logD(String tag, String msg) {
        logD(tag, msg, null);
    }

    public static void logD(String tag, String msg, Throwable e) {
        XLConfig.LOGD(buildTag(tag), msg, e);
    }

    public static void forceD(String msg) {
        forceD(null, msg, null);
    }

    public static void forceD(String msg, Throwable e) {
        forceD(null, msg, e);
    }

    public static void forceD(String tag, String msg) {
        forceD(tag, msg, null);
    }

    public static void forceD(String tag, String msg, Throwable e) {
        XLConfig.LOGD_INFO(buildTag(tag), msg, e);
    }

    private static String buildTag(String tag) {
        return TextUtils.isEmpty(tag) ? TAG : TAG + "_" + tag;
    }

    public static long generateStartTime() {
        return System.currentTimeMillis();
    }

    public static long getCostTime(long startTime) {
        return System.currentTimeMillis() - startTime;
    }

    public static String getStackTrace() {
        return Arrays.toString(Thread.currentThread().getStackTrace());
    }

    public static boolean isNotifyTest1() {
        if (IS_NOTIFY_TEST1) {
            IS_NOTIFY_TEST2 = false;
        }
        return IS_NOTIFY_TEST1;
    }

    public static boolean isNotifyTest2() {
        if (IS_NOTIFY_TEST2) {
            IS_NOTIFY_TEST1 = false;
        }
        return IS_NOTIFY_TEST2;
    }

    public static boolean isDebugPicasso() {
        return IS_DEBUG_PICASSO;
    }

    public static void logTag(String tag, String msg) {
        logTag(tag, msg, null);
    }

    public static void logTag(String tag, String msg, Throwable e) {
        if (!debugTag(tag)) {
            return;
        }
        Log.d(buildTag(tag), msg, e);
    }

    public static boolean cloudFromSD(){
        return debugTag(TAG_CLOUD_CONFIG_SD);
    }
    public static boolean showMiStat(){
        return debugTag(TAG_MI_STAT);
    }

    public static boolean showHubStat(){
        return debugTag(TAG_HUB_STAT);
    }
    public static boolean debugSpeedup(){
        return IS_DEBUG_SPEEDUP;
    }

    public static void log(String tag, String msg) {
        Log.d(tag, msg);
    }

    private final static String forceReport = "debug.download.xl_fr";
    public static boolean isForceReport() {
        return SharePreferenceHelper.instance().isForceReportXL() || SystemProperties.getBoolean(forceReport, false);
    }

}
