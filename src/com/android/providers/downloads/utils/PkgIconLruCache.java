package com.android.providers.downloads.utils;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.Log;
import android.util.LruCache;

import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.ImageUtils;

/**
 * Created by lxy on 16/7/5.
 */
public class PkgIconLruCache {

    /**
     * 优化bitmap对象
     */

    private static LruCache<String, Bitmap> mMemoryCache = new LruCache<String, Bitmap>(600 * 1024) {
        @Override
        protected int sizeOf(String key, Bitmap bitmap) {
            // 重写此方法来衡量每张图片的大小，默认返回图片数量。
            Log.i("noti", "key=" + key + ",bitmap.getByteCount()=" + bitmap.getByteCount());
            return bitmap.getByteCount();
        }
    };

    /**
     * @param key
     * @param bitmap
     * @description 将bitmap添加到内存中去
     */
    public static void addBitmapToMemoryCache(String key, Bitmap bitmap) {
        if (mMemoryCache.get(key) == null) {
            mMemoryCache.put(key, bitmap);
        }
    }

    public static Bitmap getBitMap(Context context, String key) {
        if (TextUtils.isEmpty(key)) {
            return Helpers.getIconByPackage(context, key);
        }
        Bitmap bitmap = mMemoryCache.get(key);
        if (bitmap == null) {
            bitmap = Helpers.getIconByPackage(context, key);
            addBitmapToMemoryCache(key, bitmap);
        }
        return bitmap;
    }

    public static Bitmap getBitMapByPkgName(Context context, String pkgName) {
        if (TextUtils.isEmpty(pkgName)) {
            return null;
        }
        Bitmap bitmap = mMemoryCache.get(pkgName);
        if (bitmap == null) {
            bitmap = getIconByPackage(context, pkgName);
            if (bitmap != null) {
                addBitmapToMemoryCache(pkgName, bitmap);
            }
        }
        return bitmap;
    }

    public static Bitmap getIconByPackage(Context context, String packageName) {
        Bitmap bitmap = null;

        try {
            PackageManager pm = context.getPackageManager();
            ApplicationInfo info = pm.getApplicationInfo(packageName, 0);
            Drawable drawable = info.loadIcon(pm);
            if (drawable instanceof BitmapDrawable) {
                bitmap = ((BitmapDrawable) drawable).getBitmap();
            }

            if (drawable != null) {
                drawable.setCallback(null);
            }

            if (bitmap == null && drawable != null) {
                bitmap = ImageUtils.drawableToBitmap(drawable);
            }

        } catch (Exception e) {
            XLConfig.LOGD("exc", e);
        }
        return bitmap;
    }

    public static Bitmap getBitMapByKey(Context context, String key) {
        if (TextUtils.isEmpty(key)) {
            return null;
        }
        return mMemoryCache.get(key);
    }

    /**
     * 去掉原图片的周边的透明像素如果原图比没有透明像素,则返回原图片
     *
     * @param src
     * @return
     */
    public static Bitmap clearTranslucentPixel(Bitmap src) {
        if (src == null) {
            throw new NullPointerException("src bitmap is null");
        }
        int width = src.getWidth();
        int height = src.getHeight();

        final int ALPHA_PIXEL = 0;
        if (width > 0 && height > 0) {
            int horizontalTranslucentLeftWidth = 0;
            for (int i = 0; i < width; i++) {
                int pixel = src.getPixel(i, height / 2);
                int alpha = getPixAlpha(pixel);
                if (alpha > ALPHA_PIXEL) {
                    break;
                } else {
                    horizontalTranslucentLeftWidth++;
                }
            }

            int horizontalTranslucentRightWidth = 0;

            for (int i = width - 1; i > 0; i--) {
                int pixel = src.getPixel(i, height / 2);
                int alpha = getPixAlpha(pixel);
                if (alpha > ALPHA_PIXEL) {
                    break;
                } else {
                    horizontalTranslucentRightWidth++;
                }
            }

            int verticalTranslucentTopHeight = 0;
            for (int i = 0; i < height; i++) {
                int pixel = src.getPixel(width / 2, i);
                int alpha = getPixAlpha(pixel);
                if (alpha > ALPHA_PIXEL) {
                    break;
                } else {
                    verticalTranslucentTopHeight++;
                }
            }

            int verticalTranslucentBottomHeight = 0;
            for (int i = height - 1; i > 0; i--) {
                int pixel = src.getPixel(width / 2, i);
                int alpha = getPixAlpha(pixel);
                if (alpha > ALPHA_PIXEL) {
                    break;
                } else {
                    verticalTranslucentBottomHeight++;
                }
            }

            if (horizontalTranslucentLeftWidth > 0 || horizontalTranslucentRightWidth > 0 ||
                    verticalTranslucentTopHeight > 0 || verticalTranslucentBottomHeight > 0) {

                int newWidth = width - horizontalTranslucentLeftWidth - horizontalTranslucentRightWidth;
                int newHeight = height - verticalTranslucentBottomHeight - verticalTranslucentTopHeight;
                if (newWidth <= 0 || newHeight <= 0) {
                    return src;
                }

                Bitmap newBitmap = Bitmap.createBitmap(src,
                        horizontalTranslucentLeftWidth,
                        verticalTranslucentTopHeight,
                        newWidth,
                        newHeight
                );
                return newBitmap;
            } else {
                return src;
            }
        } else {
            throw new RuntimeException("the bitmap width or height is 0");
        }
    }
    public static int getPixAlpha(int pix) {
        return (pix) >>> 24;
    }
}
