
package com.android.providers.downloads.remote.service;

import static com.android.providers.downloads.util.Constant.DOWNLOAD_UI_PACKAGENAME;
import static com.android.providers.downloads.util.Constant.XL_PERMISSION_DOWNLOADS;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Binder;
import android.os.Handler;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.util.ProcessUtil;
import com.android.providers.downloads.util.SharePreferenceHelper;
import com.michael.corelib.config.CoreConfig;
import com.xunlei.downloadlib.XLDownloadManager;

/**
 * Created by mandy on 15-10-9.
 */
public class DebugLogService extends Service implements XLDownloadCfg {

    private static final String TAG = "DebugLogService";
    private Handler handler = new Handler();

    @Override
    public IBinder onBind(Intent intent) {
        return new DebugServiceImpl();
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    class DebugServiceImpl extends IDebugLogService.Stub implements XLDownloadCfg {
        @Override
        public String openDebugLog(String path, String logFileName, int time)
                throws RemoteException {
            // 创建log文件
            XLConfig.LOGD(TAG, "openDebugLog");
            checkPermission(getApplicationContext());
            if (time > 0) {
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            closeDebugLog();
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }
                }, time * 60 * 1000);
            }
            boolean isDebug = true;
            if (TextUtils.isEmpty(path)) {
                path = getApplicationContext().getCacheDir().getAbsolutePath();
            }
            if (TextUtils.isEmpty(logFileName)) {
                logFileName = log_default_filename;
            }
            XLConfig.setDebug(getApplicationContext(), isDebug, path + "/" + logFileName);
            logD(String
                    .format("openDebugLog debug=%b path=%s soPath=%s", XLConfig.isDebug(),
                            XLConfig.getLogPath(), XLConfig.logSoDir));
            setDebug(true);
            return CoreConfig.getLogPath();
        }

        @Override
        public void closeDebugLog() throws RemoteException {
            checkPermission(getApplicationContext());
            boolean isDebug = false;
            XLConfig.setDebug(getApplicationContext(), isDebug, null);
            logD(String.format("closeDebugLog debug=%b", XLConfig.isDebug()));
            setDebug(false);
        }

        @Override
        public boolean isOpenDebugLog() throws RemoteException {
            checkPermission(getApplicationContext());
            return XLConfig.isDebug();
        }

    }

    private void logD(String msg) {
        Log.d(TAG, msg);
    }

    private void logD(String msg, Exception e) {
        Log.d(TAG, msg, e);
    }

    private void setDebug(boolean debug) {
        XLDownloadManager dm = DownloadApplication.getXlDownloadManager();
        if (dm != null) {
            dm.setStatForceReportSwitch(debug);
        }
        SharePreferenceHelper.instance().setForceReportXL(debug);
    }

    public static void  clearForceReport(){
        XLDownloadManager dm = DownloadApplication.getXlDownloadManager();
        if (dm != null) {
            dm.setStatForceReportSwitch(false);
        }
        SharePreferenceHelper.instance().clearForceReport();
    }

    private void checkPermission(Context context) {
        int callingUid = Binder.getCallingUid();
        if (callingUid != android.os.Process.myUid() && context.checkCallingPermission(XL_PERMISSION_DOWNLOADS)
                != PackageManager.PERMISSION_GRANTED) {
            Log.i(TAG,String.format("DebugLogService not allowed, callingUid %d, %s unless is granted",callingUid,XL_PERMISSION_DOWNLOADS));
        }
        if (callingUid != android.os.Process.myUid() && !canAccessDebugLogService(context)) {
            Log.i(TAG,String.format("DebugLogService callidUid %d not allowed access",callingUid));
        }
    }

    private boolean canAccessDebugLogService(Context context) {
        String appName = ProcessUtil.getCallingPkgName(context);
        if (TextUtils.equals(appName, DOWNLOAD_UI_PACKAGENAME)) {
            return true;
        }
        return false;
    }
}
