
package com.android.providers.downloads.model;

import java.net.URL;
import java.util.HashSet;

import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.kcg.KCGDownloadCfg;
import com.android.providers.downloads.service.DownloadInfo;
import com.android.providers.downloads.util.DownloadExtra2;
import com.android.providers.downloads.util.Helpers;

import android.content.Intent;
import android.net.ConnectivityManager;
import android.text.TextUtils;

/**
 * State for the entire run() method.
 */
public class State implements XLDownloadCfg, KCGDownloadCfg {
    public String mFilename;
    public String mDownloadingFileName;
    public String mTitle;
    public String mMimeType;
    public boolean mMimeTypeFromHead = false;
    public int mRetryAfter = 0;
    public boolean mGotData = false;
    public String mRequestUri;
    // -1 means haven't read content length yet, 0 means transfer encoding
    // was chunked.
    public long mTotalBytes = -1;
    public long mCurrentBytes = 0;
    public String mHeaderETag;
    public String mHeaderIfRangeId;
    public String mHeaderAcceptRanges;
    public boolean mContinuingDownload = false;
    public long mBytesNotified = 0;
    public long mTimeLastNotification = 0;
    public int mNetworkType = ConnectivityManager.TYPE_NONE;

    /** Historical bytes/second speed of this download. */
    public long mSpeed;
    /** Time when current sample started. */
    public long mSpeedSampleStart;
    /** Bytes transferred since current sample started. */
    public long mSpeedSampleBytes;

    public long mContentLength = -1;
    public String mContentDisposition;
    public String mContentLocation;

    public int mRedirectionCount;
    public URL mUrl;

    public long mFileCreateTime;
    public long mDownloadingCurrentSpeed;
    public long mDownloadSurplustime; // used for mP2SSpeed by hzg 2015-1-13
    public long mXlAccelerateSpeed;
    public long mDownloadedTime;
    public int mXlVipStatus;
    public String mXlVipCdnUrl;
    public int mXlTaskOpenMark;
    public long mId;
    public String mPackage;
    public String mApkPackageName;
    public int mFileCount;
    public HashSet<Integer> mSelect;
    public int mDownloadFrom = EXTRA_VALUE_DOWNLOAD_FROM_DEFAULT;
    public String mPassBackStr;
    // MIUI ADD FOR KCG: START
    public int mKcgTaskOpenMark;
    public int mPercent;
    public int mProtocol;
    //ResourceLine: HTTP/1.1 156 RESOURCE SSL VERIFY CERT ERROR
    public String mResourceLine;
    public boolean mFileOptimize;
    // END

    public State(DownloadInfo info) {
        mMimeType = Intent.normalizeMimeType(info.mMimeType);
        mRequestUri = info.mUri;
        mFilename = info.mFileName;
        mTitle = info.mTitle;
        mTotalBytes = info.mTotalBytes;
        mCurrentBytes = info.mCurrentBytes;
        mFileCreateTime = info.mFileCreateTime;
        mDownloadingCurrentSpeed = info.mDownloadingCurrentSpeed;
        mDownloadSurplustime = info.mDownloadSurplustime;
        mXlAccelerateSpeed = info.mXlAccelerateSpeed;
        mDownloadedTime = info.mDownloadedTime;
        mXlVipStatus = info.mXlVipStatus;
        mXlVipCdnUrl = info.mXlVipCdnUrl;
        mXlTaskOpenMark = info.mXlTaskOpenMark;
        mId = info.mId;
        mPackage = info.mPackage;
        mApkPackageName = info.apkPackageName;
        if (mPackage != null && mPackage.startsWith("com.google.")) {
            mXlTaskOpenMark = 0;
        }
        mFileCount = info.mFileCount;
        mSelect = info.mSelect;
        mDownloadFrom = info.mDownloadFrom;
        mPassBackStr = info.mPassBackStr;
        // MIUI ADD FOR KCG: START
        mKcgTaskOpenMark = info.mKcgTaskOpenMark;
        mPercent = info.mPercent;
        mProtocol = info.mProtocol;
        mFileOptimize = DownloadExtra2.isFileOptimize(info.mDownloadExtras2);
    }

    public void resetBeforeExecute() {
        // Reset any state from previous execution
        mContentLength = -1;
        mContentDisposition = null;
        mContentLocation = null;
        mRedirectionCount = 0;
    }
}
