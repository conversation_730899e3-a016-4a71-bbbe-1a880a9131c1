/*
 * Copyright (C) 2012 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.providers.downloads.service;

import static android.app.DownloadManager.Request.VISIBILITY_HIDDEN;
import static android.app.DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED;
import static android.app.DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_ONLY_COMPLETION;

import android.os.Build;
import android.app.DownloadManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.net.Uri;
import android.net.Uri.Builder;
import android.os.SystemClock;
import android.provider.Downloads;
import android.text.TextUtils;
import android.util.Log;
import android.util.LongSparseLongArray;

import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.IndentingPrintWriter;
import com.android.providers.downloads.util.NotificationHelper;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;

import javax.annotation.concurrent.GuardedBy;

import com.android.providers.downloads.config.Constants;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.receiver.DownloadReceiver;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.R;

/**
 * Update {@link NotificationManager} to reflect current {@link DownloadInfo}
 * states. Collapses similar downloads into a single notification, and builds
 * {@link PendingIntent} that launch towards {@link DownloadReceiver}.
 */
public class DownloadNotifier implements XLDownloadCfg {

    public static final int TYPE_ACTIVE = 1;
    public static final int TYPE_WAITING = 2;
    public static final int TYPE_COMPLETE = 3;
    public static final int TYPE_INSUFFICIENT_SPACE = 4;
    public static final int TYPE_WAITING_FOR_WIFI = 5;
    private static final int DOWNLOAD_NOTIFICATION_ID = 1089;
    public static final String TYPE = "Type";

    private static final String CHANNEL_ACTIVE = "active";
    private static final String CHANNEL_WAITING = "waiting";
    private static final String CHANNEL_COMPLETE = "complete";
    private static final String CHANNEL_INSUFFICIENT_SPACE = "insufficient_space";

    private final Context mContext;
    private final NotificationManager mNotifManager;

    /**
     * Currently active notifications, mapped from clustering tag to timestamp
     * when first shown.
     *
     * @see #buildNotificationTag(DownloadInfo)
     */
    @GuardedBy("mActiveNotifs")
    private final HashMap<String, Long> mActiveNotifs = Maps.newHashMap();

    /**
     * Current speed of active downloads, mapped from {@link DownloadInfo#mId}
     * to speed in bytes per second.
     */
    @GuardedBy("mDownloadSpeed")
    private final LongSparseLongArray mDownloadSpeed = new LongSparseLongArray();

    /**
     * Last time speed was reproted, mapped from {@link DownloadInfo#mId} to
     * {@link SystemClock#elapsedRealtime()}.
     */
    @GuardedBy("mDownloadSpeed")
    private final LongSparseLongArray mDownloadTouch = new LongSparseLongArray();

    public DownloadNotifier(Context context) {
        mContext = context;
        mNotifManager = (NotificationManager) context
                .getSystemService(Context.NOTIFICATION_SERVICE);
        // Ensure that all our channels are ready to use
        mNotifManager.createNotificationChannel(new NotificationChannel(CHANNEL_ACTIVE,
                context.getText(R.string.channel_active),
                NotificationManager.IMPORTANCE_LOW));
        mNotifManager.createNotificationChannel(new NotificationChannel(CHANNEL_WAITING,
                context.getText(R.string.channel_waiting),
                NotificationManager.IMPORTANCE_LOW));
        mNotifManager.createNotificationChannel(new NotificationChannel(CHANNEL_INSUFFICIENT_SPACE,
                context.getText(R.string.channel_insufficient_space),
                NotificationManager.IMPORTANCE_LOW));
        mNotifManager.createNotificationChannel(new NotificationChannel(CHANNEL_COMPLETE,
                context.getText(R.string.channel_complete),
                NotificationManager.IMPORTANCE_LOW));
    }

    public void cancelAll() {
        mNotifManager.cancelAll();
    }

    /**
     * Notify the current speed of an active download, used for calculating
     * estimated remaining time.
     */
    public void notifyDownloadSpeed(long id, long bytesPerSecond) {
        synchronized (mDownloadSpeed) {
            XLConfig.LOGD("in notifyDownloadSpeed id=" + id + ", bytesPerSecond=" + bytesPerSecond);
            if (bytesPerSecond != 0) {
                mDownloadSpeed.put(id, bytesPerSecond);
                mDownloadTouch.put(id, SystemClock.elapsedRealtime());
            } else {
                mDownloadSpeed.delete(id);
                mDownloadTouch.delete(id);
            }
        }
    }

    /**
     * Update {@link NotificationManager} to reflect the given set of
     * {@link DownloadInfo}, adding, collapsing, and removing as needed.
     */
    public void updateWith(Collection<DownloadInfo> downloads) {
        synchronized (mActiveNotifs) {
            updateWithLocked(downloads);
        }
    }

    public static Intent getIntent(String action) {
        return getIntent(action, null);
    }

    public static Intent getIntent(String action, String tag) {
        Builder scheme = new Uri.Builder().scheme("active-dl");
        final Uri uri = TextUtils.isEmpty(tag) ? scheme.build() : scheme.appendPath(tag).build();
        Intent intent = new Intent(action, uri);
        intent.setPackage("com.android.providers.downloads");
        return intent;
    }

    public void dump(IndentingPrintWriter pw, Collection<DownloadInfo> downloads) {

        pw.println("DownloadNotifier:");
        pw.increaseIndent();
        final Multimap<String, DownloadInfo> clustered = ArrayListMultimap.create();
        for (DownloadInfo info : downloads) {
            final String tag = BuildUtils.isInternationalBuilder() ? buildNotificationTagEn(info)
                    : buildNotificationTag(info);
            if (tag != null) {
                clustered.put(tag, info);
            }
        }
        for (String tag : clustered.keySet()) {
            final int type = getNotificationTagType(tag);
            final Collection<DownloadInfo> cluster = clustered.get(tag);
            HashSet<Long> ids = new HashSet<Long>();
            for (DownloadInfo downloadInfo : cluster) {
                ids.add(downloadInfo.mId);
            }
            pw.print(tag + "/" + cluster.size());
            pw.print("    " + ids.toString());
        }
        pw.println();

        pw.decreaseIndent();
    }

    private Notification.Builder createNotificationBuild(int type) {
        Notification.Builder builder = null;
        if (type == TYPE_ACTIVE) {
            builder = new Notification.Builder(mContext, CHANNEL_ACTIVE);
        } else if (type == TYPE_WAITING || type == TYPE_WAITING_FOR_WIFI) {
            builder = new Notification.Builder(mContext, CHANNEL_WAITING);
        } else if (type == TYPE_INSUFFICIENT_SPACE) {
            builder = new Notification.Builder(mContext, CHANNEL_INSUFFICIENT_SPACE);
        } else if (type == TYPE_COMPLETE) {
            builder = new Notification.Builder(mContext, CHANNEL_COMPLETE);
        }
        return builder;
    }

    private void updateWithLocked(Collection<DownloadInfo> downloads) {
        final Resources res = mContext.getResources();

        // Cluster downloads together
        final Multimap<String, DownloadInfo> clustered = ArrayListMultimap.create();
        for (DownloadInfo info : downloads) {
            final String tag = buildNotificationTag(info);
            if (tag != null) {
                clustered.put(tag, info);
            }
        }

        // Build notification for each cluster
        for (String tag : clustered.keySet()) {
            final int type = getNotificationTagType(tag);
            final Collection<DownloadInfo> cluster = clustered.get(tag);
            final DownloadInfo info = cluster.iterator().next();

            boolean isXlEngineOn = false;
            for (DownloadInfo dinfo : cluster) {
                if (dinfo.mXlTaskOpenMark == 1) {
                    isXlEngineOn = true;
                    break;
                }
            }

            final Notification.Builder builder = createNotificationBuild(type);
            if (builder == null) {
                continue;
            }

            builder.setSmallIcon(R.drawable.small_xl);
            // Use time when cluster was first shown to avoid shuffling
            final long firstShown;
            if (mActiveNotifs.containsKey(tag)) {
                firstShown = mActiveNotifs.get(tag);
            } else {
                firstShown = System.currentTimeMillis();
                mActiveNotifs.put(tag, firstShown);
            }
            builder.setWhen(firstShown);
            // Build action intents
            if (type == TYPE_ACTIVE || type == TYPE_WAITING || type == TYPE_WAITING_FOR_WIFI) {
                // build a synthetic uri for intent identification purposes
                Intent intent = getIntent(Constants.ACTION_LIST, tag);
                intent.putExtra(DownloadManager.EXTRA_NOTIFICATION_CLICK_DOWNLOAD_IDS,
                        getDownloadIds(cluster));
                builder.setContentIntent(PendingIntent.getBroadcast(mContext,
                        0, intent, DownloadNotifierInjector.fixPendingIntentFlags(PendingIntent.FLAG_UPDATE_CURRENT)));

                // Make can clear when waiting.
                if (type == TYPE_WAITING || type == TYPE_WAITING_FOR_WIFI) {
                    builder.setOngoing(false);
                    final Intent hideIntent = getIntent(Constants.ACTION_HIDE, tag);
                    hideIntent.putExtra(DownloadManager.EXTRA_NOTIFICATION_CLICK_DOWNLOAD_IDS,
                            getDownloadIds(cluster));
                    builder.setDeleteIntent(PendingIntent.getBroadcast(mContext, 0, hideIntent,
                            DownloadNotifierInjector.fixPendingIntentFlags(PendingIntent.FLAG_UPDATE_CURRENT)));
                } else {
                    builder.setOngoing(true);
                }
            } else if (type == TYPE_COMPLETE) {
                builder.setAutoCancel(true);

                final Intent intent = getIntent(Constants.ACTION_OPEN, tag);
                intent.putExtra(DownloadManager.EXTRA_NOTIFICATION_CLICK_DOWNLOAD_IDS,
                        getDownloadIds(cluster));
                intent.putExtra(TYPE, tag);
                builder.setContentIntent(PendingIntent.getBroadcast(mContext, 0, intent,
                        DownloadNotifierInjector.fixPendingIntentFlags(PendingIntent.FLAG_UPDATE_CURRENT)));

                final Intent hideIntent = getIntent(Constants.ACTION_HIDE, tag);
                hideIntent.putExtra(DownloadManager.EXTRA_NOTIFICATION_CLICK_DOWNLOAD_IDS,
                        getDownloadIds(cluster));
                builder.setDeleteIntent(PendingIntent.getBroadcast(mContext, 0, hideIntent,
                        DownloadNotifierInjector.fixPendingIntentFlags(PendingIntent.FLAG_UPDATE_CURRENT)));
            } else if (type == TYPE_INSUFFICIENT_SPACE) {
                builder.setAutoCancel(true);

                final Intent intent = getIntent(Constants.ACTION_OPEN);
                intent.putExtra(DownloadManager.EXTRA_NOTIFICATION_CLICK_DOWNLOAD_IDS,
                        getDownloadIds(cluster));
                intent.putExtra(TYPE, tag);
                builder.setContentIntent(PendingIntent.getBroadcast(mContext, 1, intent,
                        DownloadNotifierInjector.fixPendingIntentFlags(PendingIntent.FLAG_UPDATE_CURRENT)));

                final Intent hideIntent = getIntent(Constants.ACTION_HIDE);
                hideIntent.putExtra(DownloadManager.EXTRA_NOTIFICATION_CLICK_DOWNLOAD_IDS,
                        getDownloadIds(cluster));
                builder.setDeleteIntent(PendingIntent.getBroadcast(mContext, 1, hideIntent,
                        DownloadNotifierInjector.fixPendingIntentFlags(PendingIntent.FLAG_UPDATE_CURRENT)));
            }

            // Calculate and show progress
            String remainingText = null;
            String percentText = null;
            if (type == TYPE_ACTIVE) {
                long current = 0;
                long total = 0;
                long speed = 0;
                synchronized (mDownloadSpeed) {
                    for (DownloadInfo dinfo : cluster) {
                        if (dinfo.mTotalBytes > 0) {
                            current += dinfo.mCurrentBytes;
                            total += dinfo.mTotalBytes;
                            speed += mDownloadSpeed.get(dinfo.mId);
                        }
                    }
                }

                if (total == -1) {
                    if (info.isM3u8Task()) {
                        setProgress(builder, 100, info.mPercent, false);
                    } else {
                        setProgress(builder, 100, 0, true);
                    }
                } else if (total > 0) {
                    int percent = (int) ((current * 100) / total);
                    if (percent > 100) {
                        percent = 100;
                    }
                    percentText = res.getString(R.string.download_percent, percent);
                    setProgress(builder, 100, percent, false);
                } else {
                    if (info.isM3u8Task()) {
                        setProgress(builder, 100, info.mPercent, false);
                    } else {
                        setProgress(builder, 100, 0, true);
                    }
                }
            }

            // Build titles and description
            final Notification notif;
            // All insufficient-space tasks post only one notification
            if (type == TYPE_INSUFFICIENT_SPACE) {
                // 通知的icon换成垃圾清理的icon
                // 主标题：手机存储空间不足，副标题：点击进行垃圾清理
                setTitle(builder,mContext.getString(R.string.insufficient_space));
                setContext(builder,mContext.getString(R.string.enter_garbage_clean));

            } else if (cluster.size() == 1) {
                if (!Helpers.isInternationalBuilder()) {
                    setTitle(builder, getDownloadTitle(res, info, type, 1));
                } else {
                    setTitle(builder, getDownloadTitle(res, info, TYPE_COMPLETE, 1));
                }

                if (type == TYPE_ACTIVE) {
                    if (Helpers.isInternationalBuilder()) {
                        setContext(builder,remainingText);
                    } else if (isXlEngineOn) {
                        if (!BuildUtils.isTablet()) {
                            setContext(builder,
                                    res.getString(R.string.notif_text_engine_on));
                        }
                    } else {
                        if (!BuildUtils.isTablet()) {
                            setContext(builder,
                                    res.getString(R.string.notif_text_engine_off));
                        }
                    }
                    setSubText(builder,percentText);
                } else if (type == TYPE_WAITING || type == TYPE_WAITING_FOR_WIFI) {
                    String text = "";
                    if (type == TYPE_WAITING_FOR_WIFI) {
                        setTitle(builder,
                                getDownloadTitle(res, info, TYPE_COMPLETE, 1));
                        text = getTextByErrorCode(mContext, getErrorCode(tag));
                    } else if (type == TYPE_WAITING) {
                        setTitle(builder,
                                getDownloadTitle(res, info, TYPE_COMPLETE, 1));
                        text = res.getString(R.string.paused_waiting_for_network);
                    }
                    setContext(builder,text);
                } else if (type == TYPE_COMPLETE) {
                    if (Downloads.Impl.isStatusSuccess(info.mStatus)) {
                        if (isXlEngineOn && !Helpers.isInternationalBuilder()) {
                        }
                        setContext(builder,
                                res.getString(R.string.notification_download_complete));
                    } else {
                        setContext(builder,
                                res.getText(R.string.notification_download_failed));
                    }
                }
            } else {
                setTitle(builder, getDownloadTitle(res, info, type, cluster.size()));

                if (type == TYPE_ACTIVE) {
                    if (Helpers.isInternationalBuilder()) {
                        setContext(builder,remainingText);
                    } else if (isXlEngineOn) {
                        if (!BuildUtils.isTablet()) {
                            setContext(builder,
                                    res.getString(R.string.notif_text_engine_on));
                        }
                    } else {
                        if (!BuildUtils.isTablet()) {
                            setContext(builder,
                                    res.getString(R.string.notif_text_engine_off));
                        }
                    }
                    setSubText(builder,percentText);
                } else if (type == TYPE_WAITING|| type == TYPE_WAITING_FOR_WIFI) {
                    setTitle(builder,
                            String.format(res.getString(R.string.notif_title_file_size,
                                    cluster.size())));
                    String text = "";
                    if (type == TYPE_WAITING_FOR_WIFI) {
                        text = getTextByErrorCode(mContext, getErrorCode(tag));
                    } else if (type == TYPE_WAITING) {
                        text = res.getString(R.string.paused_waiting_for_network);
                    }
                    setContext(builder, text);
                } else if (type == TYPE_COMPLETE) {
                    if (Downloads.Impl.isStatusSuccess(info.mStatus)) {
                        if (isXlEngineOn && !Helpers.isInternationalBuilder()) {
                        }
                        setTitle(builder, String.format(res.getString(
                                R.string.notification_download_success_more, cluster.size())));
                        setContext(builder,
                                res.getString(R.string.notification_download_complete));
                    } else {
                        int resId = R.plurals.notification_download_failed_more;
                        int size = cluster.size();
                        String title = mContext.getResources().getQuantityString(resId, size, size);
                        setTitle(builder, title);
                        setContext(builder,res.getText(R.string.notification_download_failed));
                    }
                }
            }
            builder.setShowWhen(true);
            String pkgName = null;
            if (type == TYPE_INSUFFICIENT_SPACE) {
                pkgName = "com.miui.cleanmaster";
            } else {
                pkgName = TextUtils.isEmpty(info.mSubPackage) ? info.mPackage : info.mSubPackage;
            }
            builder.setGroup(pkgName);
            if (mIconHelper == null) {
                mIconHelper = new NotificationHelper(mContext);
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH) {
                builder.getExtras().putBoolean("miui.isGrayscaleIcon",true);
            }
            mIconHelper.notify(NotificationHelper.buildNotifyItem(null, android.R.id.icon,
                            builder, DOWNLOAD_NOTIFICATION_ID, tag),
                    pkgName,
                    info.mNotificationIconUrl);
        }


        // Remove stale tags that weren't renewed
        final Iterator<String> it = mActiveNotifs.keySet().iterator();
        while (it.hasNext()) {
            final String tag = it.next();
            if (!clustered.containsKey(tag)) {
                mNotifManager.cancel(tag, DOWNLOAD_NOTIFICATION_ID);
                it.remove();
            }
        }
    }

    private void setProgress(Notification.Builder builder, int max, int percent, boolean indeterminate) {
        builder.setProgress(max, percent, indeterminate);
    }

    private void setTitle(Notification.Builder builder,CharSequence text) {
        builder.setContentTitle(text);
    }

    private void setContext(Notification.Builder builder,CharSequence text) {
        builder.setContentText(text);
    }

    private void setSubText(Notification.Builder builder,CharSequence text) {
        builder.setSubText(text);
    }

    private NotificationHelper mIconHelper;

    private static CharSequence getDownloadTitle(Resources res, DownloadInfo info, int type, int size) {

        if (type == TYPE_COMPLETE) {
            return !TextUtils.isEmpty(info.mTitle) ? info.mTitle : res.getString(R.string.download_unknown_title);
        }
        if (size > 1) {
            return String.format(res.getString(R.string.notif_title_multiple_downloading),
                    size);
        } else if (size == 1) {
            return !TextUtils.isEmpty(info.mTitle) ? String.format(res.getString(R.string.notif_title_single_downloading), info.mTitle) : res.getString(R.string.download_unknown_title);
        }
        return res.getString(R.string.download_unknown_title);
    }


    private long[] getDownloadIds(Collection<DownloadInfo> infos) {
        final long[] ids = new long[infos.size()];
        int i = 0;
        for (DownloadInfo info : infos) {
            ids[i++] = info.mId;
        }
        return ids;
    }

    public void dumpSpeeds() {
        synchronized (mDownloadSpeed) {
            for (int i = 0; i < mDownloadSpeed.size(); i++) {
                final long id = mDownloadSpeed.keyAt(i);
                final long delta = SystemClock.elapsedRealtime() - mDownloadTouch.get(id);
                XLConfig.LOGD("Download " + id + " speed " + mDownloadSpeed.valueAt(i) + "bps, "
                        + delta + "ms ago");
            }
        }
    }

    /**
     * Build tag used for collapsing several {@link DownloadInfo} into a single
     * {@link Notification}.
     */
    private static String buildNotificationTag(DownloadInfo info) {
        return BuildUtils.isInternationalBuilder() ? buildNotificationTagEn(info)
                : _buildNotificationTag(info);
    }

    /**
     * Build tag used for collapsing several {@link DownloadInfo} into a single
     * {@link Notification}.
     */
    private static String _buildNotificationTag(DownloadInfo info) {
        if (info.mVisibility == VISIBILITY_HIDDEN || info.mNotifyIsClear) {
            return null;
        }
        if (info.mStatus == Downloads.Impl.STATUS_WAITING_FOR_NETWORK) {
            return TYPE_WAITING + ":" + wrapPackage(info);
        } else if (info.mStatus == Downloads.Impl.STATUS_QUEUED_FOR_WIFI) {
            int code = info.getErrorCodByReason(info.mErrorMsg);
            return TYPE_WAITING_FOR_WIFI + ":" + code + ":" + wrapPackage(info);
        } else if (isInsufficientNotification(info)) {
            // return TYPE_INSUFFICIENT_SPACE + ":" + info.mId;
            if (info.isOnExternalStorage(info.getLocalUri())) {
                return TYPE_INSUFFICIENT_SPACE + ": external";
            } else {
                return TYPE_INSUFFICIENT_SPACE + ": internal";
            }
        } else if (isActiveAndVisible(info)) {
            return TYPE_ACTIVE + ":" + wrapPackage(info);
        } else if (isCompleteAndVisible(info)) {
            // Complete downloads always have unique notifs
            // return TYPE_COMPLETE + ":" + info.mId;
            if (Downloads.Impl.isStatusSuccess(info.mStatus)) {
                return TYPE_COMPLETE + ":" + wrapPackage(info) + ":success";
            } else {
                return TYPE_COMPLETE + ":" + wrapPackage(info) + ":failed";
            }
        } else {
            return null;
        }
    }

    private static String wrapPackage(DownloadInfo info) {
        return TextUtils.isEmpty(info.mSubPackage) ? info.mPackage : info.mSubPackage;
    }

    /**
     * Build tag used for collapsing several downloads into a single
     * {@link Notification}.
     */
    private static String buildNotificationTagEn(DownloadInfo info) {
        final int status = info.mStatus;
        final int visibility = info.mVisibility;
        final String notifPackage = info.mPackage;

        if (isQueuedAndVisible(status, visibility)) {
            return TYPE_WAITING + ":" + notifPackage;
        } else if (isActiveAndVisible(status, visibility)) {
            return TYPE_ACTIVE + ":" + notifPackage;
        } else if (isCompleteAndVisible(info)) {
            if (Downloads.Impl.isStatusSuccess(status)) {
                return TYPE_COMPLETE + ":" + info.mPackage + ":success";
            } else {
                return TYPE_COMPLETE + ":" + info.mPackage + ":failed";
            }
        } else {
            return null;
        }
    }

    private static boolean isQueuedAndVisible(int status, int visibility) {
        return status == STATUS_QUEUED_FOR_WIFI &&
                (visibility == VISIBILITY_VISIBLE
                        || visibility == VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
    }

    private static boolean isActiveAndVisible(int status, int visibility) {
        return status == STATUS_RUNNING &&
                (visibility == VISIBILITY_VISIBLE
                        || visibility == VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
    }

    /**
     * Return the cluster type of the given tag, as created by
     * {@link #buildNotificationTag(DownloadInfo)}.
     */
    public static int getNotificationTagType(String tag) {
        return Integer.parseInt(tag.substring(0, tag.indexOf(':')));
    }

    /**
     * 目前只支持等待wifi的情况下解析错误码
     * @param tag
     * @return
     */
    public static int getErrorCode(String tag) {
        if (TYPE_WAITING_FOR_WIFI != getNotificationTagType(tag)) {
            return -1;
        }
        String newStr = tag.substring((TYPE_WAITING_FOR_WIFI + ":").length());
        return Integer.parseInt(newStr.substring(0, newStr.indexOf(':')));
    }

    public static String getTextByErrorCode(Context context,int errorCode) {
        Resources res = context.getResources();
        String text = null;
        switch (errorCode) {
            case STATUS_NET_DISALLOWED_BY_REQUESTOR:
                text = res.getString(R.string.notify_only_wifi_download);
                break;
            case STATUS_NET_UNUSABLE_DUE_TO_SIZE:
                text = res.getString(R.string.notification_need_wifi_for_size);
                break;
            case STATUS_NET_DISALLOWED_BY_APP:
                text = res.getString(R.string.notify_disabled_net_by_app);
                break;
            default:
                text = res.getString(R.string.notification_need_wifi_for_size);
                Log.w(TAG, "getTextByErrorCode unhandle errorCode=" + errorCode);
                break;
        }
        return text;
    }

    private static boolean isActiveAndVisible(DownloadInfo download) {
        return (download.mStatus == STATUS_RUNNING || download.mStatus == Downloads.Impl.STATUS_PENDING) &&
                (download.mVisibility == VISIBILITY_VISIBLE
                        || download.mVisibility == VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
    }

    private static boolean isCompleteAndVisible(DownloadInfo download) {
        return Downloads.Impl.isStatusCompleted(download.mStatus)
                && (download.mVisibility == VISIBILITY_VISIBLE_NOTIFY_COMPLETED
                || download.mVisibility ==
                VISIBILITY_VISIBLE_NOTIFY_ONLY_COMPLETION);
    }

    private static boolean isInsufficientNotification(DownloadInfo download) {
        return download.mStatus == Downloads.Impl.STATUS_INSUFFICIENT_SPACE_ERROR &&
                !download.hasEnoughSpace();
    }

}
