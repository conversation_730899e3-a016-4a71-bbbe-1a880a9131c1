/*
 * Copyright (C) 2008 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.providers.downloads.service;

import android.app.DownloadManager;
import android.app.DownloadManager.ExtraDownloads;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.NetworkInfo.DetailedState;
import android.net.Uri;
import android.os.Environment;
import android.provider.Downloads;
import android.provider.Downloads.Impl;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;

import androidx.annotation.NonNull;

import com.android.internal.annotations.GuardedBy;
import com.android.providers.downloads.DownloadThread;
import com.android.providers.downloads.R;
import com.android.providers.downloads.RealSystemFacade;
import com.android.providers.downloads.StorageManager;
import com.android.providers.downloads.SystemFacade;
import com.android.providers.downloads.config.ActionConstants;
import com.android.providers.downloads.config.Constants;
import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.exception.StopRequestException;
import com.android.providers.downloads.fileoptimization.OptDownloadThread;
import com.android.providers.downloads.helper.FeatureSwitch;
import com.android.providers.downloads.kcg.KCGDownloadCfg;
import com.android.providers.downloads.kcg.KCGDownloadThread;
import com.android.providers.downloads.kcg.utils.KCGLog;
import com.android.providers.downloads.m3u8.M3u8DownloadThread;
import com.android.providers.downloads.m3u8.VideoUtils;
import com.android.providers.downloads.provider.DownLoadProviderUtils;
import com.android.providers.downloads.provider.DownloadProvider;
import com.android.providers.downloads.service.DesktopProgressAppInfo;
import com.android.providers.downloads.service.DownloadNotifier;
import com.android.providers.downloads.service.DownloadScanner;
import com.android.providers.downloads.service.DownloadService;
import com.android.providers.downloads.service.InstallManager;
import com.android.providers.downloads.util.BTDatabaseHelper;
import com.android.providers.downloads.util.DownloadExtra;
import com.android.providers.downloads.util.DownloadExtra2;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.IndentingPrintWriter;
import com.android.providers.downloads.util.NetworkUtils;
import com.android.providers.downloads.util.XLDownloadHelper;
import com.android.providers.downloads.xunlei.BTDownloadThread;
import com.android.providers.downloads.xunlei.EmuleDownloadThread;
import com.android.providers.downloads.xunlei.FtpDownloadThread;
import com.android.providers.downloads.xunlei.MagnetDownloadThread;
import com.android.providers.downloads.xunlei.XLDownloadThread;
import com.xunlei.downloadlib.XLDownloadManager;
import com.xunlei.vipchannel.XLVipChannelManager;
import com.android.providers.downloads.util.BuildUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * Stores information about an individual download.
 */
public class DownloadInfo implements XLDownloadCfg, KCGDownloadCfg {
    // TODO: move towards these in-memory objects being sources of truth, and
    // periodically pushing to provider.
    private static final String TAG = "DownloadInfo";
    public static final String COLUMN_APK_PACKGENAME = "apk_package_name";

    public static class Reader {
        private ContentResolver mResolver;
        private Cursor mCursor;

        public Reader(ContentResolver resolver, Cursor cursor) {
            mResolver = resolver;
            mCursor = cursor;
        }

        public DownloadInfo newDownloadInfo(Context context, SystemFacade systemFacade,
                                            StorageManager storageManager,
                                            DownloadNotifier notifier, XLDownloadManager dm,
                                            XLVipChannelManager vm) {
            final DownloadInfo info = new DownloadInfo(context, systemFacade, storageManager,
                    notifier, dm, vm);

            updateFromDatabase(context.getContentResolver(), info);
            readRequestHeaders(info);

            if (Integer.valueOf(info.apkInstallWay) == 1) {
                if (!DesktopProgressAppInfo.appInfoMap.containsKey(info.apkPackageName)) {
                    DesktopProgressAppInfo.appInfoMap.put(info.apkPackageName, new DesktopProgressAppInfo(info));
                }
                DesktopProgressAppInfo  desktopProgressAppInfo = DesktopProgressAppInfo.appInfoMap.get(info.apkPackageName);
                desktopProgressAppInfo.startDesktopProgress();
            }
            logStaticD("in newDownloadInfo " + info);
            return info;
        }

        public void updateFromDatabase(ContentResolver resolver, DownloadInfo info) {
            info.mId = getLong(Downloads.Impl._ID);
            info.mUri = getString(Downloads.Impl.COLUMN_URI);
            int noIntegrity=getInt(Downloads.Impl.COLUMN_NO_INTEGRITY);
            info.mNoIntegrity = noIntegrity == 1;
            info.mHint = getString(Downloads.Impl.COLUMN_FILE_NAME_HINT);
            info.mFileName = getString(Downloads.Impl._DATA);
            info.mMimeType = getString(Downloads.Impl.COLUMN_MIME_TYPE);
            info.mDestination = getInt(Downloads.Impl.COLUMN_DESTINATION);
            info.mVisibility = getInt(Downloads.Impl.COLUMN_VISIBILITY);
            info.mStatus = getInt(Downloads.Impl.COLUMN_STATUS);

            info.mNumFailed = getInt(Downloads.Impl.COLUMN_FAILED_CONNECTIONS);
            int retryRedirect = getInt(Constants.RETRY_AFTER_X_REDIRECT_COUNT);
            info.mRetryAfter = retryRedirect & 0xfffffff;
            info.mLastMod = getLong(Downloads.Impl.COLUMN_LAST_MODIFICATION);
            String vPackage = getString(Downloads.Impl.COLUMN_NOTIFICATION_PACKAGE);
            info.mPackage = vPackage;

            info.mClass = getString(Downloads.Impl.COLUMN_NOTIFICATION_CLASS);
            info.mExtras = getString(Downloads.Impl.COLUMN_NOTIFICATION_EXTRAS);
            info.mCookies = getString(Downloads.Impl.COLUMN_COOKIE_DATA);
            info.mUserAgent = getString(Downloads.Impl.COLUMN_USER_AGENT);
            info.mReferer = getString(Downloads.Impl.COLUMN_REFERER);
            long cursorBytes = getLong(Downloads.Impl.COLUMN_TOTAL_BYTES);
            if (info.mTotalBytes <= 0 || cursorBytes > info.mTotalBytes) {
                info.mTotalBytes = cursorBytes;
            }
            info.mFileSize = BuildUtils.isSdkOverR() ? getLong(COLUMN_FILE_SIZE) : -1;
            info.mCurrentBytes = getLong(Downloads.Impl.COLUMN_CURRENT_BYTES);
            info.mETag = getString(Constants.ETAG);
            info.mIfRange = getString(ExtraDownloads.COLUMN_IF_RANGE_ID);
            info.mUid = getInt(Constants.UID);
            info.mMediaScanned = getInt(Constants.MEDIA_SCANNED);
            info.mDeleted = getInt(Downloads.Impl.COLUMN_DELETED) == 1;
            info.mMediaProviderUri = getString(Downloads.Impl.COLUMN_MEDIAPROVIDER_URI);
            info.mIsPublicApi = getInt(Downloads.Impl.COLUMN_IS_PUBLIC_API) != 0;
            info.mAllowedNetworkTypes = getInt(Downloads.Impl.COLUMN_ALLOWED_NETWORK_TYPES);
            info.mAllowRoaming = getInt(Downloads.Impl.COLUMN_ALLOW_ROAMING) != 0;
            info.mAllowMetered = getInt(Downloads.Impl.COLUMN_ALLOW_METERED) != 0;
            info.mTitle = getString(Downloads.Impl.COLUMN_TITLE);
            info.mDescription = getString(Downloads.Impl.COLUMN_DESCRIPTION);
            info.mBypassRecommendedSizeLimit =
                    getInt(Downloads.Impl.COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT);
            info.mErrorMsg = getString(Downloads.Impl.COLUMN_ERROR_MSG);
            // v5 have v6 don't have
            // Read xunlei relevant fields
            info.mFileCreateTime = getLong(ExtraDownloads.COLUMN_FILE_CREATE_TIME);
            // info.mFileCreateTime = System.currentTimeMillis(); // close by
            // hzg 20140923
            info.mDownloadingCurrentSpeed = getLong(ExtraDownloads.COLUMN_DOWNLOADING_CURRENT_SPEED);
            info.mDownloadSurplustime = getLong(ExtraDownloads.COLUMN_DOWNLOAD_SURPLUS_TIME);
            info.mXlAccelerateSpeed = getLong(ExtraDownloads.COLUMN_XL_ACCELERATE_SPEED);
            info.mDownloadedTime = getLong(ExtraDownloads.COLUMN_DOWNLOADED_TIME);
            info.mXlVipStatus = getInt(ExtraDownloads.COLUMN_XL_VIP_STATUS);
            info.mXlVipCdnUrl = getString(ExtraDownloads.COLUMN_XL_VIP_CDN_URL);
            // needed by updateDownload function in DownloadService, not working
            // in insertDownloadLocked
            info.mXlTaskOpenMark = getInt(ExtraDownloads.COLUMN_XL_TASK_OPEN_MARK);
            info.apkInstallWay = getString(DownloadProvider.COLUMN_APK_INSTALL_WAY);
            info.fileHash = getString(DownloadProvider.COLUMN_FILE_HASH);
            info.apkPackageName = getString(COLUMN_APK_PACKGENAME);

            if (info.mStatus != Downloads.Impl.STATUS_INSUFFICIENT_SPACE_ERROR) {
                info.mInsufficientAlreadyPosted = false;
            }

            synchronized (this) {
                info.mControl = getInt(Downloads.Impl.COLUMN_CONTROL);
            }
            info.mFileCount = getInt(TORRENT_FILE_COUNT);
            info.mFilesInfoHash = getString(TORRENT_FILE_INFOS_HASH);
            info.mSelect = BTDatabaseHelper.getSubTask(resolver, info.mFilesInfoHash);
            info.mDownloadType = getInt(COLUMN_DOWNLOAD_TYPE);
            info.mIconUrl = getString(COLUMN_TASK_FOR_THUMBNAIL);
            String json = getString(COLUMN_EXTRA2);
            info.mDownloadExtras2 = json;
            info.mNotificationIconUrl = DownloadExtra2.getNotificationIconUrl(json);
            info.mSubPackage = DownloadExtra2.getSubPackage(json);
            info.mEnableM3u8 = DownloadExtra2.getEnableM3u8(json, vPackage);
            info.mCompleteClass = DownloadExtra2.getCompleteClass(json);

            if (Integer.valueOf(info.apkInstallWay) == 1) {
                if (DesktopProgressAppInfo.deleteApkFromDownloadlistSet.contains(info.apkPackageName)) {
                    return;
                }
                if (!DesktopProgressAppInfo.appInfoMap.containsKey(info.apkPackageName)) {
                    DesktopProgressAppInfo.appInfoMap.put(info.apkPackageName, new DesktopProgressAppInfo(info));
                }
                DesktopProgressAppInfo desktopProgressAppInfo = DesktopProgressAppInfo.appInfoMap.get(info.apkPackageName);
                int progress = info.mTotalBytes > 0 ? (int) ((float) info.mCurrentBytes / info.mTotalBytes * 100) : 0;
                desktopProgressAppInfo.updateStatus(progress, info.mStatus);
            }
            getSpeedUpColumn(info);

            String extras = getString(COLUMN_EXTRA);
            info.mDownloadExtras = extras;
            info.mDownloadFrom = DownloadExtra.getDownloadFrom(extras);
            info.mNotifyIsClear = DownloadExtra.notifyIsClear(extras);
            info.mPassBackStr = DownloadExtra.getPassBackStr(extras);

            info.mMediaStoreUri = getString(Downloads.Impl.COLUMN_MEDIASTORE_URI);
            info.mIsVisibleInDownloadsUi
                    = getInt(Downloads.Impl.COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI) != 0;

            // MIUI ADD FOR KCG: START
            try {
                info.mKcgDownloadExtra = getString(COLUMN_EXTRA2);
                info.mKcgTaskOpenMark = getInt(COLUMN_KCG_TASK_OPEN_MARK);
            } catch (Exception exception) {
            }
            // END
            info.mPercent = getInt(COLUMN_PERCENT);
            info.mProtocol = getInt(COLUMN_DOWNLOAD_TYPE);
        }

        public void getSpeedUpColumn(DownloadInfo info){
            String mSpTime =getString(COLUMN_TRYSPEEDUP_TIME);
            if(!TextUtils.isEmpty(mSpTime)){
                info.mSeepUpTrialDuration = Integer.parseInt(mSpTime.split(":")[0]);
                info.mSeepUpRemainDuration = Integer.parseInt(mSpTime.split(":")[1]);
                info.mSeepUpMode =getInt(COLUMN_TRYSPEEDUP_MODE);
            }
            info.mSeepUpStatus = getInt(COLUMN_TRYSPEEDUP_STATUS);
        }

        private void readRequestHeaders(DownloadInfo info) {
            info.mRequestHeaders.clear();
            Uri headerUri = Uri.withAppendedPath(info.getAllDownloadsUri(),
                    Downloads.Impl.RequestHeaders.URI_SEGMENT);
            Cursor cursor = null;
            try {
                cursor = mResolver.query(headerUri, null, null, null, null);
                int headerIndex =
                        cursor.getColumnIndexOrThrow(Downloads.Impl.RequestHeaders.COLUMN_HEADER);
                int valueIndex =
                        cursor.getColumnIndexOrThrow(Downloads.Impl.RequestHeaders.COLUMN_VALUE);
                for (cursor.moveToFirst(); !cursor.isAfterLast(); cursor.moveToNext()) {
                    addHeader(info, cursor.getString(headerIndex), cursor.getString(valueIndex));
                }
            } catch (Exception e) {
                logStaticD("error when readRequestHeaders: ", e);
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }

            if (info.mCookies != null) {
                addHeader(info, "Cookie", info.mCookies);
            }
            if (info.mReferer != null) {
                addHeader(info, "Referer", info.mReferer);
            }
        }

        private void addHeader(DownloadInfo info, String header, String value) {
            info.mRequestHeaders.add(Pair.create(header, value));
        }

        private String getString(String column) {
            int index = mCursor.getColumnIndexOrThrow(column);
            String s = mCursor.getString(index);
            return TextUtils.isEmpty(s) ? null : s;
        }

        private Integer getInt(String column) {
            return mCursor.getInt(mCursor.getColumnIndexOrThrow(column));
        }

        private Integer getInt(Cursor cursor, String column) {
            return cursor.getInt(cursor.getColumnIndexOrThrow(column));
        }

        private Long getLong(String column) {
            return mCursor.getLong(mCursor.getColumnIndexOrThrow(column));
        }
    }

    /**
     * Constants used to indicate network state for a specific download, after
     * applying any requested constraints.
     */
    public enum NetworkState {
        /**
         * The network is usable for the given download.
         */
        OK,

        /**
         * There is no network connectivity.
         */
        NO_CONNECTION,

        /**
         * The download exceeds the maximum size for this network.
         */
        UNUSABLE_DUE_TO_SIZE,

        /**
         * The download exceeds the recommended maximum size for this network,
         * the user must confirm for this download to proceed without WiFi.
         */
        RECOMMENDED_UNUSABLE_DUE_TO_SIZE,

        /**
         * The current connection is roaming, and the download can't proceed
         * over a roaming connection.
         */
        CANNOT_USE_ROAMING,

        /**
         * The app requesting the download specific that it can't use the
         * current network connection.
         */
        TYPE_DISALLOWED_BY_REQUESTOR,

        /**
         * Current network is blocked for requesting application.
         */
        BLOCKED,

        CANNOT_GET_CONTENT_LENGHT,

        /**
         * 被安全中心禁止连网
         */
        TYPE_DISALLOWED_BY_APP;
    }

    /**
     * For intents used to notify the user that a download exceeds a size
     * threshold, if this extra is true, WiFi is required for this download
     * size; otherwise, it is only recommended.
     */
    public static final String EXTRA_IS_WIFI_REQUIRED = "isWifiRequired";

    public long mId;
    public String mUri;
    public String mUriDomain;
    public boolean mNoIntegrity;
    public String mHint;
    public String mFileName;
    public String mMimeType;
    public int mDestination;
    public int mVisibility;
    public int mControl;
    public int mStatus;
    public int mNumFailed;
    public int mRetryAfter;
    public long mLastMod;
    public String mPackage;
    public String mSubPackage;
    public boolean mEnableM3u8;
    public String mClass;
    public String mCompleteClass;
    public String mExtras;
    public String mCookies;
    public String mUserAgent;
    public String mReferer;
    public long mTotalBytes;
    public long mFileSize;
    public long mCurrentBytes;
    public String mETag;
    public String mIfRange;
    public int mUid;
    public int mMediaScanned;
    public boolean mDeleted;
    public String mMediaProviderUri;
    public boolean mIsPublicApi;
    public int mAllowedNetworkTypes;
    public boolean mAllowRoaming;
    public boolean mAllowMetered;
    public String mTitle;
    public String mDescription;
    public int mBypassRecommendedSizeLimit;
    public String mSubDirectory;
    public String mAppointName;
    public boolean mInsufficientAlreadyPosted;
    public long mFileCreateTime;
    public long mDownloadingCurrentSpeed;
    public long mDownloadSurplustime;
    public long mXlAccelerateSpeed;
    public long mDownloadedTime;
    public String mErrorMsg;
    public int mXlVipStatus;
    public String mXlVipCdnUrl;
    public int mXlTaskOpenMark;
    public String fileHash;
    public String apkInstallWay;
    public String apkPackageName;
    public int mFuzz;
    // whether SizeLimitActivity has shown for one time.
    public boolean mHasNotifyDueToSize = false;
    public int mFileCount;
    public boolean isScanned = false;
    public String mFilesInfoHash;
    public HashSet<Integer> mSelect;
    private List<Pair<String, String>> mRequestHeaders = new ArrayList<Pair<String, String>>();
    public int mDownloadType;
    public String mIconUrl;
    public String mNotificationIconUrl;

    public int mSeepUpTrialDuration;
    public int mSeepUpRemainDuration;
    public int mSeepUpStatus;
    public int mSeepUpMode;

    public String mDownloadExtras;
    public String mDownloadExtras2;
    public int mDownloadFrom ;
    public String mPassBackStr;

    // MIUI ADD FOR KCG: START
    public String mKcgDownloadExtra;
    public int mKcgTaskOpenMark;
    // END
    public int mPercent;
    public int mProtocol;

    public boolean mNotifyIsClear = false;//用来标记当条通知是否被手动清除

    public boolean isM3u8Task() {
        return mProtocol == XLDownloadCfg.DownloadType.M3U8.ordinal();
    }
    /**
     * Result of last {@link DownloadThread} started by
     * {@link #startDownloadIfReady(ExecutorService)}.
     */
    @GuardedBy("this")
    private Future<?> mSubmittedTask;

    @GuardedBy("this")
    private Runnable mTask;

    private final Context mContext;
    private final SystemFacade mSystemFacade;
    private StorageManager mStorageManager;
    private DownloadNotifier mNotifier;

    private XLDownloadManager mXlDownloadManager = null;
    private XLVipChannelManager mXLVipChannelManager = null;

    public boolean mIsVisibleInDownloadsUi;

    public String mMediaStoreUri;

    public DownloadInfo(Context context) {
        mContext = context;
        mSystemFacade = new RealSystemFacade(context);
    }

    private DownloadInfo(Context context, SystemFacade systemFacade, StorageManager storageManager,
                         DownloadNotifier notifier, XLDownloadManager dm, XLVipChannelManager vm) {
        mContext = context;
        mSystemFacade = systemFacade;
        mInsufficientAlreadyPosted = false;
        mStorageManager = storageManager;
        mNotifier = notifier;
        mFuzz = Helpers.getsRandom().nextInt(1001);
        mXlDownloadManager = dm;
        mXLVipChannelManager = vm;
    }

    public Collection<Pair<String, String>> getHeaders() {
        return Collections.unmodifiableList(mRequestHeaders);
    }

    public void sendIntentIfRequested() {
        if (mPackage == null) {
            return;
        }

        Intent intent;
        if (mIsPublicApi) {
            intent = new Intent(DownloadManager.ACTION_DOWNLOAD_COMPLETE);
            if (TextUtils.isEmpty(mCompleteClass)) {
                intent.setPackage(mPackage);
            } else {
                intent.setClassName(mPackage, mCompleteClass);
            }
            intent.putExtra(DownloadManager.EXTRA_DOWNLOAD_ID, mId);
        } else {
            // legacy behavior
            if (mClass == null) {
                return;
            }
            intent = new Intent(Downloads.Impl.ACTION_DOWNLOAD_COMPLETED);
            intent.setClassName(mPackage, mClass);
            if (mExtras != null) {
                intent.putExtra(Downloads.Impl.COLUMN_NOTIFICATION_EXTRAS, mExtras);
            }
            // We only send the content: URI, for security reasons. Otherwise,
            // malicious
            // applications would have an easier time spoofing download results
            // by
            // sending spoofed intents.
            intent.setData(getMyDownloadsUri());
        }

        mSystemFacade.sendBroadcast(intent);

        if(!Helpers.isInternationalBuilder()) {
            autoInstall();
        }
    }

    private void autoInstall() {

        XLConfig.LOGD(TAG, "auto install: " + apkPackageName + " " + apkInstallWay + " hint: " + mHint + " muri: " + mUri + " fileHash" + fileHash);
        if (Integer.valueOf(apkInstallWay) == 1) {
            final InstallManager installManager = InstallManager.getInstance(mContext);
            installManager.installPackage(Uri.parse(mHint), apkPackageName);
        }
    }

    public void sendCompletedBroadcast() {
        if (!XLConfig.needShowInstallGuide()) {
            return;
        }
        if (TextUtils.isEmpty(apkPackageName)) {
            return;
        }
        if (!TextUtils.equals("com.xiaomi.market", mPackage)) {
            return;
        }
        Intent intent = new Intent(ActionConstants.ACTION_MARKET_APK_COMPLETED);
        intent.putExtra(ActionConstants.EXTRA_DOWNLOAD_APK_PKG, apkPackageName);
        mSystemFacade.sendBroadcast(intent);
    }

    /**
     * Send download progress update intent if request
     *
     * @hide
     */
    public void sendDownloadProgressUpdateIntent() {
        if (mPackage == null) {
            return;
        }

        Intent intent = new Intent(DownloadManager.ACTION_DOWNLOAD_UPDATED);
        intent.setPackage(mPackage);
        intent.putExtra(DownloadManager.EXTRA_DOWNLOAD_ID, mId);
        intent.putExtra(DownloadManager.EXTRA_DOWNLOAD_CURRENT_BYTES, mCurrentBytes);
        intent.putExtra(DownloadManager.EXTRA_DOWNLOAD_TOTAL_BYTES, mTotalBytes);
        mSystemFacade.sendBroadcast(intent);
    }

    /**
     * Returns the time when a download should be restarted.
     */
    public long restartTime(long now) {
        if (mNumFailed == 0) {
            return now;
        }
        if (mRetryAfter > 0) {
            return mLastMod + mRetryAfter;
        }
        return mLastMod + Constants.RETRY_FIRST_DELAY * (1000 + mFuzz) * (1 << (mNumFailed - 1));
    }

    /**
     * Returns whether this download should be enqueued.
     */
    private boolean isReadyToDownload(boolean isActive) {
        if (mControl == Downloads.Impl.CONTROL_PAUSED) {
            // the download is paused, so it's not going to start
            return false;
        }
        switch (mStatus) {
            case 0: // status hasn't been initialized yet, this is a new
                // download
            case Downloads.Impl.STATUS_PENDING: // download is explicit marked
                // as ready to start
            case Downloads.Impl.STATUS_RUNNING: // download interrupted (process
                // killed etc) while
                // running, without a chance to update the database
            case Downloads.Impl.STATUS_WAITING_FOR_NETWORK:
            case Downloads.Impl.STATUS_QUEUED_FOR_WIFI:
                NetworkState checkCanUseNetwork = checkCanUseNetwork(false);
                fixStatusByNetworkState(checkCanUseNetwork);
                logD("isReadyToDownload checkCanUseNetwork = " + checkCanUseNetwork);
                return checkCanUseNetwork == NetworkState.OK;
            case Downloads.Impl.STATUS_WAITING_TO_RETRY:
                // download was waiting for a delayed restart
                final long now = mSystemFacade.currentTimeMillis();
                return restartTime(now) <= now;
            case Downloads.Impl.STATUS_DEVICE_NOT_FOUND_ERROR:
                // is the media mounted?
                return checkStorageState();
            case Downloads.Impl.STATUS_INSUFFICIENT_SPACE_ERROR:
                // should check space to make sure it is worth retrying the
                // download.
                return hasEnoughSpace();
            default:
                break;
        }
        return false;
    }

    public boolean checkStorageState() {
        boolean isMounted = false;
        if (TextUtils.isEmpty(mFileName)) {
            if (mDestination == Downloads.Impl.DESTINATION_FILE_URI) {
                isMounted = mStorageManager.isExternalSdcardMounted(mContext, mHint);
            } else {
                XLConfig.LOGD_INFO("checkStorageState no handle mDestination=" + mDestination);
            }
        } else {
            isMounted = mStorageManager.isExternalSdcardMounted(mContext, mFileName);
        }
        return isMounted;
    }

    public void fixStatusByNetworkState(NetworkState networkUsable) {
        if (networkUsable == NetworkState.OK) {
            return;
        }

        String errorMsg = null;
        if (networkUsable == NetworkState.UNUSABLE_DUE_TO_SIZE ||
                networkUsable == NetworkState.RECOMMENDED_UNUSABLE_DUE_TO_SIZE) {
            errorMsg = generateReason(STATUS_NET_UNUSABLE_DUE_TO_SIZE, networkUsable.name());
        } else if (networkUsable == NetworkState.TYPE_DISALLOWED_BY_REQUESTOR) {
            errorMsg = generateReason(STATUS_NET_DISALLOWED_BY_REQUESTOR, networkUsable.name());
        } else if (networkUsable == NetworkState.TYPE_DISALLOWED_BY_APP) {
            errorMsg = generateReason(STATUS_NET_DISALLOWED_BY_APP, networkUsable.name());
        }

        int status = networkUsable == NetworkState.NO_CONNECTION ? STATUS_WAITING_FOR_NETWORK : STATUS_QUEUED_FOR_WIFI;
        if (TextUtils.isEmpty(errorMsg)) {
            errorMsg = networkUsable.name();
        }
        updateStatus(status, errorMsg);
    }

    /**
     * Returns whether this download has a visible notification after
     * completion.
     */
    public boolean hasCompletionNotification() {
        if (!Downloads.Impl.isStatusCompleted(mStatus)) {
            return false;
        }
        if (mVisibility == Downloads.Impl.VISIBILITY_VISIBLE_NOTIFY_COMPLETED) {
            return true;
        }
        return false;
    }

    /**
     * Returns whether this download is allowed to use the network.
     */
    public NetworkState checkCanUseNetwork(boolean isTaskRunning) {
        final NetworkInfo info = mSystemFacade.getActiveNetworkInfo();
        if (info == null || !info.isConnected()) {
            return NetworkState.NO_CONNECTION;
        }
        if (DetailedState.BLOCKED.equals(info.getDetailedState())) {
            return NetworkState.BLOCKED;
        }
        if (mSystemFacade.isNetworkRoaming() && !isRoamingAllowed()) {
            return NetworkState.CANNOT_USE_ROAMING;
        }
        //将付费网络当成流量网络处理
//        if (mSystemFacade.isActiveNetworkMetered() && !mAllowMetered) {
//            return NetworkState.TYPE_DISALLOWED_BY_REQUESTOR;
//        }
        return checkIsNetworkTypeAllowed(isTaskRunning, info.getType());
    }

    public String  generateReason(int errorCode,String errorMsg){
        return errorCode+":"+errorMsg;
    }

    public int getErrorCodByReason(String reason) {
        int code = -1;
        if (TextUtils.isEmpty(reason)) {
            return code;
        }

        try {
            code = Integer.parseInt(reason.substring(0, reason.indexOf(':')));
        } catch (Exception e) {
            XLConfig.LOGD("exc", e);
        }
        return code;
    }

    public String getDownloadInfo() {
        return "mId=" + mId;
    }

    private boolean isRoamingAllowed() {
        if (mIsPublicApi) {
            return mAllowRoaming;
        } else { // legacy behavior
            return mDestination != Downloads.Impl.DESTINATION_CACHE_PARTITION_NOROAMING;
        }
    }

    /**
     * Check if this download can proceed over the given network type.
     *
     * @param networkType a constant from ConnectivityManager.TYPE_*.
     * @return one of the NETWORK_* constants
     */
    private NetworkState checkIsNetworkTypeAllowed(boolean isTaskRunning, int networkType) {
        if (mIsPublicApi) {
            final int flag = translateNetworkTypeToApiFlag(networkType);
            final boolean allowAllNetworkTypes = mAllowedNetworkTypes == ~0;
            if (!allowAllNetworkTypes && (flag & mAllowedNetworkTypes) == 0) {
                Log.i(TAG, "checkIsNetworkTypeAllowed TYPE_DISALLOWED_BY_REQUESTOR flag="
                        + flag
                        + " allow netTypes is " + mAllowedNetworkTypes);
                return NetworkState.TYPE_DISALLOWED_BY_REQUESTOR;
            }
        }
        if (isNetDisallowedByApp(networkType)) {
            Log.i(TAG, String.format("isNetDisallowedByApp %s  networkType=%d", mPackage, networkType));
            return NetworkState.TYPE_DISALLOWED_BY_APP;
        }
        return checkSizeAllowedForNetwork(isTaskRunning, networkType);
    }

    private boolean isNetDisallowedByApp(int networkType) {
        String pkg = mContext.getPackageName();
        return NetworkUtils.isNetDisallowed(mContext, pkg, networkType) |
                NetworkUtils.isNetDisallowed(mContext, mPackage, networkType);
    }

    /**
     * Translate a ConnectivityManager.TYPE_* constant to the corresponding
     * DownloadManager.Request.NETWORK_* bit flag.
     */
    private int translateNetworkTypeToApiFlag(int networkType) {
        switch (networkType) {
            case ConnectivityManager.TYPE_MOBILE:
                return DownloadManager.Request.NETWORK_MOBILE;

            case ConnectivityManager.TYPE_WIFI:
            case ConnectivityManager.TYPE_ETHERNET:
                if (mSystemFacade.isActiveNetworkMetered()) {
                    return DownloadManager.Request.NETWORK_MOBILE;
                } else {
                    return DownloadManager.Request.NETWORK_WIFI;
                }

            case ConnectivityManager.TYPE_BLUETOOTH:
                return DownloadManager.Request.NETWORK_BLUETOOTH;

            default:
                return 0;
        }
    }

    /**
     * Check if the download's size prohibits it from running over the current
     * network.
     *
     * @return one of the NETWORK_* constants
     */
    private NetworkState checkSizeAllowedForNetwork(boolean isTaskRunning, int networkType) {
        // 移动送测的特殊处理
        if (Helpers.isCmTestBuilder()) {
            return NetworkState.OK;
        }

        if (networkType == ConnectivityManager.TYPE_WIFI ||
                networkType == ConnectivityManager.TYPE_ETHERNET || networkType == ConnectivityManager.TYPE_BLUETOOTH) {
            // anything goes over wifi
            boolean activeNetworkMetered = mSystemFacade.isActiveNetworkMetered();
            logD("checkSizeAllowedForNetwork " + "isMeteredNetwork="+activeNetworkMetered+" user isAllowMetered="+mAllowMetered);
            if (!activeNetworkMetered) {
                return NetworkState.OK;
            }

        }
        logInfo("checkSizeAllowedForNetwork " + getDownloadInfo() + " isTaskRunning="
                + isTaskRunning
                + ", networkType=" + networkType + ", mTotalBytes=" + mTotalBytes + ", mStatus="
                + mStatus + " mBypassRecommendedSizeLimit=" + mBypassRecommendedSizeLimit);
        if (mTotalBytes == 0 || mBypassRecommendedSizeLimit == 1) {
            return NetworkState.OK;
        }

        Long recommendedMaxBytesOverMobile = mSystemFacade.getRecommendedMaxBytesOverMobile();
        logD("checkSizeAllowedForNetwork recommendedMaxBytesOverMobile="
                + recommendedMaxBytesOverMobile
                + ", MAX_BYTES_OVER_MOBILE=" + MAX_BYTES_OVER_MOBILE);
        if (recommendedMaxBytesOverMobile != null && recommendedMaxBytesOverMobile >= MAX_BYTES_OVER_MOBILE) {
            return NetworkState.OK;
        }

        if (mTotalBytes < 0) {
            logD("checkSizeAllowedForNetwork don't know file size, return NetworkState.UNUSABLE_DUE_TO_SIZE");
            return NetworkState.UNUSABLE_DUE_TO_SIZE; // we don't know the size
            // yet
        }

        Long maxBytesOverMobile = mSystemFacade.getMaxBytesOverMobile();
        logD("checkSizeAllowedForNetwork maxBytesOverMobile=" + maxBytesOverMobile);
        if (maxBytesOverMobile != null && mTotalBytes > maxBytesOverMobile) {
            return NetworkState.UNUSABLE_DUE_TO_SIZE;
        }

        if (mBypassRecommendedSizeLimit == 0 && recommendedMaxBytesOverMobile != null
                && mTotalBytes > recommendedMaxBytesOverMobile) {
            Log.w(TAG, "size not valid, return RECOMMENDED_UNUSABLE_DUE_TO_SIZE");
            return NetworkState.RECOMMENDED_UNUSABLE_DUE_TO_SIZE;
        }

        logD("finally return OK");
        return NetworkState.OK;
    }

    private boolean updateStatus(int status) {
        return updateStatus(status, null);
    }

    private boolean updateStatus(int status, String error_msg) {
        if (status == mStatus && TextUtils.equals(mErrorMsg, error_msg)) {
            return true;
        }

        if (checkStatusEnd()) {
            return false;
        }

        boolean result = false;
        ContentValues values = new ContentValues();
        values.put(Downloads.Impl.COLUMN_ERROR_MSG, !TextUtils.isEmpty(error_msg) ? error_msg : "");
        values.put(Downloads.Impl.COLUMN_STATUS, status);
        int update = mContext.getContentResolver().update(getAllDownloadsUri(), values, null, null);
        if (update > 0) {
            mStatus = status;
            result = true;
        }
        return result;
    }

    /**
     * DownloadInfo中因为此时的状态具有延时性，所以在修正状态之前必须查一下状态是否能修正，此时只能修改非结束状态的状态
     * @return
     */
    public boolean checkStatusEnd() {
        boolean isEnd = false;
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(getAllDownloadsUri(),
                    new String[]{COLUMN_STATUS}, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                int columnStatus = cursor.getColumnIndex(COLUMN_STATUS);
                int status = cursor.getInt(columnStatus);
                //是否是结束状态，包含暂停，失败，完成
                isEnd = isStatusEnd(status);
            }
        } catch (Exception e) {
            XLConfig.LOGD("exc", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return isEnd;
    }

    private boolean isStatusEnd(int status) {
        return status == STATUS_PAUSED_BY_APP || Downloads.Impl.isStatusCompleted(status);
    }


    /**
     * If download is ready to start, and isn't already pending or executing,
     * create a {@link DownloadThread} and enqueue it into given
     * {@link Executor}.
     *
     * @return If actively downloading.
     */
    public boolean startDownloadIfReady(ExecutorService executor) {
        synchronized (this) {
            final boolean isActive = mSubmittedTask != null && !mSubmittedTask.isDone();
            if (isActive) {
                if (mTask != null) {
                    DownloadThread thread = (DownloadThread) mTask;
                    if (!thread.isRunning() && XLDownloadHelper.isBtTask(mUri)) {
                        checkBtStatus();
                    }
                }
                return true;
            }
            logD("in startDownloadIfReady " + getDownloadInfo()
                    + ",mTitle=" + (TextUtils.isEmpty(mTitle) ? "" : mTitle)
                    + ",mControl=" + mControl
                    + ", mStatus=" + Downloads.Impl.statusToString(mStatus));
            final boolean isReady = isReadyToDownload(isActive);
            boolean run = isReady && !isActive;
            if (run) {
                logD("in startDownloadIfReady " + getDownloadInfo() + " isReady="
                        + isReady
                        + ",mTitle="
                        + (TextUtils.isEmpty(mTitle) ? "" : mTitle) + ", mStatus="
                        + Downloads.Impl.statusToString(mStatus));
                if (mStatus != Impl.STATUS_RUNNING) {
                    updateStatus(Impl.STATUS_RUNNING);
                }
                mTask = getDownloadThread();
                mSubmittedTask = executor.submit(mTask);
            } else {
                if (XLDownloadHelper.isBtTask(mUri)) {
                    checkBtStatus();
                }
            }
            return isReady;
        }
    }

    /**
     * If download is ready to start, and isn't already pending or executing,
     * create a {@link DownloadThread} and enqueue it into given
     * {@link Executor}.
     *
     * @return If actively downloading.
     */
    public boolean startDownloadIfReadyNew(ExecutorService exe) {
        DownloadService.PkgThreadPoolExecutor executor = (DownloadService.PkgThreadPoolExecutor) exe;
        synchronized (this) {

            //检查当前任务是否已经在运行状态
            if (executor.isRunning(this)) {
                if (mTask != null) {
                    DownloadThread thread = (DownloadThread) mTask;
                    if (!thread.isRunning() && XLDownloadHelper.isBtTask(mUri)) {
                        checkBtStatus();
                    } else if (thread.isRunning() && mStatus == Impl.STATUS_PENDING) {
                        //处理快速点击时任务在下载，状态为STATUS_PENDING
                        updateStatus(Impl.STATUS_RUNNING);
                    } else {
                        //don't handle
                    }
                }
                return true;
            }
            logD("in startDownloadIfReady " + getDownloadInfo()
                    + ",mTitle=" + (TextUtils.isEmpty(mTitle) ? "" : mTitle)
                    + ",mControl=" + mControl
                    + ", mStatus=" + Downloads.Impl.statusToString(mStatus));
            //2）检查任务是否准备好了
            if (!isReadyToDownload(false)) {
                if (XLDownloadHelper.isBtTask(mUri)) {
                    checkBtStatus();
                }
                return false;
            }

            //如果没有子任务，说明子任务还没有插入进来，所以需要等待
            if (XLDownloadHelper.isBtTask(mUri) && (mSelect == null || mSelect.isEmpty()))
                return false;
        }

        //3)是否超过每个app的同时下载线程和总共同时运行的线程限制
        if (!executor.checkCanAdd(this)) {
            if (XLDownloadHelper.isBtTask(mUri)) {
                checkBtStatus();
            }
            updateStatus(Impl.STATUS_PENDING);
            return false;
        }
        logD("in startDownloadIfReady " + getDownloadInfo()
                + ",mTitle=" + (TextUtils.isEmpty(mTitle) ? "" : mTitle)
                + ", mStatus=" + Downloads.Impl.statusToString(mStatus));
        if (!updateStatus(Impl.STATUS_RUNNING)) {
            return false;
        }
        mTask = getDownloadThread();
        mSubmittedTask = executor.submit(mTask);
        return true;
    }

    private void checkBtStatus() {
        if (!Downloads.Impl.isStatusCompleted(mStatus)) {
            logD(String.format("mStatus= %d", mStatus));
            int subTaskStatus = (mStatus == STATUS_RUNNING || mStatus == STATUS_PENDING) ? STATUS_PENDING
                    : mStatus;
            StringBuilder sqlWhere = new StringBuilder();
            sqlWhere.append(String.format("(%s = ?)", TORRENT_FILE_INFOS_HASH));
            sqlWhere.append(" AND ");
            sqlWhere.append(String.format("(%s AND %s)",
                    BTDatabaseHelper.statusClause(">=", 100),
                    BTDatabaseHelper.statusClause("<", 200)));
            String[] args = new String[] {
                    mFilesInfoHash
            };
            ContentValues values = new ContentValues();
            values.put(TORRENT_STATUS, subTaskStatus);
            // 将所有非完成状态的子任务改为pending
            BTDatabaseHelper.updateBtDetail(mContext.getContentResolver(), values,
                    sqlWhere.toString(), args);
        }
    }

    private Runnable getDownloadThread() {
        if (Helpers.isTablet()) {
            mTask = getAospDownloadThread();
            return mTask;
        }
        DownloadType type = getDownloadType(mUri);
        if (type == DownloadType.BT) {
            mTask = new BTDownloadThread(mContext, mSystemFacade, this,
                    mStorageManager,
                    mNotifier, mXlDownloadManager, mXLVipChannelManager);
        } else if (type == DownloadType.MAGNET) {
            mTask = new MagnetDownloadThread(mContext, mSystemFacade, this,
                    mStorageManager,
                    mNotifier, mXlDownloadManager, mXLVipChannelManager);
        } else if (type == DownloadType.FTP) {
            mTask = new FtpDownloadThread(mContext, mSystemFacade, this,
                    mStorageManager,
                    mNotifier, mXlDownloadManager, mXLVipChannelManager);
        } else if (type == DownloadType.EMULE) {
            mTask = new EmuleDownloadThread(mContext, mSystemFacade, this,
                    mStorageManager,
                    mNotifier, mXlDownloadManager, mXLVipChannelManager);
        } else if (type == DownloadType.M3U8) {
            mTask = new M3u8DownloadThread(mContext, mSystemFacade, this,
                    mStorageManager,
                    mNotifier, mXlDownloadManager, mXLVipChannelManager);
        } else if (type == DownloadType.HTTP || type == DownloadType.HTTPS) {
            // MIUI MOD FOR KCG: START
            if (mXlTaskOpenMark == 0) {
                mTask = getAospDownloadThread();
            } else {
                boolean useXl = true;
                try {
                    String host = new URL(mUri).getHost();
                    if (TextUtils.equals(host, "localhost")) {
                        useXl = false;
                    }
                } catch (MalformedURLException e) {
                    e.printStackTrace();
                }

                if (useXl) {
                    mTask = new XLDownloadThread(mContext, mSystemFacade, this,
                            mStorageManager,
                            mNotifier, mXlDownloadManager, mXLVipChannelManager);
                } else {
                    mTask = getAospDownloadThread();
                }
            }
        } else {
            mTask = getAospDownloadThread();
            logD("not supported downloadType uri=" + mUri);
        }
        return mTask;
    }

    private @NonNull Runnable getAospDownloadThread() {
        if (DownloadExtra2.isFileOptimize(mDownloadExtras2)) {
            return new OptDownloadThread(mContext, mSystemFacade, this, mStorageManager,
                    mNotifier);
        }
        return new DownloadThread(mContext, mSystemFacade, this, mStorageManager,
                mNotifier);
    }

    public DownloadType getDownloadType(String url) {
        DownloadType type = XLDownloadHelper.getDownloadType(url);
        boolean m3U8Mimetype = VideoUtils.isM3U8Mimetype(mMimeType);
        if (type != DownloadType.M3U8 && m3U8Mimetype) {
            type = DownloadType.M3U8;
        }
        if (type == DownloadType.M3U8) {
            if (!mEnableM3u8) {//当成正常的http/https下载
                String preStr = url.substring(0, 7).toLowerCase();
                if (preStr.startsWith("https")) {
                    type = DownloadType.HTTPS;
                } else if (preStr.startsWith("http")) {
                    type = DownloadType.HTTP;
                }
            }
        }
        return type;
    }

    /**
     * If download is ready to be scanned, enqueue it into the given
     * {@link DownloadScanner}.
     *
     * @return If actively scanning.
     */
    public boolean startScanIfReady(DownloadScanner scanner) {
        synchronized (this) {
            final boolean isReady = shouldScanFile();
            if (isReady) {
                scanner.requestScan(this);
                isScanned = true;
            }
            return isReady;
        }
    }

    public boolean isOnCache() {
        return mDestination == Downloads.Impl.DESTINATION_CACHE_PARTITION
                || mDestination == Downloads.Impl.DESTINATION_SYSTEMCACHE_PARTITION
                || mDestination == Downloads.Impl.DESTINATION_CACHE_PARTITION_NOROAMING
                || mDestination == Downloads.Impl.DESTINATION_CACHE_PARTITION_PURGEABLE;
    }

    public Uri getMyDownloadsUri() {
        return ContentUris.withAppendedId(Downloads.Impl.CONTENT_URI, mId);
    }

    public Uri getAllDownloadsUri() {
        return ContentUris.withAppendedId(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, mId);
    }

    public void dump(IndentingPrintWriter pw) {
        pw.println("DownloadInfo:");
        pw.increaseIndent();
        pw.printPair("mId", mId);
        pw.printPair("mLastMod", mLastMod);
        pw.printPair("mPackage", mPackage);
        pw.printPair("mUid", mUid);
        pw.println();

        pw.printPair("mTitle", !TextUtils.isEmpty(mTitle) ? mTitle : "");
        pw.printPair("mFileName", !TextUtils.isEmpty(mFileName) ? mFileName : "");
        pw.println();

        pw.printPair("mUri", mUri);
        pw.println();

        pw.printPair("mMimeType", mMimeType);
        pw.printPair("mCookies", (mCookies != null) ? "yes" : "no");
        pw.printPair("mReferer", (mReferer != null) ? "yes" : "no");
        pw.printPair("mUserAgent", mUserAgent);
        pw.println();

        pw.printPair("mFileName", mFileName);
        pw.printPair("mDestination", mDestination);
        pw.println();

        pw.printPair("mStatus", Downloads.Impl.statusToString(mStatus));
        pw.printPair("mErrorMsg", mErrorMsg);
        pw.printPair("mCurrentBytes", mCurrentBytes);
        pw.printPair("mTotalBytes", mTotalBytes);
        pw.println();

        pw.printPair("mNumFailed", mNumFailed);
        pw.printPair("mRetryAfter", mRetryAfter);
        pw.printPair("mETag", mETag);
        pw.printPair("mIsPublicApi", mIsPublicApi);
        pw.println();

        pw.printPair("mAllowedNetworkTypes", mAllowedNetworkTypes);
        pw.printPair("mAllowRoaming", mAllowRoaming);
        pw.printPair("mAllowMetered", mAllowMetered);
        pw.println();

        pw.printPair("mKcgDownloadExtra", mKcgDownloadExtra);
        pw.printPair("mKcgTaskOpenMark", mKcgTaskOpenMark);
        pw.println();
        pw.decreaseIndent();
    }

    /**
     * Return time when this download will be ready for its next action, in
     * milliseconds after given time.
     *
     * @return If {@code 0}, download is ready to proceed immediately. If
     *         {@link Long#MAX_VALUE}, then download has no future actions.
     */
    public long nextActionMillis(long now) {
        if (Downloads.Impl.isStatusCompleted(mStatus)) {
            return Long.MAX_VALUE;
        }
        if (mStatus == Downloads.Impl.STATUS_PAUSED_BY_APP) {
            return Long.MAX_VALUE;
        }
        if (mStatus != Downloads.Impl.STATUS_WAITING_TO_RETRY) {
            return 0;
        }
        long when = restartTime(now);
        if (when <= now) {
            return 0;
        }
        return when - now;
    }

    /**
     * Returns whether a file should be scanned
     */
    public boolean shouldScanFile() {
        return (mMediaScanned == 0) && !isScanned
                && (mDestination == Downloads.Impl.DESTINATION_EXTERNAL ||
                        mDestination == Downloads.Impl.DESTINATION_FILE_URI ||
                mDestination == Downloads.Impl.DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD)
                && Downloads.Impl.isStatusSuccess(mStatus);
    }

    boolean hasEnoughSpace() {
        boolean spaceIsEnough = false;
        try {
            mStorageManager.verifySpace(mDestination, mFileName, mTotalBytes
                    - mCurrentBytes);
            spaceIsEnough = true;
        } catch (StopRequestException e) {
            XLConfig.LOGD("verify sapce StopRequestException" + e.getMessage(), e);
        } catch (Exception e) {
            XLConfig.LOGD("verify sapce fail " + e.getMessage(), e);
        }
        return spaceIsEnough;
    }


    void notifyPauseDueToSize(boolean isWifiRequired) {
        if (Helpers.isCmTestBuilder()) {
            boolean isWarningShowing= DownloadSettings.XLSecureConfigSettings.isWarnActivityShowing(false);
            if (!isWarningShowing) {
                DownLoadProviderUtils.notifyPauseDueToSize(mContext, mId, isWifiRequired);
            }
        } else {
            DownLoadProviderUtils.notifyPauseDueToSize(mContext, mId, isWifiRequired);
        }
    }


    /**
     * Whether Send insufficient space nofification
     *
     * @hide
     */
    boolean hasPostNotificationDueToInsufficientSpace() {
        return mInsufficientAlreadyPosted;
    }

    /**
     * get whether Send insufficient space nofification
     *
     * @hide
     */
    void setPostNotificationDueToInsufficientSpace() {
        mInsufficientAlreadyPosted = true;
    }

    /**
     * get notification string
     *
     * @hide
     */
    String getNotificationStringOfInsufficientSpace() {
        int notifyResId = isOnExternalStorage(getLocalUri()) ?
                R.string.dialog_insufficient_space_on_external :
                R.string.dialog_insufficient_space_on_cache;
        return mContext.getString(notifyResId);
    }

    /**
     * Query and return status of requested download.
     */
    public static int queryDownloadStatus(ContentResolver resolver, long id) {
        Cursor cursor = null;
        try {
            cursor = resolver.query(
                    ContentUris.withAppendedId(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, id),
                    new String[] {
                        Downloads.Impl.COLUMN_STATUS
                    }, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                return cursor.getInt(0);
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        // TODO: increase strictness of value returned for unknown
        // downloads; this is safe default for now.
        return Downloads.Impl.STATUS_PENDING;
    }

    /**
     * Query and return status of requested download.
     */
    public static Cursor queryDownloadInfo(ContentResolver resolver, long id) {
        return resolver.query(
                ContentUris.withAppendedId(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, id),
                new String[]{
                        Downloads.Impl.COLUMN_STATUS
                        ,COLUMN_EXTRA
                        ,COLUMN_EXTRA2
                }, null, null, null);
    }

    boolean isOnExternalStorage(String localUriString) {
        if (localUriString == null) {
            return false;
        }
        Uri localUri = Uri.parse(localUriString);
        if (!"file".equals(localUri.getScheme())) {
            return false;
        }
        String path = localUri.getPath();
        String externalRoot = Environment.getExternalStorageDirectory().getPath();
        return path.startsWith(externalRoot);
    }

    String getLocalUri() {
        switch (mDestination) {
            case Downloads.Impl.DESTINATION_FILE_URI:
                return mHint;
            case Downloads.Impl.DESTINATION_EXTERNAL:
            case Downloads.Impl.DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD:
                return mFileName != null ? Uri.fromFile(new File(mFileName)).toString() : null;
            default:
                return getAllDownloadsUri().toString();
        }
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("DownloadInfo:\n\t");
        sb.append("mId=").append(mId).append("\n\t");
        sb.append("mLastMod=").append(mLastMod).append("\n\t");
        sb.append("mPackage=").append(mPackage).append("\n\t");
        sb.append("mUid=").append(mUid).append("\n\t");
        sb.append("mUri=").append(mUri).append("\n\t");
        sb.append("mMimeType=").append(mMimeType).append("\n\t");
        sb.append("mCookies=").append((mCookies != null) ? "yes" : "no").append("\n\t");
        sb.append("mReferer=").append((mReferer != null) ? "yes" : "no").append("\n\t");
        sb.append("mUserAgent=").append(mUserAgent).append("\n\t");
        sb.append("mFileName=").append(mFileName).append("\n\t");
        sb.append("mDestination=").append(mDestination).append("\n\t");
        sb.append("mStatus=").append(Downloads.Impl.statusToString(mStatus)).append("\n\t");
        sb.append("mCurrentBytes=").append(mCurrentBytes).append("\n\t");
        sb.append("mTotalBytes=").append(mTotalBytes).append("\n\t");
        sb.append("mNumFailed=").append(mNumFailed).append("\n\t");
        sb.append("mRetryAfter=").append(mRetryAfter).append("\n\t");
        sb.append("mETag=").append(mETag).append("\n\t");
        sb.append("mIsPublicApi=").append(mIsPublicApi).append("\n\t");
        sb.append("mAllowedNetworkTypes=").append(mAllowedNetworkTypes).append("\n\t");
        sb.append("mAllowRoaming=").append(mAllowRoaming).append("\n\t");
        sb.append("mAllowMetered=").append(mAllowMetered).append("\n\t");
        sb.append("mXlTaskOpenMark=").append(mXlTaskOpenMark).append("\n\t");
        sb.append("mDownloadExtras=").append(mDownloadExtras).append("\n\t");
        sb.append("mDownloadExtras2=").append(mDownloadExtras2).append("\n\t");
        sb.append("mDownloadFrom=").append(mDownloadFrom).append("\n\t");
        sb.append("mNotifyIsClear=").append(mNotifyIsClear).append("\n\t");
        sb.append("mPassBackStr=").append(mPassBackStr).append("\n\t");
        return sb.toString();
    }

    private void logInfo(String msg) {
        XLConfig.LOGD_INFO(this.getClass().getSimpleName(), msg);
    }

    private void logD(String msg) {
        XLConfig.LOGD(this.getClass().getSimpleName(), getTask() + msg);
    }

    private void logD(String msg, Throwable t) {
        XLConfig.LOGD(this.getClass().getSimpleName(), getTask() + msg, t);
    }

    private static void logStaticD(String msg) {
        XLConfig.LOGD(TAG, msg);
    }

    private static void logStaticD(String msg, Throwable t) {
        XLConfig.LOGD(TAG, msg, t);
    }

    private String getTask() {
        return "mId=" + mId + " ";
    }

}
