/*
 * Copyright (C) 2008 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 **
 **
 */

package com.android.providers.downloads.service;

import android.app.AlarmManager;
import android.app.DownloadManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.sqlite.SQLiteDiskIOException;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Message;
import android.os.Process;
import android.provider.Downloads;
import android.text.TextUtils;
import android.util.Log;

import com.android.internal.annotations.GuardedBy;
import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.DownloadThread;
import com.android.providers.downloads.RealSystemFacade;
import com.android.providers.downloads.StorageManager;
import com.android.providers.downloads.SystemFacade;
import com.android.providers.downloads.api.notifyrecommend.NewDownloadRequestBase;
import com.android.providers.downloads.config.Constants;
import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.MobileDataConfig;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.m3u8.M3U8Parse;
import com.android.providers.downloads.provider.DownloadProvider;
import com.android.providers.downloads.scdn.ScdnHelper;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.setting.PrivacySettingHelper;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.statistics.TraceReport;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.FileUtil;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.IndentingPrintWriter;
import com.android.providers.downloads.util.LimitSpeedUtil;
import com.android.providers.downloads.util.NetworkUtils;
import com.android.providers.downloads.util.ProcessUtil;
import com.android.providers.downloads.util.ProvisionHelper;
import com.android.providers.downloads.util.TokenHelper;
import com.android.providers.downloads.util.XLUtil;
import com.android.providers.downloads.utils.AESUtils;
import com.android.providers.downloads.utils.LogUtil;
import com.android.providers.downloads.xunlei.notifyrecommend.NotifyRecommendManager;
import com.google.android.collect.Maps;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Sets;
import com.xunlei.downloadlib.XLDownloadManager;
import com.xunlei.vipchannel.XLVipChannelManager;

import java.io.FileDescriptor;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


import static android.text.format.DateUtils.MINUTE_IN_MILLIS;

/**
 * Performs background downloads as requested by applications that use
 * {@link DownloadManager}. Multiple start commands can be issued at this
 * service, and it will continue running until no downloads are being actively
 * processed. It may schedule alarms to resume downloads in future.
 * <p>
 * Any database updates important enough to initiate tasks should always be
 * delivered through {@link Context#startService(Intent)}.
 */
public class DownloadService extends Service implements XLDownloadCfg {
    // TODO: migrate WakeLock from individual DownloadThreads out into
    // DownloadReceiver to protect our entire workflow.

    @VisibleForTesting
    SystemFacade mSystemFacade;

    private AlarmManager mAlarmManager;
    private StorageManager mStorageManager;

    /** Observer to get notified when the content observer's data changes */
    private DownloadManagerContentObserver mObserver;
    private FirewallObserver mFirewallObserver;

    /** Class to handle Notification Manager updates */
    private DownloadNotifier mNotifier;
    public final static String ACCOUNTSRC_PREFERENCE = "xl_accountinfo_src";

    /** Xunlei DownloadManager */
    private XLDownloadManager mXlDownloadManager = mXlDownloadManager = null;
    private XLVipChannelManager mXLVipChannelManager;
    private boolean mXunleiEngineEnable = false;

    public static final int MAX_DOWNLOAD_CONCURRENT = miui.os.Build.IS_CM_CUSTOMIZATION_TEST ? 3
            : 2;

    public static final long XUNLEI_VIP_ENABLED = 3;
    public static final String PREF_NAME_IN_UI = "com.android.providers.downloads.ui_preferences";
    public static final String PREF_KEY_XUNLEI_VIP = "xl_optdownload_flag";

    private final String sUnknownPackage = "unknown";
    private static final int DOWNLOAD_SERVICE_START = 1;
    private static final int DOWNLOAD_SERVICE_STOP = 2;

    /**
     * The Service's view of the list of downloads, mapping download IDs to the
     * corresponding info object. This is kept independently from the content
     * provider, and the Service only initiates downloads based on this data, so
     * that it can deal with situation where the data in the content provider
     * changes or disappears.
     */
    @GuardedBy("mDownloads")
    private final Map<Long, DownloadInfo> mDownloads = Maps.newHashMap();

    private static final int MSG_UPDATE = 1;
    private static final int MSG_FINAL_UPDATE = 2;
    private static final String TAG = "DownloadService";

    /*
     * private final ExecutorService mExecutor = buildDownloadExecutor();
     * private static ExecutorService buildDownloadExecutor() { final int
     * maxConcurrent = Resources.getSystem().getInteger(
     * com.android.internal.R.integer.config_MaxConcurrentDownloadsAllowed); //
     * Create a bounded thread pool for executing downloads; it creates //
     * threads as needed (up to maximum) and reclaims them when finished. final
     * ThreadPoolExecutor executor = new ThreadPoolExecutor( maxConcurrent,
     * maxConcurrent, 10, TimeUnit.SECONDS, new
     * LinkedBlockingQueue<Runnable>()); executor.allowCoreThreadTimeOut(true);
     * return executor; }
     */
    private static class MyExecutor {
        private static ExecutorService mExecutor;
        private static HashMap<String, ExecutorService> executorMap = new HashMap<String, ExecutorService>();

        public static ExecutorService getExecutorInstance(String pkg) {
            if (executorMap.containsKey(pkg)) {
                return executorMap.get(pkg);
            }

            new MyExecutor();
            if (mExecutor != null)
                executorMap.put(pkg, mExecutor);
            return mExecutor;
        }

        private MyExecutor() {
            // Create a bounded thread pool for executing downloads; it creates
            // threads as needed (up to maximum) and reclaims them when
            // finished.
            final ThreadPoolExecutor executor = new ThreadPoolExecutor(
                    MAX_DOWNLOAD_CONCURRENT, MAX_DOWNLOAD_CONCURRENT, 10, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>());
            executor.allowCoreThreadTimeOut(true);
            mExecutor = executor;
        }
    }

    /**
     * 这里是通过控制核心线程的数量来保证同时只有n个线程在运行，通过维护正在执行的mWorks队列来控制不让线程排队
     */
    public static class PkgThreadPoolExecutor extends ThreadPoolExecutor {
        private static ExecutorService mExecutor;
        //正在运行的任务
        private static ConcurrentHashMap<Future<?>, Runnable> mWorks = new ConcurrentHashMap<Future<?>, Runnable>();
        private static int SINGLE_APP_RUN_COUNT = 2;//每个app运行的线程数
        private static int MAX_RUN_COUNT = 6;//每个app运行的线程数
        private static int mMaxConcurrentCount;//同时运行线程的并发数
        public static ExecutorService getExecutorInstance() {
            if (mExecutor == null) {
                mExecutor = buildDownloadExecutor(MAX_RUN_COUNT);
            }
            return mExecutor;
        }

        public static ExecutorService buildDownloadExecutor(int concurrentCount) {
            final PkgThreadPoolExecutor executor = new PkgThreadPoolExecutor(concurrentCount);
            executor.allowCoreThreadTimeOut(true);
            mExecutor = executor;
            return mExecutor;
        }

        private PkgThreadPoolExecutor(int maxConcurrentCount) throws IllegalArgumentException {
            super(maxConcurrentCount, maxConcurrentCount, 10, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>());
            if (maxConcurrentCount < 1 || maxConcurrentCount > MAX_RUN_COUNT) {
                throw new IllegalArgumentException("maxConcurrentCount must be  [1,6]");
            }
            mMaxConcurrentCount = maxConcurrentCount;
        }

        public boolean isRunning(DownloadInfo info) {
            boolean run = false;
            for (Runnable r : mWorks.values()) {
                DownloadThread dt = (DownloadThread) r;
                long taskId = dt.getInfo().mId;
                if (taskId == info.mId) {
                    run = true;
                    break;
                }

            }
            return run;
        }

        /**
         *
         * @param info
         * @return
         * @throws IllegalArgumentException
         */
        public boolean checkCanAdd(DownloadInfo info) throws IllegalArgumentException {

            if (info == null) {
                throw new IllegalArgumentException("DownloadInfo is null");
            }
            int runCount = getRunCount(info.mPackage);
            if (runCount >= SINGLE_APP_RUN_COUNT) {
                return false;
            }

            if (mWorks.size() >= mMaxConcurrentCount) {
                return false;
            }

            BlockingQueue<Runnable> queue = this.getQueue();
            if (queue.size() > 0) {
                Log.w("PkgThreadPoolExecutor", String.format("queue contains %s members ", queue.size()));
            }
            return true;
        }

        private int getRunCount(String pkg) {
            int countRun = 0;
            for (Runnable r : mWorks.values()) {
                DownloadThread dt = (DownloadThread) r;
                String dPkg = dt.getInfo().mPackage;
                if (TextUtils.equals(dPkg, pkg)) {
                    countRun++;
                }

            }
            return countRun;
        }

        private void addQueue(Future<?> future, Runnable task) {
            DownloadInfo info = getDownloadInfo((DownloadThread) task);
            // checkCanAdd(info);
            mWorks.put(future, task);
            Log.i("PkgThreadPoolExecutor", String.format("addQueue %s pkg=%s runCount=%d mWorks=%s",
                    info.getDownloadInfo(), info.mPackage, mWorks.size(), mWorks.toString()));
        }

        private DownloadInfo getDownloadInfo(DownloadThread down) {
            return down.getInfo();
        }

        private void removeQueue(Runnable r) {
            mWorks.remove(r);
            Log.i("PkgThreadPoolExecutor", String.format("removeQueue runCount=%d mWorks=%s rm->%s",
                    mWorks.size(), mWorks.toString(), r.toString()));
        }

        @Override
        public Future<?> submit(Runnable task) {
            Future<?> future = super.submit(task);
            addQueue(future, task);
            return future;
        }

        @Override
        protected void beforeExecute(Thread t, Runnable r) {
            super.beforeExecute(t, r);
        }

        @Override
        protected void afterExecute(Runnable r, Throwable t) {
            super.afterExecute(r, t);
            removeQueue(r);
        }
    }

    private DownloadScanner mScanner;

    private HandlerThread mUpdateThread;
    private Handler mUpdateHandler;

    private volatile int mLastStartId;

    /**
     * Receives notifications when the data in the content provider changes
     */
    private class DownloadManagerContentObserver extends ContentObserver {
        public DownloadManagerContentObserver() {
            super(new Handler());
        }

        @Override
        public void onChange(final boolean selfChange) {
            enqueueUpdate();
        }
    }

    private class FirewallObserver extends ContentObserver {
        public FirewallObserver() {
            super(new Handler());
        }

        @Override
        public void onChange(final boolean selfChange) {
            Log.i(TAG,"FirewallObserver "+selfChange);
            enqueueUpdate();
        }
    }

    /**
     * Returns an IBinder instance when someone wants to connect to this
     * service. Binding to this service is not allowed.
     *
     * @throws UnsupportedOperationException
     */
    @Override
    public IBinder onBind(Intent intent) {
        XLConfig.LOGD(TAG, String.format("onBind %s Intent=%s", ProcessUtil.getProcessInfo(this), intent));
        return null;
    }

    @Override
    public void onRebind(Intent intent) {
        XLConfig.LOGD(TAG, String.format("onRebind %s Intent=%s", ProcessUtil.getProcessInfo(this), intent));
        super.onRebind(intent);
    }

    @Override
    public boolean onUnbind(Intent intent) {
        XLConfig.LOGD(TAG, String.format("onUnbind %s Intent=%s", ProcessUtil.getProcessInfo(this), intent));
        return super.onUnbind(intent);
    }

    Context mContext = null;
    /**
     * Initializes the service when it is first created
     */
    @Override
    public void onCreate() {
        super.onCreate();
        mContext = this;
        if (mSystemFacade == null) {
            mSystemFacade = new RealSystemFacade(this);
        }

        mAlarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        mStorageManager = new StorageManager(this);

        mUpdateThread = new HandlerThread(Constants.TAG + "-UpdateThread");
        mUpdateThread.start();
        mUpdateHandler = new Handler(mUpdateThread.getLooper(), mUpdateCallback);

        mScanner = new DownloadScanner(this);

        mNotifier = new DownloadNotifier(this);
        mNotifier.cancelAll();

        mObserver = new DownloadManagerContentObserver();
        getContentResolver().registerContentObserver(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI,
                true, mObserver);

        mFirewallObserver = new FirewallObserver();
        NetworkUtils.registerFirewallContentObserver(this, mFirewallObserver);
        startGetXlTokenEx();

        DesktopProgressAppInfo.init(getApplicationContext());
        loadXLConfig();
        Statistics.trackServiceStart(mContext);
        registerDeviceProvisionedObserver(mContext);
    }

    private void loadXLConfig() {
        mXlDownloadManager = getXlDownloadManager();
        mXLVipChannelManager = getXLVipChannelManager();
    }

    public static final int CMD_DEF = 0;
    public static final int CMD_GET_XLTOKEN = 1;
    public static final int CMD_NET_CHANGE = 2;
    public static final int CMD_ENGINE_INIT = 3;
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {

        int cmd = intent != null ? intent.getIntExtra("CMD_TYPE", -1) : -1;
        LogUtil.d(TAG, "onStartCommand --->startId=" + startId + " cmd=" + cmd);
        switch (cmd) {
            case CMD_GET_XLTOKEN:
                startGetXlTokenEx();
                break;
            case CMD_DEF:
            case CMD_NET_CHANGE:
                enqueueUpdate();
                break;
            case CMD_ENGINE_INIT:
                loadXLConfig();
                enqueueUpdate();
                break;
            default:
                LogUtil.forceD(TAG, "on handle --->startId=" + startId + " cmd=" + cmd);
                break;
        }

        int returnValue = super.onStartCommand(intent, flags, startId);
        mLastStartId = startId;
        return returnValue;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        LogUtil.forceD(TAG, "onDestroy");
        getContentResolver().unregisterContentObserver(mObserver);
        if (mFirewallObserver != null) {
            NetworkUtils.unRegisterFirewallContentObserver(this, mFirewallObserver);
        }
        if (mScanner != null) {
            mScanner.shutdown();
        }

        if (mUpdateThread != null) {
            mUpdateThread.quit();
        }
        mStorageManager.unInit();
        TraceReport.reportServiceEnd(this);
        unRegisterDeviceProvisionedObserver(mContext);
    }

    /**
     * Enqueue an {@link #updateLocked()} pass to occur in future.
     */
    private void enqueueUpdate() {
        synchronized (this) {
            if (mUpdateThread == null) {
                mUpdateThread = new HandlerThread(Constants.TAG + "-UpdateThread");
                mUpdateThread.start();
                mUpdateHandler = new Handler(mUpdateThread.getLooper(), mUpdateCallback);
            }
            mUpdateHandler.removeMessages(MSG_UPDATE);
            mUpdateHandler.obtainMessage(MSG_UPDATE, mLastStartId, -1).sendToTarget();
        }
    }

    /**
     * Enqueue an {@link #updateLocked()} pass to occur after delay, usually to
     * catch any finished operations that didn't trigger an update pass.
     */
    private void enqueueFinalUpdate() {
        mUpdateHandler.removeMessages(MSG_FINAL_UPDATE);
        if (mUpdateThread != null && mUpdateThread.isAlive()) {
            mUpdateHandler.sendMessageDelayed(
                    mUpdateHandler.obtainMessage(MSG_FINAL_UPDATE, mLastStartId, -1),
                    5 * MINUTE_IN_MILLIS);
        }
    }

    private boolean needWaitXLEngine() {
        if (BuildUtils.isTablet() || BuildUtils.isInternationalVersion()) {
            return false;
        }
        return mXlDownloadManager == null;
    }

    private Handler.Callback mUpdateCallback = new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            Process.setThreadPriority(Process.THREAD_PRIORITY_BACKGROUND);
            if (needWaitXLEngine()) {
                LogUtil.forceD(TAG, "needWaitXLEngine");
                return true;
            }
            final int startId = msg.arg1;

            // XLConfig.LOGD("in handlerMessage, Updating for startId " +
            // startId + ", what=" + msg.what);

            // Since database is current source of truth, our "active" status
            // depends on database state. We always get one final update pass
            // once the real actions have finished and persisted their state.

            // TODO: switch to asking real tasks to derive active state
            // TODO: handle media scanner timeouts

            final boolean isActive;
            synchronized (mDownloads) {
                isActive = updateLocked();
            }

            if (msg.what == MSG_FINAL_UPDATE) {
                // Dump thread stacks belonging to pool
                for (Map.Entry<Thread, StackTraceElement[]> entry :
                Thread.getAllStackTraces().entrySet()) {
                    if (entry.getKey().getName().startsWith("pool")) {
                        XLConfig.LOGD_INFO(entry.getKey() + ": " + Arrays.toString(entry.getValue()));
                    }
                }

                // Dump speed and update details
                mNotifier.dumpSpeeds();

                XLConfig.LOGD_INFO(TAG, "Final update pass triggered, isActive=" + isActive
                        + "; someone didn't update correctly.");
            }

            if (isActive) {
                // Still doing useful work, keep service alive. These active
                // tasks will trigger another update pass when they're finished.

                // Enqueue delayed update pass to catch finished operations that
                // didn't trigger an update pass; these are bugs.
                enqueueFinalUpdate();
            } else {
                // No active tasks, and any pending update messages can be
                // ignored, since any updates important enough to initiate tasks
                // will always be delivered with a new startId.

                if ((msg.what == MSG_FINAL_UPDATE)) {
                    stopSelf();
                    XLConfig.LOGD_INFO(TAG, "Nothing left; stopped " + " startId="
                            + startId);
                    getContentResolver().unregisterContentObserver(mObserver);
                    mScanner.shutdown();
                    if (mUpdateThread != null) {
                        mUpdateThread.quit();
                    }
                } else {
                    enqueueFinalUpdate();
                }
            }

            return true;
        }
    };

    /**
     * Update {@link #mDownloads} to match {@link DownloadProvider} state.
     * Depending on current download state it may enqueue {@link DownloadThread}
     * instances, request {@link DownloadScanner} scans, update user-visible
     * notifications, and/or schedule future actions with {@link AlarmManager}.
     * <p>
     * Should only be called from {@link #mUpdateThread} as after being
     * requested through {@link #enqueueUpdate()}.
     *
     * @return If there are active tasks being processed, as of the database
     *         snapshot taken in this update.
     */
    private boolean updateLocked() {
        LogUtil.forceD(TAG, "updateLocked enter ");
        final long now = mSystemFacade.currentTimeMillis();
        boolean isActive = false;
        long nextActionMillis = Long.MAX_VALUE;

        final Set<Long> staleIds = Sets.newHashSet(mDownloads.keySet());
        final ContentResolver resolver = getContentResolver();
        Cursor cursor = null;
        try {
            DesktopProgressAppInfo.deleteApkFromDownloadlistSet.clear();
            cursor = resolver.query(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI,
                    null, null, null, null);
            final DownloadInfo.Reader reader = new DownloadInfo.Reader(resolver, cursor);
            final int idColumn = cursor.getColumnIndexOrThrow(Downloads.Impl._ID);
            while (cursor.moveToNext()) {
                final long id = cursor.getLong(idColumn);
                long currentDownloadNextActionMillis = Long.MAX_VALUE;

                DownloadInfo info = mDownloads.get(id);
                if (info != null) {
                    updateDownload(resolver, reader, info, now);
                } else {
                    // Check xunlei engine status when create a new task
                    info = insertDownloadLocked(reader, now);
                }

                if (info.mDeleted) {
                    // if download has been completed, delete xxx, else delete
                    // xxx.midownload
                    if (info.mStatus == Downloads.Impl.STATUS_SUCCESS) {
                        if (info.mFileName != null) {
                            FileUtil.deleteFileIfExists(info.mFileName);
                            M3U8Parse.deleteFileIfExists(info.mFileName, info.mUri);
                        }
                    } else {
                        if (info.mFileName != null) {
                            FileUtil.deleteFileIfExists(info.mFileName
                                    + Helpers.sDownloadingExtension);
                            FileUtil.deleteFileIfExists(info.mFileName
                                    + Helpers.sDownloadingExtension
                                    + Helpers.sDownload2GCfgFileExtension);
                            FileUtil.deleteFileIfExists(info.mFileName);
                            M3U8Parse.deleteFileIfExists(info.mFileName, info.mUri);
                        }
                    }
                    resolver.delete(info.getAllDownloadsUri(), null, null);
                    // Delete download if requested, but only after cleaning up
                    if (!TextUtils.isEmpty(info.mMediaProviderUri)) {
                        try {
                            DownloadServiceInjector.deleteMediaStore(getContentResolver(),info);
                        } catch (Exception e){
                            Log.i(TAG,"delete mMediaProviderUri fail:"+e.toString());
                        }

                    }
                } else {
                    staleIds.remove(id);
                    // Kick off download task if ready
                    String pkg = TextUtils.isEmpty(info.mPackage) ? sUnknownPackage : info.mPackage;
                    long start = System.currentTimeMillis();
                    boolean activeDownload = info.startDownloadIfReadyNew(PkgThreadPoolExecutor.getExecutorInstance());
                    long end = System.currentTimeMillis();
                    Statistics.printCallTime("startDownloadIfReady", start, end, info.mId);
                    // Kick off media scan if completed
                    final boolean activeScan = info.startScanIfReady(mScanner);

                    // get current download task's next action millis
                    currentDownloadNextActionMillis = info.nextActionMillis(now);

                    LogUtil.forceD(TAG, "Download " + info.mId + ": activeDownload="
                            + activeDownload + ", activeScan=" + activeScan + " nextActionMillis="
                            + currentDownloadNextActionMillis);

                    isActive |= activeDownload;
                    isActive |= activeScan;
                    // if equals 0, keep download service on.
                    isActive |= (currentDownloadNextActionMillis == 0);
                }

                // Keep track of nearest next action
                nextActionMillis = Math.min(currentDownloadNextActionMillis, nextActionMillis);
            }
        } catch (SQLiteDiskIOException e) {
            LogUtil.forceD(TAG, "updateLocked error when updateLocked: ", e);
        } catch (Exception e) {
            LogUtil.forceD(TAG, "updateLocked error when updateLocked: ", e);
        } finally {
            LogUtil.forceD(TAG, "cusor close before");
            if (cursor != null) {
                cursor.close();
            }
            LogUtil.forceD(TAG, "cusor close after");
        }

        // Clean up stale downloads that disappeared
        for (Long id : staleIds) {
            deleteDownloadLocked(id);
        }

        // Update notifications visible to user
        mNotifier.updateWith(mDownloads.values());
        if (NotifyRecommendManager.isNotifyRecommendSwitchOpen()) {
            NotifyRecommendManager.getInstance().doRecomendTask(mDownloads.values());
        }
        // Set alarm when next action is in future. It's okay if the service
        // continues to run in meantime, since it will kick off an update pass.
        if (nextActionMillis > 0 && nextActionMillis < Long.MAX_VALUE) {
            LogUtil.forceD(TAG, "updateLocked scheduling start in " + nextActionMillis
                    + "ms");

            final Intent intent = DownloadNotifier.getIntent(Constants.ACTION_RETRY);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                mAlarmManager.setExact(AlarmManager.RTC_WAKEUP, now + nextActionMillis,
                        PendingIntent.getBroadcast(this, 0, intent,
                                DownloadNotifierInjector.fixPendingIntentFlags(PendingIntent.FLAG_ONE_SHOT)));
            } else {
                mAlarmManager.set(AlarmManager.RTC_WAKEUP, now + nextActionMillis,
                        PendingIntent.getBroadcast(this, 0, intent,
                                DownloadNotifierInjector.fixPendingIntentFlags(PendingIntent.FLAG_ONE_SHOT)));
            }
        }
        LogUtil.forceD(TAG, String.format("updateLocked exit totalTime=%d isActive=%b nextActionMillis=%d",
                mSystemFacade.currentTimeMillis() - now, isActive, nextActionMillis));
        return isActive;
    }

    /**
     * Keeps a local copy of the info about a download, and initiates the
     * download if appropriate.
     */
    private DownloadInfo insertDownloadLocked(DownloadInfo.Reader reader, long now) {
        final DownloadInfo info = reader.newDownloadInfo(
                this, mSystemFacade, mStorageManager, mNotifier,
                mXlDownloadManager, mXLVipChannelManager);

        mDownloads.put(info.mId, info);

        // XLConfig.LOGD(TAG,"processing inserted download " +
        // info.mId);

        return info;
    }

    /**
     * Updates the local copy of the info about a download.
     *
     * @param resolver
     */
    private void updateDownload(ContentResolver resolver, DownloadInfo.Reader reader,
            DownloadInfo info, long now) {
        reader.updateFromDatabase(resolver, info);
        // XLConfig.LOGD(TAG,"processing updated download " +
        // info.mId + ", status: "
        // + info.mStatus);
    }

    /**
     * Removes the local copy of the info about a download.
     */
    private void deleteDownloadLocked(long id) {
        DownloadInfo info = mDownloads.get(id);
        if (info.mStatus == Downloads.Impl.STATUS_RUNNING) {
            info.mStatus = Downloads.Impl.STATUS_CANCELED;
        }
        if (info.mStatus != Downloads.Impl.STATUS_SUCCESS && info.mFileName != null) {
            XLConfig.LOGD(Constants.TAG, "deleteDownloadLocked() deleting " + info.mFileName);
            FileUtil.deleteFileIfExists(info.mFileName + Helpers.sDownloadingExtension);
        }
        mDownloads.remove(info.mId);
    }

    @Override
    protected void dump(FileDescriptor fd, PrintWriter writer, String[] args) {
        XLConfig.LOGD(TAG, Arrays.toString(args));

        if (args == null || args.length <= 0) {
            dumpVersion(writer);
            dumpCloudconfig(writer);
            dumpNotify(writer);
            dumpDownloadCfg(writer);
            LimitSpeedUtil.dump(writer);
            NetworkUtils.dumpNetRule(writer);
        } else {
            String cmd = args[0];
            if (TextUtils.equals("dumpThread", cmd)) {
                dumpThread();
            } else if (TextUtils.equals("setSpeedLimit", cmd)) {
                //setLimitSpeed(args[1],args[2]);
            } else if (TextUtils.equals("cloudConfig", cmd)) {
                //CloudConfigPreference.getInstance().config(args[1],args[2]);
            }
        }
    }

    private void dumpDownloadCfg(PrintWriter writer) {
        Context context = getApplicationContext();
        Long maxByteLimit = MobileDataConfig.getLimit(context);
        StringBuilder downCfg = new StringBuilder();
        downCfg.append("dumpDownloadCfg:\n");
        downCfg.append(String.format("maxByteLimit=%s\n", String.valueOf(maxByteLimit)));
        downCfg.append(String.format("isXunleiUsageOpen=%s\n", String.valueOf(PrivacySettingHelper.isXunleiUsageOpen())));
        writer.write(downCfg.toString());
    }

    private void dumpCloudconfig(PrintWriter writer) {
        if (DownloadApplication.getGlobalApplication() == null) {
            LogUtil.logD("dumpCloudconfig Global Context is null");
            return;
        }
        String cloudCfg = CloudConfigPreference.getInstance().toString();
        writer.write("dumpCloudconfig:\n" + cloudCfg + "\n");
    }

    private void setLimitSpeed(String limitSpeedStr, String limitTimeStr) {
        if (TextUtils.isEmpty(limitSpeedStr) || TextUtils.isEmpty(limitTimeStr)) {
            return;
        }
        try {
            long limitSpeed = parseLong(limitSpeedStr);
            long limitTime = parseLong(limitTimeStr);
            LimitSpeedUtil.getInstance().setLimitSpeed((DownloadApplication) getApplication(), limitSpeed, limitTime);
        } catch (Exception e) {
            XLConfig.LOGD_INFO(LimitSpeedUtil.TAG, "exc", e);
        }
    }

    private void dumpVersion(PrintWriter writer) {
        String appVersion = String.valueOf(XLUtil.getAppVersion(getApplicationContext()));
        String miuiVersion = String.format("%s/%s/%s/%s/%s"
                , BuildUtils.getDeviceName()
                , BuildUtils.getBigMiuiVersion()
                , BuildUtils.getMiuiVersion()
                , BuildUtils.getAndroidVersion()
                , BuildUtils.getRegion());
        StringBuffer tmpStr = new StringBuffer();
        tmpStr.append("dumpVersion:\n");
        tmpStr.append(String.format("miuiVersion=%s\n", miuiVersion));
        tmpStr.append(String.format("appVersion=%s\n", appVersion));
        tmpStr.append(String.format("device=%s\n", getDeviceID()));
        tmpStr.append(String.format("libVersion=%s\n", BuildUtils.getSoVersion()));
        tmpStr.append(String.format("isForceRep=%b\n", LogUtil.isForceReport()));
        ScdnHelper.dump(tmpStr);
        writer.write(tmpStr.toString());
    }

    private String getDeviceID() {
        String str = TraceReport.getDeviceIDFromTrack();
        String encodeStr = AESUtils.aesEncode(str, NewDownloadRequestBase.APP_SECRET_KEY);
        return encodeStr;
    }

    private long parseLong(String longStr) {
        long _long = Long.parseLong(longStr);
        return _long;
    }

    private void dumpNotify(PrintWriter writer) {
        final IndentingPrintWriter pw = new IndentingPrintWriter(writer, "  ");
        synchronized (mNotifier) {
            mNotifier.dump(pw, mDownloads.values());
        }
    }

    private void dumpThread() {
        for (Map.Entry<Thread, StackTraceElement[]> entry :
                Thread.getAllStackTraces().entrySet()) {
            Thread thread = entry.getKey();
            String threadInfo = String.format("thread name=%s id=%d other=%s", thread.getName(), thread.getId(), thread);
            String stackMsg = "threadInfo " + threadInfo + "\n";
            try {
                StackTraceElement[] stack = entry.getValue();
                for (int i = 0; i < stack.length; i++) {
                    stackMsg += stack[i] + "\n";
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            XLConfig.LOGD_INFO(stackMsg);
        }
    }

    private TokenHelper mTokenHelper;

    public void startGetXlTokenEx() {
        String xlToken = DownloadSettings.XLShareConfigSettings.getXLToken("");
        LogUtil.d(TAG, "(startGetXlTokenEx) ---> create get token subThread. token: " + xlToken);
        if (!TextUtils.isEmpty(xlToken)) {
            return;
        }
        if (mTokenHelper == null) {
            mTokenHelper = TokenHelper.getInstance();
        }
        mTokenHelper.requestToken();
    }

    private XLDownloadManager getXlDownloadManager() {
        DownloadApplication application=(DownloadApplication)getApplication();
        return application.getXlDownloadManager();
    }

    private XLVipChannelManager getXLVipChannelManager() {
        DownloadApplication application=(DownloadApplication)getApplication();
        return application.getXLVipChannelManager();
    }

    ProvisionHelper mProvisionHelper = new ProvisionHelper();

    public void registerDeviceProvisionedObserver(Context context) {
        mProvisionHelper.registerDeviceProvisionedObserver(context, mDeviceProvisionedObserver);
    }

    public void unRegisterDeviceProvisionedObserver(Context context) {
        context.getContentResolver().unregisterContentObserver(mDeviceProvisionedObserver);
    }

    private ContentObserver mDeviceProvisionedObserver = new ContentObserver(new Handler()) {
        @Override
        public void onChange(boolean selfChange) {
            boolean provisioned = ProvisionHelper.isDeviceProvisioned(mContext);
            TraceReport.setNetworkAccessEnabled(provisioned);
            LogUtil.forceD(ProvisionHelper.TAG, "provisioned=" + provisioned);
        }
    };
}
