
package com.android.providers.downloads;

import android.app.Application;
import android.content.Context;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Handler;
import android.security.NetworkSecurityPolicy;
import android.util.Log;

import com.android.providers.downloads.alarm.LimitSpeed;
import com.android.providers.downloads.alarm.LimitSpeedAlarm;
import com.android.providers.downloads.config.ActionConstants;
import com.android.providers.downloads.config.Constants;
import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.RecoveryConfigHelpers;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.exception.CrashHandler;
import com.android.providers.downloads.fileoptimization.OptApplication;
import com.android.providers.downloads.helper.CloudControlHelper;
import com.android.providers.downloads.kcg.utils.KcgHelper;
import com.android.providers.downloads.receiver.DownloadMiAccountReceiver;
import com.android.providers.downloads.receiver.DownloadNotificationReceiver;
import com.android.providers.downloads.receiver.DownloadReceiver;
import com.android.providers.downloads.receiver.DynamicReceiver;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.statistics.HubStatistics;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.statistics.TraceReport;
import com.android.providers.downloads.ui.ui.DirectMailStatusReceiver;
import com.android.providers.downloads.util.DataUsageHelper;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.LimitSpeedUtil;
import com.android.providers.downloads.util.NetworkUtils;
import com.android.providers.downloads.util.TokenHelper;
import com.android.providers.downloads.utils.LogUtil;
import com.xunlei.downloadlib.XLDownloadManager;
import com.xunlei.vipchannel.XLVipChannelManager;

import miui.accounts.ExtraAccountManager;
import miuix.autodensity.AutoDensityConfig;
import miuix.autodensity.IDensity;

public class DownloadApplication extends Application implements ActionConstants, IDensity {

    public static final String APP_ID = XLDownloadCfg.APP_ID;
    public static final String APP_KEY = XLDownloadCfg.APP_KEY;
    private static Context mApplicationContext;
    private static Context mAttachContext;
    private static Context mProviderContext;
    private DownloadNotificationReceiver mNotificationReceiver;
    private DynamicReceiver mDynamicReceiver = null;
    private static XLDownloadApplication mXLApplication;
    private DirectMailStatusReceiver mDirectMainStatusRecerver;
    public static OptApplication miProvider = null;

    @Override
    public void onCreate() {
        super.onCreate();
        miProvider = new OptApplication();
        init();
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        unInit();
    }

    @Override
    protected void attachBaseContext(Context base) {
        mAttachContext = base;
        super.attachBaseContext(base);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            NetworkSecurityPolicy.getInstance().setCleartextTrafficPermitted(true);
        }
    }

    public void init() {
        mApplicationContext = this;
        Log.d(LogUtil.TAG,"app init " + this.getApplicationContext());
        TraceReport.init(DownloadApplication.this);
        getWorkHandler().post(new Runnable() {
            @Override
            public void run() {
                initXLEngine();
                DataUsageHelper.getInstance().loadDataLimit();
                DataUsageHelper.getInstance().registerBillContentObserver(DownloadApplication.this);
                CloudConfigPreference.getInstance().loadLocal();
                if (DownloadSettings.XLSecureConfigSettings.getUsedFirstDownload()) {
                    CloudControlHelper.getInstance().getCloudConfig();
                }
            }
        });

        mNotificationReceiver = new DownloadNotificationReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction(Constants.ACTION_RETRY);
        filter.addDataScheme("active-dl");
        registerReceiver(mNotificationReceiver, filter,RECEIVER_EXPORTED);
        Log.d(Constants.TAG, "Received broadcast intent for filter=" + filter);

        IntentFilter dynamicFilter = new IntentFilter();
        dynamicFilter.addAction(ACTION_MIUI_HOME_CLICK);
        dynamicFilter.addAction(ACTION_MIUI_HOME_CANCEL);
        dynamicFilter.addAction(ACTION_INSTALL_NOTIFY_CLICK);
        dynamicFilter.addAction(ACTION_MARKET_APK_COMPLETED);
        dynamicFilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        mDynamicReceiver = new DynamicReceiver();
        registerReceiver(mDynamicReceiver, dynamicFilter,RECEIVER_EXPORTED);
        mDirectMainStatusRecerver = new DirectMailStatusReceiver();
        mDirectMainStatusRecerver.register(this);

        getMainThreadHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                AutoDensityConfig.init(DownloadApplication.this);
                HubStatistics.init();

                RecoveryConfigHelpers.recoveryConfigFormOldVersion(DownloadApplication.this);
                initToken();
                registerAccountReceiver();
                XLConfig.initLog(DownloadApplication.this);

                if (Helpers.isCmTestBuilder()) {
                    DownloadReceiver.networkType = Helpers.getSpecificNetworkType(DownloadApplication.this);
                }

                LimitSpeedUtil.getInstance().initLimitSpeed(DownloadApplication.this);
                Handler workHandler = getWorkHandler();
                NetworkUtils.registerFirewallObserver(DownloadApplication.this, workHandler);
                LimitSpeedAlarm.registerAlarmReceiver(DownloadApplication.this);
                LimitSpeed.getInstance().init(workHandler);
                Statistics.trackProcessStart(DownloadApplication.this);
                HubStatistics.reloadCommonParams();
            }
        },500);
    }

    public DirectMailStatusReceiver getDirectMainStatusRecerver() {
        return mDirectMainStatusRecerver;
    }

    private void initXLEngine() {
        mXLApplication = new XLDownloadApplication(mApplicationContext);
        mXLApplication.initXunleiEngine();
    }

    public static Handler getWorkHandler() {
        return Helpers.getAsyncHandler();
    }

    public void unInit() {
        unInitToken();
        if (mNotificationReceiver != null) {
            unregisterReceiver(mNotificationReceiver);
        }
        if (mDynamicReceiver != null) {
            unregisterReceiver(mDynamicReceiver);
        }

        XLConfig.saveLogMark(mApplicationContext);
        mXLApplication.uninitXunleiEngine();
        NetworkUtils.unRegisterFirewallObserver(mApplicationContext);
        unRegisterAccountReceiver();
        LimitSpeedAlarm.unRegisterAlarmReceiver(mApplicationContext);
        DataUsageHelper.getInstance().unRegisterBillContentObserver(mApplicationContext);

        unInitKCGEngine();
        mDirectMainStatusRecerver.unResiter(this);
        miProvider.unInit();
    }

    private void initToken() {
        TokenHelper.getInstance().initWithContext(mApplicationContext);
    }

    private void unInitToken() {
        TokenHelper.getInstance().uninit();
    }

    public static Context getGlobalApplication() {
        Context context = null;
        if (mApplicationContext != null) {
            context = mApplicationContext.getApplicationContext();
        }
        if (context == null) {
            context = mApplicationContext;
        }
        if (context == null) {
            context = mAttachContext;
        }
        if (context == null) {
            context = mProviderContext;
        }
        return context;
    }

    public static void setProviderContext(Context context) {
        if (context == null || mProviderContext != null) {
            return;
        }
        mProviderContext = context;
    }

    public static XLDownloadManager getXlDownloadManager() {
        return mXLApplication != null ? mXLApplication.getManager() : null;
    }

    public XLVipChannelManager getXLVipChannelManager() {
        return mXLApplication != null ? mXLApplication.getVipManager() : null;
    }

    private DownloadMiAccountReceiver mAccountReceiver;

    private void registerAccountReceiver() {
        mAccountReceiver = new DownloadMiAccountReceiver();
        IntentFilter filter = new IntentFilter(
                ExtraAccountManager.LOGIN_ACCOUNTS_POST_CHANGED_ACTION);
        mApplicationContext.registerReceiver(mAccountReceiver, filter,RECEIVER_EXPORTED);
    }

    private void unRegisterAccountReceiver() {
        if (mAccountReceiver == null) {
            return;
        }
        mApplicationContext.unregisterReceiver(mAccountReceiver);
    }


    private void initKCGEngine() {
        KcgHelper.getInstance().initKCGEngine(getGlobalApplication());
    }

    private void unInitKCGEngine() {
        KcgHelper.getInstance().unInitKCGEngine();
    }

    @Override
    public boolean shouldAdaptAutoDensity() {
        return true;
    }
}
