
package com.android.providers.downloads.util;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.os.*;
import android.text.TextUtils;
import android.util.Log;

import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.provider.DownLoadProviderUtils;
import com.android.providers.downloads.provider.DownloadProvider;
import com.xunlei.downloadlib.parameter.BtSubTaskDetail;
import com.xunlei.downloadlib.parameter.XLTaskInfo;

public class BTDatabaseHelper implements XLDownloadCfg {

    public static final Uri URI_BT_DETAIL = Uri
            .parse("content://downloads/all_downloads_download_bt_detail");

    public static void createBTTable(SQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_BT_DETAIL);
        db.execSQL("CREATE TABLE " + TABLE_BT_DETAIL + "(" +
                TORRENT_ID + " INTEGER PRIMARY KEY AUTOINCREMENT," +
                TORRENT_DOWNLOAD_ID + " INTEGER NOT NULL," +
                TORRENT_CURRENT_SIZE + " INTEGER," +
                TORRENT_FILE_SIZE + " INTEGER," +
                TORRENT_SUB_PATH + " TEXT NOT NULL," +
                TORRENT_FILE_INDEX + " INTEGER," +
                TORRENT_P2S_SPEED + " INTEGER," +
                TORRENT_DOWNLOAD_SPEED + " INTEGER," +
                TORRENT_STATUS + " INTEGER," +
                TORRENT_REASON + " TEXT," +
                TORRENT_DATA + " TEXT," +
                TORRENT_FILE_NAME + " TEXT," +
                TORRENT_FILE_INFOS_HASH + " TEXT NOT NULL" +
                ");");
    }

    public static boolean isHttp(String uri) {
        boolean isHttp = false;
        if (!TextUtils.isEmpty(uri)) {
            int indexOf = uri.indexOf(':');
            String host = indexOf > 0 ? uri.substring(0, indexOf) : "";
            String lowerCase = host.toLowerCase();
            isHttp = TextUtils.equals(lowerCase, "http") || TextUtils.equals(lowerCase, "https");
        }
        return isHttp;
    }

    public static Cursor queryBtDetails(SQLiteDatabase db, Uri uri, String selection, String[]
            selectionArgs, String sort) {
        return db.query(TABLE_BT_DETAIL, null, selection, selectionArgs, null, null, sort);
    }

    public static void insertRequestBT(SQLiteDatabase db, long downloadId, ContentValues values) {
        String fileInfos = values.getAsString(TORRENT_FILE_INFOS);
        if (!TextUtils.isEmpty(fileInfos)) {
            try {
                JSONArray jsonArray = new JSONArray(fileInfos);
                if (jsonArray != null && jsonArray.length() > 0) {
                    int length = jsonArray.length();
                    for (int i = 0; i < length; i++) {
                        ContentValues fileInfo = new ContentValues();
                        JSONObject obj = jsonArray.getJSONObject(i);
                        fileInfo.put(TORRENT_FILE_NAME, obj.getString(TORRENT_FILE_NAME));
                        fileInfo.put(TORRENT_SUB_PATH, obj.getString(TORRENT_SUB_PATH));
                        fileInfo.put(TORRENT_FILE_INDEX, obj.getString(TORRENT_FILE_INDEX));
                        fileInfo.put(TORRENT_FILE_SIZE, obj.getString(TORRENT_FILE_SIZE));
                        fileInfo.put(TORRENT_DOWNLOAD_ID, downloadId);
                        fileInfo.put(TORRENT_FILE_INFOS_HASH,
                                values.getAsString(TORRENT_FILE_INFOS_HASH));
                        long insert = db.insert(TABLE_BT_DETAIL, null, fileInfo);
                        XLConfig.LOGD("insert into downloads database insert=" + insert);
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    public static int updateBtDetail(SQLiteDatabase db, final ContentValues values,
            final String where, final String[] whereArgs) {
        return db.update(TABLE_BT_DETAIL, values, where, whereArgs);
    }

    public static int updateBtById(Context context, final ContentValues values,
            long btId) {
        ContentResolver resolver = context.getContentResolver();
        SqlSelection sqlSel = new SqlSelection();
        sqlSel.appendClause(String.format("%s = ?", COLUMN_ID), btId);
        return resolver.update(CONTENT_URI, values, sqlSel.getSelection(),
                sqlSel.getParameters());
    }

    public static int updateBtDetail(ContentResolver resolver, final ContentValues values,
            final String where, final String[] whereArgs) {
        StringBuilder result = null;
        if (whereArgs != null) {
            result = new StringBuilder();
            for (int i = 0; i < whereArgs.length; i++) {
                result.append(whereArgs[i] + ",");
            }
        }
        logD("updateBtDetail " + values + " where=" + where
                + " args=" + (result != null ? result.toString() : ""));
        return resolver.update(URI_BT_DETAIL, values, where, whereArgs);
    }

    /**
     * 判断是非本app的暂停和恢复下载操作
     *
     * @param values
     * @return
     */
    public static boolean needCheckBtTask(ContentValues values) {
        return (Binder.getCallingPid() != android.os.Process.myPid()) && (DownLoadProviderUtils.isPauseOper(values)
                || DownLoadProviderUtils.isResumeOper(values) || DownLoadProviderUtils.isReStartOper(values));
    }

    /**
     * 将主任务id为STATUS_PENDING，和Running的子任务变为暂停状态
     * @param db
     * @param btIds
     */
    public static void pauseBtSubTask(SQLiteDatabase db,long... btIds) {
        ContentValues values = new ContentValues();
        values.put(TORRENT_STATUS, STATUS_PAUSED_BY_APP);
        String whereClause = "( " + getWhereClauseForIds(btIds) + " AND " +
                getWhereClauseForStatuses(new String[]{"=", "="}, new String[]{"OR"}) + ")";
        String[] whereArgs = concatArrays(DownLoadProviderUtils.getWhereArgsForIds(btIds),
                getWhereArgsForStatuses(new int[]{STATUS_PENDING,
                        STATUS_RUNNING}), String.class);
        int count = db.update(TABLE_BT_DETAIL, values, whereClause, whereArgs);
        XLConfig.LOGD(DownloadProvider.TAG, String.format("pauseBtSubTask count=%d values=%s where=%s " +
                "args=%s", count, values.toString(), whereClause, Arrays.toString(whereArgs)));
    }

    /**
     * 将主任务id为STATUS_PAUSED_BY_APP，STATUS_WAITING_FOR_NETWORK，STATUS_QUEUED_FOR_WIFI的子任务变为暂停状态
     *
     * @param btIds the IDs of the downloads
     * @hide
     */
    public static void resumeBtSubTask(SQLiteDatabase db,long... btIds) {
        ContentValues values = new ContentValues();
        values.put(COLUMN_STATUS, STATUS_PENDING);
        String whereClause = "( " + getWhereClauseForIds(btIds) + " AND " +
                getWhereClauseForStatuses(new String[]{"=", "=", "="}, new String[]{"OR", "OR"}) + ")";
        String[] whereArgs = concatArrays(DownLoadProviderUtils.getWhereArgsForIds(btIds),
                getWhereArgsForStatuses(new int[]{STATUS_PAUSED_BY_APP,
                        STATUS_WAITING_FOR_NETWORK,
                        STATUS_QUEUED_FOR_WIFI}),
                String.class);
        int count = db.update(TABLE_BT_DETAIL, values, whereClause, whereArgs);
        XLConfig.LOGD(DownloadProvider.TAG, String.format("resumeBtSubTask count=%d values=%s where=%s " +
                "args=%s", count, values.toString(), whereClause, Arrays.toString(whereArgs)));
    }


    /**
     * 将对应失败的子任务改为STATUS_PENDING
     *
     * @param btIds the IDs of the downloads
     * @hide
     */
    public static void restartBtSubTask(SQLiteDatabase db,long... btIds) {
        ContentValues values = new ContentValues();
        values.put(COLUMN_STATUS, STATUS_PENDING);
        String whereClause = "( " + getWhereClauseForIds(btIds) + " AND " +
                getWhereClauseForStatuses(new String[]{">=", "<"}, new String[]{"AND"}) + ")";
        String[] whereArgs = concatArrays(DownLoadProviderUtils.getWhereArgsForIds(btIds),
                getWhereArgsForStatuses(new int[]{400, 600}),
                String.class);
        int count = db.update(TABLE_BT_DETAIL, values, whereClause, whereArgs);
        XLConfig.LOGD(DownloadProvider.TAG, String.format("restartBtSubTask count=%d values=%s where=%s " +
                "args=%s", count, values.toString(), whereClause, Arrays.toString(whereArgs)));
    }

    public static HashSet<Integer> getSubTask(ContentResolver resolver, String parentHash) {
        if (parentHash == null) {
            return null;
        }
        HashSet<Integer> indexs = null;
        String selection = TORRENT_FILE_INFOS_HASH + " = ?";
        String[] args = new String[] {
                parentHash + ""
        };
        Cursor cursor = null;
        try {
            cursor = resolver.query(URI_BT_DETAIL, null, selection, args, null);
            if (cursor != null && cursor.getCount() > 0) {
                indexs = new HashSet<Integer>();
                int index = cursor.getColumnIndexOrThrow(TORRENT_FILE_INDEX);
                for (cursor.moveToFirst(); !cursor.isAfterLast(); cursor.moveToNext()) {
                    indexs.add(cursor.getInt(index));
                }
            }
        } finally {
            closeCursor(cursor);
        }
        return indexs;
    }

    public static int[] getBtIndexSetById(Context context, long mId) {
        int[] btIndexSet = null;
        String selection = TORRENT_DOWNLOAD_ID + " = ?";
        String[] args = new String[] {
                mId + ""
        };
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver().query(URI_BT_DETAIL, null, selection, args, null);
            if (cursor != null && cursor.getCount() > 0 && cursor.moveToFirst()) {
                int count = cursor.getCount();
                btIndexSet = new int[count];
                int i = 0;
                int indexOrThrow = cursor.getColumnIndexOrThrow("torrent_file_index");
                for (; !cursor.isAfterLast(); cursor.moveToNext()) {
                    btIndexSet[i] = cursor.getInt(indexOrThrow);
                    i++;
                }
            }
        } finally {
            closeCursor(cursor);
        }
        return btIndexSet;
    }

    private static void closeCursor(Cursor cursor) {
        if (cursor != null) {
            cursor.close();
        }
    }

    public static void updateBtSubTaskDetail(Context context, ContentValues values, long btId,
            int fileIndex) {
        StringBuilder sqlWhere = new StringBuilder();
        sqlWhere.append(String.format("(%s = ?)", TORRENT_DOWNLOAD_ID));
        sqlWhere.append(" AND ");
        sqlWhere.append(String.format("(%s = ?)", TORRENT_FILE_INDEX));
        String[] args = new String[] {
                btId + "", fileIndex + ""
        };
        context.getContentResolver().update(URI_BT_DETAIL, values, sqlWhere.toString(),
                args);
    }

    public static String statusClause(String operator, int value) {
        return TORRENT_STATUS + operator + "'" + value + "'";
    }

    public static int deleteBtDetailById(SQLiteDatabase db, String where, String[] whereArgs) {
        String[] projection = new String[] {
                TORRENT_ID
        };
        int delete = 0;
        Cursor cursor = null;
        try {
            cursor = db.query("downloads", projection, where, whereArgs, null, null, null, null);
            for (cursor.moveToFirst(); !cursor.isAfterLast(); cursor.moveToNext()) {
                long id = cursor.getLong(0);
                String idWhere = TORRENT_DOWNLOAD_ID + "=" + id;
                delete = db.delete(TABLE_BT_DETAIL, idWhere, null);
            }
        } finally {
            closeCursor(cursor);
        }
        return delete;
    }

    public static int deleteBtDetail(SQLiteDatabase db, String where, String[] whereArgs) {
        return db.delete(TABLE_BT_DETAIL, where, whereArgs);
    }

    /**
     * This class encapsulates a SQL where clause and its parameters. It makes
     * it possible for shared methods (like
     * {@link DownloadProvider#getWhereClause(Uri, String, String[], int)}) to
     * return both pieces of information, and provides some utility logic to
     * ease piece-by-piece construction of selections.
     */
    private static class SqlSelection {
        public StringBuilder mWhereClause = new StringBuilder();
        public List<String> mParameters = new ArrayList<String>();

        public <T> void appendClause(String newClause, final T... parameters) {
            if (newClause == null || newClause.isEmpty()) {
                return;
            }
            if (mWhereClause.length() != 0) {
                mWhereClause.append(" AND ");
            }
            mWhereClause.append("(");
            mWhereClause.append(newClause);
            mWhereClause.append(")");
            if (parameters != null) {
                for (Object parameter : parameters) {
                    mParameters.add(parameter.toString());
                }
            }
        }

        public String getSelection() {
            return mWhereClause.toString();
        }

        public String[] getParameters() {
            String[] array = new String[mParameters.size()];
            return mParameters.toArray(array);
        }
    }

    private static String getWhereClauseForIds(long[] ids) {
        if (ids == null || ids.length <= 0) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("(");
        for (int i = 0; i < ids.length; i++) {
            if (i > 0) {
                sb.append("OR ");
            }
            sb.append(String.format("%s = ?", TORRENT_DOWNLOAD_ID));
        }
        sb.append(")");
        return sb.toString();
    }

    private static String getWhereClauseForStatuses(String[] operators, String[] jointConditions) {
        StringBuilder whereClause = new StringBuilder();
        whereClause.append("(");
        for (int i = 0; i < operators.length; i++) {
            if (i > 0) {
                whereClause.append(jointConditions[i - 1] + " ");
            }
            whereClause.append(TORRENT_STATUS);
            whereClause.append(" " + operators[i] + " ? ");
        }
        whereClause.append(")");
        return whereClause.toString();
    }

    private static String[] getWhereArgsForStatuses(int[] statuses) {
        String[] whereArgs = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            whereArgs[i] = Integer.toString(statuses[i]);
        }
        return whereArgs;
    }

    /**
     * concatenate two arrays and return
     */
    private static <T> T[] concatArrays(T[] src1, T[] src2, Class<T> type) {
        T dst[] = (T[]) Array.newInstance(type, src1.length + src2.length);
        System.arraycopy(src1, 0, dst, 0, src1.length);
        System.arraycopy(src2, 0, dst, src1.length, src2.length);
        return dst;
    }

    private static void logD(String msg) {
        XLConfig.LOGD(TAG, msg);
    }

}
