/*
 * Copyright (C) 2008 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.providers.downloads.util;

import static android.os.Environment.buildExternalStorageAndroidObbDirs;
import static android.os.Environment.buildExternalStorageAppDataDirs;
import static android.os.Environment.buildExternalStorageAppMediaDirs;
import static android.os.Environment.buildExternalStorageAppObbDirs;
import static android.os.Environment.buildExternalStoragePublicDirs;

import android.Manifest;
import android.annotation.NonNull;
import android.annotation.Nullable;
import android.app.AppOpsManager;
import android.app.ActivityManager;
import android.app.DownloadManager;
import android.app.KeyguardManager;
import android.content.ComponentName;
import android.content.ContentProvider;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Binder;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Process;
import android.os.SystemClock;
import android.os.UserHandle;
import android.provider.Downloads;
import android.provider.Downloads.Impl;
import android.provider.MediaStore;
import android.provider.MiuiSettings.Secure;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;
import android.util.SparseArray;
import android.webkit.MimeTypeMap;

import com.android.internal.annotations.VisibleForTesting;
import com.android.internal.util.ArrayUtils;
import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.FileUtils;
import com.android.providers.downloads.R;
import com.android.providers.downloads.StorageManager;
import com.android.providers.downloads.StorageUtils;
import com.android.providers.downloads.activity.WarningActivity;
import com.android.providers.downloads.config.Constants;
import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.exception.StopRequestException;
import com.android.providers.downloads.service.DesktopProgressAppInfo;
import com.android.providers.downloads.setting.PrivacySettingHelper;
import com.michael.corelib.coreutils.CustomThreadPool;
import com.xunlei.downloadlib.parameter.XLConstant.XLNetWorkCarrier;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Locale;
import java.util.Random;

import miui.securityspace.XSpaceUserHandle;
import miuix.os.DeviceHelper;
import miuix.os.DeviceType;

/**
 * Some helper functions for the download manager
 */
public class Helpers implements XLDownloadCfg {

    private static Random sRandom = new Random(SystemClock.uptimeMillis());
    public static final boolean analyticsMark = false;

    /**
     * Regex used to parse content-disposition headers
     */
    private static final Pattern CONTENT_DISPOSITION_PATTERN =
            Pattern.compile("attachment;\\s*filename\\s*=\\s*\"([^\"]*)\"");
    private static final Object sUniqueLock = new Object();
    /**
     * When download is not finished, filename has extra extension ".part"
     */
    public static final String sDownloadingExtension = ".midownload";

    public static final String sDownload2GCfgFileExtension = ".cfg";

    private static final Pattern PATTERN_PUBLIC_DIRS =
            Pattern.compile("(?i)^/storage/[^/]+(?:/[0-9]+)?/([^/]+)/.+");

    private static final Pattern PATTERN_ANDROID_PRIVATE_DIRS =
            Pattern.compile("(?i)^/storage/[^/]+(?:/[0-9]+)?/Android/(data|obb)/.+");

    private static final Pattern PATTERN_ANDROID_DIRS =
            Pattern.compile("(?i)^/storage/[^/]+(?:/[0-9]+)?/Android/(?:data|obb|media)/.+");

    /*
     * v6 statistics:divide networktype into classes : 2G/3G/4G/WIFI/no_network
     */
    public static final int MOBILE_2G = 2;
    public static final int MOBILE_3G = 3;
    public static final int MOBILE_4G = 4;
    public static final int WIFI = 9;
    public static final int NO_NETWORK = 0;
    public static final int UNKNOWN_NETWORK = 5;

    private static HandlerThread sAsyncHandlerThread;
    private static Handler sAsyncHandler;

    private Helpers() {}
    public synchronized static Handler getAsyncHandler() {
        if (sAsyncHandlerThread == null) {
            sAsyncHandlerThread = new HandlerThread("sAsyncHandlerThread",
                    Process.THREAD_PRIORITY_BACKGROUND);
            sAsyncHandlerThread.start();
            sAsyncHandler = new Handler(sAsyncHandlerThread.getLooper());
        }
        return sAsyncHandler;
    }

    public static Random getsRandom() {
        return sRandom;
    }

    /*
         * Parse the Content-Disposition HTTP Header. The format of the header is
         * defined here: http://www.w3.org/Protocols/rfc2616/rfc2616-sec19.html This
         * header provides a filename for content that is going to be downloaded to
         * the file system. We only support the attachment type.
         */
    private static String parseContentDisposition(String contentDisposition) {
        try {
            Matcher m = CONTENT_DISPOSITION_PATTERN.matcher(contentDisposition);
            if (m.find()) {
                return m.group(1);
            }
        } catch (IllegalStateException ex) {
            XLConfig.LOGD("error when parseContentDisposition: ", ex);
            // This function is defined as returning null when it can't parse
            // the header
        }
        return null;
    }

    /**
     * Creates a filename (where the file should be saved) from info about a
     * download.
     */
    public static String generateSaveFile(
            Context context,
            String url,
            String hint,
            String contentDisposition,
            String contentLocation,
            String mimeType,
            int destination,
            long contentLength,
            StorageManager storageManager,
            boolean ifCreateFile, boolean fixExtension) throws StopRequestException {
        String path = null;
        File base = null;
        if (destination == DESTINATION_FILE_URI) {
            try {
                if (!TextUtils.isEmpty(hint)) {
                    String newHint = hint.replace("?", "");
                    path = Uri.parse(newHint).getPath();
                }
                if (!TextUtils.isEmpty(path) && path.endsWith("/")) {
                    String fileName = Uri.parse(url).getPath();
                    String newFileName = fileName.substring(fileName.lastIndexOf('/') + 1);
                    path = path + newFileName;
                }
            } catch (Exception e) {
                String msg = !TextUtils.isEmpty(hint) ? hint : "";
                XLConfig.LOGD("get path ex hint=" + msg, e);
            }
        } else {
            base = storageManager.locateDestinationDirectory(mimeType, destination,
                    contentLength < 0 ? 0 : contentLength);
            path = chooseFilename(url, hint, contentDisposition, contentLocation,
                    destination);
        }
        if (DownloadDrmHelper.isDrmConvertNeeded(mimeType)) {
            path = DownloadDrmHelper.modifyDrmFwLockFileExtension(path);
        }
        StorageUtils.fixupAppDir(storageManager, new File(path));
        path = getFullPath(context, path, mimeType, destination, base, ifCreateFile, fixExtension);
        return path;
    }

    static String getFullPath(Context context, String filename, String mimeType, int destination, File base,
                              boolean ifCreateFile, boolean fixExtension)
            throws StopRequestException {
        String newFileName = filename;
        String extension = null;
        int dotIndex = filename.lastIndexOf('.');
        boolean missingExtension = dotIndex < 0 || dotIndex < filename.lastIndexOf('/');
        if (destination == DESTINATION_FILE_URI) {
            // Destination is explicitly set - do not change the extension
            if (missingExtension) {
                extension = "";
            } else {
                extension = filename.substring(dotIndex);
                newFileName = filename.substring(0, dotIndex);
            }
        } else {
            // Split filename between base and extension
            // Add an extension if filename does not have one
            if (missingExtension) {
                extension = chooseExtensionFromMimeType(mimeType, true);
            } else {
                extension = chooseExtensionFromFilename(mimeType, destination, filename, dotIndex, fixExtension);
                newFileName = filename.substring(0, dotIndex);
            }
        }

        boolean recoveryDir = Constants.RECOVERY_DIRECTORY.equalsIgnoreCase(newFileName + extension);

        if (base != null) {
            newFileName = base.getPath() + File.separator + newFileName;
        }

        XLConfig.LOGD("target file: " + newFileName + extension);

        synchronized (sUniqueLock) {
            final String path = chooseUniqueFilenameLocked(
                    destination, newFileName, extension, recoveryDir);
            // Claim this filename inside lock to prevent other threads from
            // clobbering us. We're not paranoid enough to use O_EXCL.
            try {
                File tempFile = new File(path);
                if (!tempFile.exists()) {
                    File parentDir = tempFile.getParentFile();
                    if (!parentDir.exists()) {
                        boolean mkdirs = parentDir.mkdirs();
                        if (!mkdirs) {
                            checkSDcardState(context, parentDir);
                            throw new StopRequestException(STATUS_FILE_ERROR,
                                    "Failed to create target dir " + parentDir);
                        }
                    }
                }
                if (ifCreateFile) {
                    boolean successed = new File(path).createNewFile();
                    XLConfig.LOGD("ifCreateFile: " + successed);
                    if (path.contains(".siexternal")) {
                        FileUtils.chmod(path, 0777);
                    }
                }
            } catch (IOException e) {
                String msg = (e != null && e.getMessage() != null) ? e.getMessage() : "";
                throw new StopRequestException(STATUS_FILE_ERROR,
                        "Failed to create target file " + path + " msg=" + msg, e);
            }
            return path;
        }
    }

    private static void checkSDcardState(Context context, File root) throws StopRequestException {
        if (!Environment.MEDIA_MOUNTED.equals(Environment.getStorageState(root))) {
            if (XSpaceUtil.isValidInXspace(context, root)) {
                return;
            }
            throw new StopRequestException(STATUS_DEVICE_NOT_FOUND_ERROR,
                    String.format("STATUS_DEVICE_NOT_FOUND_ERROR(%d) getFullPath(%s)",
                            STATUS_DEVICE_NOT_FOUND_ERROR, root == null ? "null" : root.toString()));
        }
    }

    private static String chooseFilename(String url, String hint, String contentDisposition,
                                         String contentLocation, int destination) {
        String filename = null;

        // First, try to use the hint from the application, if there's one
        if (hint != null && !hint.endsWith("/")) {
            XLConfig.LOGD("getting filename from hint");
            int index = hint.lastIndexOf('/') + 1;
            if (index > 0) {
                filename = hint.substring(index);
            } else {
                filename = hint;
            }
        }

        // If we couldn't do anything with the hint, move toward the content
        // disposition
        if (filename == null && contentDisposition != null) {
            filename = parseContentDisposition(contentDisposition);
            if (filename != null) {
                XLConfig.LOGD("getting filename from content-disposition");
                int index = filename.lastIndexOf('/') + 1;
                if (index > 0) {
                    filename = filename.substring(index);
                }
            }
        }

        // If we still have nothing at this point, try the content location
        if (filename == null && contentLocation != null) {
            String decodedContentLocation = Uri.decode(contentLocation);
            if (decodedContentLocation != null
                    && !decodedContentLocation.endsWith("/")
                    && decodedContentLocation.indexOf('?') < 0) {
                XLConfig.LOGD("getting filename from content-location");
                int index = decodedContentLocation.lastIndexOf('/') + 1;
                if (index > 0) {
                    filename = decodedContentLocation.substring(index);
                } else {
                    filename = decodedContentLocation;
                }
            }
        }

        // If all the other http-related approaches failed, use the plain uri
        if (filename == null) {
            String decodedUrl = Uri.decode(url);
            if (decodedUrl != null
                    && !decodedUrl.endsWith("/") && decodedUrl.indexOf('?') < 0) {
                int index = decodedUrl.lastIndexOf('/') + 1;
                if (index > 0) {
                    XLConfig.LOGD("getting filename from uri");
                    filename = decodedUrl.substring(index);
                }
            }
        }

        // Finally, if couldn't get filename from URI, get a generic filename
        if (filename == null) {
            XLConfig.LOGD("using default filename");
            filename = Constants.DEFAULT_DL_FILENAME;
        }

        // The VFAT file system is assumed as target for downloads.
        // Replace invalid characters according to the specifications of VFAT.
        filename = replaceInvalidVfatCharacters(filename);

        return filename;
    }

    private static String chooseExtensionFromMimeType(String mimeType, boolean useDefaults) {
        String extension = null;
        if (mimeType != null) {
            extension = MimeTypeMap.getSingleton().getExtensionFromMimeType(mimeType);
            if (extension != null) {
                XLConfig.LOGD("adding extension from type");
                extension = "." + extension;
            } else {
                XLConfig.LOGD("couldn't find extension for " + mimeType);
            }
        }
        if (extension == null) {
            if (mimeType != null && mimeType.toLowerCase().startsWith("text/")) {
                if ("text/html".equalsIgnoreCase(mimeType)) {
                    XLConfig.LOGD("adding default html extension");
                    extension = Constants.DEFAULT_DL_HTML_EXTENSION;
                } else if (useDefaults) {
                    XLConfig.LOGD("adding default text extension");
                    extension = Constants.DEFAULT_DL_TEXT_EXTENSION;
                }
            } else if (useDefaults) {
                XLConfig.LOGD("adding default binary extension");
                extension = Constants.DEFAULT_DL_BINARY_EXTENSION;
            }
        }
        return extension;
    }

    private static String chooseExtensionFromFilename(String mimeType, int destination,
                                                      String filename, int lastDotIndex, boolean fixExtension) {
        String extension = null;
        if (mimeType != null && fixExtension) {
            // Compare the last segment of the extension against the mime type.
            // If there's a mismatch, discard the entire extension.
            String typeFromExt = MimeTypeMap.getSingleton().getMimeTypeFromExtension(
                    filename.substring(lastDotIndex + 1));
            if (typeFromExt == null || !typeFromExt.equalsIgnoreCase(mimeType)) {
                extension = chooseExtensionFromMimeType(mimeType, false);
                if (extension != null) {
                    XLConfig.LOGD("substituting extension from type");
                } else {
                    XLConfig.LOGD("couldn't find extension for " + mimeType);
                }
            }
        }
        if (extension == null) {
            XLConfig.LOGD("keeping extension");
            extension = filename.substring(lastDotIndex);
        }
        return extension;
    }

    private static String chooseUniqueFilenameLocked(int destination, String filename,
                                                     String extension, boolean recoveryDir) throws StopRequestException {
        String fullFilename = filename + extension;
        String downloadingFilename = fullFilename + sDownloadingExtension;
        // check whether xxx and xxx.midownload exist.
        if ((!new File(fullFilename).exists() && !new File(downloadingFilename).exists())
                && (!recoveryDir ||
                (destination != Downloads.Impl.DESTINATION_CACHE_PARTITION &&
                        destination != Downloads.Impl.DESTINATION_SYSTEMCACHE_PARTITION &&
                        destination != Downloads.Impl.DESTINATION_CACHE_PARTITION_PURGEABLE &&
                        destination != Downloads.Impl.DESTINATION_CACHE_PARTITION_NOROAMING))) {
            return fullFilename;
        }
        String newfilename = filename + Constants.FILENAME_SEQUENCE_SEPARATOR;
        /*
         * This number is used to generate partially randomized filenames to
         * avoid collisions. It starts at 1. The next 9 iterations increment it
         * by 1 at a time (up to 10). The next 9 iterations increment it by 1 to
         * 10 (random) at a time. The next 9 iterations increment it by 1 to 100
         * (random) at a time. ... Up to the point where it increases by
         * 100000000 at a time. (the maximum value that can be reached is
         * 1000000000) As soon as a number is reached that generates a filename
         * that doesn't exist, that filename is used. If the filename coming in
         * is [base].[ext], the generated filenames are [base]-[sequence].[ext].
         */
        int sequence = 1;
        for (int magnitude = 1; magnitude < 1000000000; magnitude *= 10) {
            for (int iteration = 0; iteration < 9; ++iteration) {
                fullFilename = newfilename + sequence + extension;
                downloadingFilename = newfilename + sequence + extension + sDownloadingExtension;
                if (!new File(fullFilename).exists() && !new File(downloadingFilename).exists()) {
                    return fullFilename;
                }
                XLConfig.LOGD("file with sequence number " + sequence + " exists");
                sequence += sRandom.nextInt(magnitude) + 1;
            }
        }
        throw new StopRequestException(STATUS_FILE_ERROR,
                "failed to generate an unused filename on internal download storage");
    }

    public static String getUniqueFilename(String path)
            throws StopRequestException {
        int dotIndex = path.lastIndexOf('.');
        String filename = path;
        String extension = "";
        if (dotIndex > 0) {
            filename = path.substring(0, dotIndex);
            extension = path.substring(dotIndex);
        }

        XLConfig.LOGD("target file: " + filename + extension);
        return generateUniqueFilename(filename, extension);
    }

    private static String generateUniqueFilename(String filename, String extension)
            throws StopRequestException {
        String fullFilename = filename + extension;
        String downloadingFilename = fullFilename + sDownloadingExtension;
        if (!new File(fullFilename).exists() && !new File(downloadingFilename).exists()) {
            return fullFilename;
        }
        /*
         * This number is used to generate partially randomized filenames to
         * avoid collisions. It starts at 1. The next 9 iterations increment it
         * by 1 at a time (up to 10). The next 9 iterations increment it by 1 to
         * 10 (random) at a time. The next 9 iterations increment it by 1 to 100
         * (random) at a time. ... Up to the point where it increases by
         * 100000000 at a time. (the maximum value that can be reached is
         * 1000000000) As soon as a number is reached that generates a filename
         * that doesn't exist, that filename is used. If the filename coming in
         * is [base].[ext], the generated filenames are [base]-[sequence].[ext].
         */
        String newfilename = filename + Constants.FILENAME_SEQUENCE_SEPARATOR;
        int sequence = 1;
        for (int magnitude = 1; magnitude < 1000000000; magnitude *= 10) {
            for (int iteration = 0; iteration < 9; ++iteration) {
                fullFilename = newfilename + sequence + extension;
                downloadingFilename = newfilename + sequence + extension + sDownloadingExtension;
                if (!new File(fullFilename).exists() && !new File(downloadingFilename).exists()) {
                    return fullFilename;
                }
                XLConfig.LOGD("file with sequence number " + sequence + " exists");
                sequence += sRandom.nextInt(magnitude) + 1;
            }
        }
        throw new StopRequestException(STATUS_FILE_ERROR,
                "failed to generate an unused filename on internal download storage");
    }

    public static boolean isFilenameValid2(Context context, File file) {
        return isFilenameValid(context, file, true);
    }

    /**
     * Checks whether the filename looks legitimate for security purposes. This
     * prevents us from opening files that aren't actually downloads.
     */
    public static boolean isFilenameValid2(Context context, File file, boolean allowInternal) {
        try {
            if (allowInternal) {
                if (containsCanonical(context.getFilesDir(), file)
                        || containsCanonical(context.getCacheDir(), file)
                        || containsCanonical(Environment.getDownloadCacheDirectory(), file)) {
                    return true;
                }
            }

            final android.os.storage.StorageVolume[] volumes = android.os.storage.StorageManager.getVolumeList(UserHandle.myUserId(),
                    android.os.storage.StorageManager.FLAG_FOR_WRITE);
            for (android.os.storage.StorageVolume volume : volumes) {
                if (containsCanonical(volume.getPathFile(), file)) {
                    return true;
                }
            }
        } catch (IOException e) {
            Log.w(TAG, "Failed to resolve canonical path: " + e);
            return false;
        }

        Log.w(TAG, "Path appears to be invalid: " + file);
        return false;
    }

    /**
     * Checks whether the filename looks legitimate
     */
    static boolean isFilenameValid(String filename, File downloadsDataDir) {
        String fName;
        final String[] whitelist;
        try {
            fName = new File(filename).getCanonicalPath();
            whitelist = new String[]{
                    downloadsDataDir.getCanonicalPath(),
                    Environment.getDownloadCacheDirectory().getCanonicalPath(),
                    Environment.getExternalStorageDirectory().getCanonicalPath(),
            };
        } catch (IOException e) {
            XLConfig.LOGD("Failed to resolve canonical path: ", e);
            return false;
        }

        if (TextUtils.isEmpty(fName)) {
            return false;
        }
        for (String test : whitelist) {
            if (fName.startsWith(test)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks whether this looks like a legitimate selection parameter
     */
    public static void validateSelection(String selection, Set<String> allowedColumns) {
        try {
            if (selection == null || selection.isEmpty()) {
                return;
            }
            Lexer lexer = new Lexer(selection, allowedColumns);
            parseExpression(lexer);
            if (lexer.currentToken() != Lexer.TOKEN_END) {
                throw new IllegalArgumentException("syntax error");
            }
        } catch (RuntimeException ex) {
            XLConfig.LOGD("invalid selection [" + selection + "] triggered ", ex);
            throw ex;
        }

    }

    // expression <- ( expression ) | statement [AND_OR ( expression ) |
    // statement] *
    // | statement [AND_OR expression]*
    private static void parseExpression(Lexer lexer) {
        for (; ; ) {
            // ( expression )
            if (lexer.currentToken() == Lexer.TOKEN_OPEN_PAREN) {
                lexer.advance();
                parseExpression(lexer);
                if (lexer.currentToken() != Lexer.TOKEN_CLOSE_PAREN) {
                    throw new IllegalArgumentException("syntax error, unmatched parenthese");
                }
                lexer.advance();
            } else {
                // statement
                parseStatement(lexer);
            }
            if (lexer.currentToken() != Lexer.TOKEN_AND_OR) {
                break;
            }
            lexer.advance();
        }
    }

    // statement <- COLUMN COMPARE VALUE
    // | COLUMN IS NULL
    private static void parseStatement(Lexer lexer) {
        // both possibilities start with COLUMN
        if (lexer.currentToken() != Lexer.TOKEN_COLUMN) {
            throw new IllegalArgumentException("syntax error, expected column name");
        }
        lexer.advance();

        // statement <- COLUMN COMPARE VALUE
        if (lexer.currentToken() == Lexer.TOKEN_COMPARE) {
            lexer.advance();
            if (lexer.currentToken() != Lexer.TOKEN_VALUE) {
                throw new IllegalArgumentException("syntax error, expected quoted string");
            }
            lexer.advance();
            return;
        }

        // statement <- COLUMN IS NULL
        if (lexer.currentToken() == Lexer.TOKEN_IS) {
            lexer.advance();
            if (lexer.currentToken() != Lexer.TOKEN_NULL) {
                throw new IllegalArgumentException("syntax error, expected NULL");
            }
            lexer.advance();
            return;
        }

        // didn't get anything good after COLUMN
        throw new IllegalArgumentException("syntax error after column name");
    }

    /**
     * A simple lexer that recognizes the words of our restricted subset of SQL
     * where clauses
     */
    private static class Lexer {
        public static final int TOKEN_START = 0;
        public static final int TOKEN_OPEN_PAREN = 1;
        public static final int TOKEN_CLOSE_PAREN = 2;
        public static final int TOKEN_AND_OR = 3;
        public static final int TOKEN_COLUMN = 4;
        public static final int TOKEN_COMPARE = 5;
        public static final int TOKEN_VALUE = 6;
        public static final int TOKEN_IS = 7;
        public static final int TOKEN_NULL = 8;
        public static final int TOKEN_END = 9;

        private final String mSelection;
        private final Set<String> mAllowedColumns;
        private int mOffset = 0;
        private int mCurrentToken = TOKEN_START;
        private final char[] mChars;

        public Lexer(String selection, Set<String> allowedColumns) {
            mSelection = selection;
            mAllowedColumns = allowedColumns;
            mChars = new char[mSelection.length()];
            mSelection.getChars(0, mChars.length, mChars, 0);
            advance();
        }

        public int currentToken() {
            return mCurrentToken;
        }

        public void advance() {
            char[] chars = mChars;

            // consume whitespace
            while (mOffset < chars.length && chars[mOffset] == ' ') {
                ++mOffset;
            }

            // end of input
            if (mOffset == chars.length) {
                mCurrentToken = TOKEN_END;
                return;
            }

            // "("
            if (chars[mOffset] == '(') {
                ++mOffset;
                mCurrentToken = TOKEN_OPEN_PAREN;
                return;
            }

            // ")"
            if (chars[mOffset] == ')') {
                ++mOffset;
                mCurrentToken = TOKEN_CLOSE_PAREN;
                return;
            }

            // "?"
            if (chars[mOffset] == '?') {
                ++mOffset;
                mCurrentToken = TOKEN_VALUE;
                return;
            }

            // "=" and "=="
            if (chars[mOffset] == '=') {
                ++mOffset;
                mCurrentToken = TOKEN_COMPARE;
                if (mOffset < chars.length && chars[mOffset] == '=') {
                    ++mOffset;
                }
                return;
            }

            // ">" and ">="
            if (chars[mOffset] == '>') {
                ++mOffset;
                mCurrentToken = TOKEN_COMPARE;
                if (mOffset < chars.length && chars[mOffset] == '=') {
                    ++mOffset;
                }
                return;
            }

            // "<", "<=" and "<>"
            if (chars[mOffset] == '<') {
                ++mOffset;
                mCurrentToken = TOKEN_COMPARE;
                if (mOffset < chars.length && (chars[mOffset] == '=' || chars[mOffset] == '>')) {
                    ++mOffset;
                }
                return;
            }

            // "!="
            if (chars[mOffset] == '!') {
                ++mOffset;
                mCurrentToken = TOKEN_COMPARE;
                if (mOffset < chars.length && chars[mOffset] == '=') {
                    ++mOffset;
                    return;
                }
                throw new IllegalArgumentException("Unexpected character after !");
            }

            // columns and keywords
            // first look for anything that looks like an identifier or a
            // keyword
            // and then recognize the individual words.
            // no attempt is made at discarding sequences of underscores with no
            // alphanumeric
            // characters, even though it's not clear that they'd be legal
            // column names.
            if (isIdentifierStart(chars[mOffset])) {
                int startOffset = mOffset;
                ++mOffset;
                while (mOffset < chars.length && isIdentifierChar(chars[mOffset])) {
                    ++mOffset;
                }
                String word = mSelection.substring(startOffset, mOffset);
                if (mOffset - startOffset <= 4) {
                    if ("IS".equals(word)) {
                        mCurrentToken = TOKEN_IS;
                        return;
                    }
                    if ("OR".equals(word) || "AND".equals(word)) {
                        mCurrentToken = TOKEN_AND_OR;
                        return;
                    }
                    if ("NULL".equals(word)) {
                        mCurrentToken = TOKEN_NULL;
                        return;
                    }
                    // LIKE
                    if ("LIKE".equals(word)) {
                        mCurrentToken = TOKEN_COMPARE;
                        return;
                    }
                    // NOT LIKE
                    if ("NOT".equals(word)) {
                        // consume whitespace
                        while (mOffset < chars.length && chars[mOffset] == ' ') {
                            ++mOffset;
                        }
                        if (isIdentifierStart(chars[mOffset])) {
                            int startOffsetLike = mOffset;
                            ++mOffset;
                            while (mOffset < chars.length && isIdentifierChar(chars[mOffset])) {
                                ++mOffset;
                            }
                            String wordLike = mSelection.substring(startOffsetLike, mOffset);
                            if ("LIKE".equals(wordLike)) {
                                mCurrentToken = TOKEN_COMPARE;
                                return;
                            }
                        }
                    }
                }
                if (mAllowedColumns.contains(word)) {
                    mCurrentToken = TOKEN_COLUMN;
                    return;
                }
                throw new IllegalArgumentException("unrecognized column or keyword " + word);
            }

            // quoted strings
            if (chars[mOffset] == '\'') {
                ++mOffset;
                while (mOffset < chars.length) {
                    if (chars[mOffset] == '\'') {
                        if (mOffset + 1 < chars.length && chars[mOffset + 1] == '\'') {
                            ++mOffset;
                        } else {
                            break;
                        }
                    }
                    ++mOffset;
                }
                if (mOffset == chars.length) {
                    throw new IllegalArgumentException("unterminated string");
                }
                ++mOffset;
                mCurrentToken = TOKEN_VALUE;
                return;
            }

            // anything we don't recognize
            throw new IllegalArgumentException("illegal character: " + chars[mOffset]);
        }
        private static final boolean isIdentifierStart(char c) {
            return c == '_' ||
                    (c >= 'A' && c <= 'Z') ||
                    (c >= 'a' && c <= 'z');
        }

        private static final boolean isIdentifierChar(char c) {
            return c == '_' ||
                    (c >= 'A' && c <= 'Z') ||
                    (c >= 'a' && c <= 'z') ||
                    (c >= '0' && c <= '9');
        }
    }

    /**
     * Replace invalid filename characters according to specifications of the
     * VFAT
     *
     * @note Package-private due to testing.
     */
    private static String replaceInvalidVfatCharacters(String filename) {
        final char START_CTRLCODE = 0x00;
        final char END_CTRLCODE = 0x1f;
        final char QUOTEDBL = 0x22;
        final char ASTERISK = 0x2A;
        final char SLASH = 0x2F;
        final char COLON = 0x3A;
        final char LESS = 0x3C;
        final char GREATER = 0x3E;
        final char QUESTION = 0x3F;
        final char BACKSLASH = 0x5C;
        final char BAR = 0x7C;
        final char DEL = 0x7F;
        final char UNDERSCORE = 0x5F;

        StringBuilder sb = new StringBuilder();
        char ch;
        boolean isRepetition = false;
        for (int i = 0; i < filename.length(); i++) {
            ch = filename.charAt(i);
            if ((START_CTRLCODE <= ch &&
                    ch <= END_CTRLCODE) ||
                    ch == QUOTEDBL ||
                    ch == ASTERISK ||
                    ch == SLASH ||
                    ch == COLON ||
                    ch == LESS ||
                    ch == GREATER ||
                    ch == QUESTION ||
                    ch == BACKSLASH ||
                    ch == BAR ||
                    ch == DEL) {
                if (!isRepetition) {
                    sb.append(UNDERSCORE);
                    isRepetition = true;
                }
            } else {
                sb.append(ch);
                isRepetition = false;
            }
        }
        return sb.toString();
    }

    public static Uri convertToMediaStoreDownloadsUri(Uri mediaStoreUri) {
        final String volumeName = MediaStore.getVolumeName(mediaStoreUri);
        final long id = android.content.ContentUris.parseId(mediaStoreUri);
        return MediaStore.Downloads.getContentUri(volumeName, id);
    }

    public static boolean isInternationalBuilder() {
        boolean res = false;
        if (BuildUtils.isCTSVersion() || BuildUtils.isInternationalVersion()) {
            res = true;
        }
        return res;
    }

    public static boolean isCmTestBuilder() {
        return miui.os.Build.IS_CM_CUSTOMIZATION_TEST;
    }

    /**
     * track for common
     */
    static void trackCommon(Context context, Map<String, String> trackData, int status,
                            long taskId, boolean xlEnable,
                            String xmId, String xlId, String pkgName, String product, String productVersion,
                            int network) {
        if (analyticsMark) {
            return;
        }

        if (trackData == null)
            return;
        boolean xlVipEnable = !DownloadSettings.XLShareConfigSettings.isVipEnable();
        String MIUIVersion = Build.VERSION.INCREMENTAL;
        String time = Long.toString(System.currentTimeMillis() / 1000);
        trackData.put("download_event", Integer.toString(10001));
        trackData.put("download_event_status", Integer.toString(status));
        trackData.put("download_seq_id", Long.toString(taskId));
        trackData.put("xunlei_open", String.valueOf(xlEnable ? 1 : 0));
        trackData.put("xunlei_vip_open", String.valueOf(xlVipEnable ? 1 : 0));
        trackData.put("product_name", XLConfig.PRODUCT_NAME);
        trackData.put("product_version", XLConfig.getVersionName(context));
        trackData.put("application_name", pkgName);
        trackData.put("phone_type", android.os.Build.MODEL);
        trackData.put("system_version", android.os.Build.VERSION.RELEASE);
        trackData.put("miui_version", MIUIVersion);
        trackData.put("network_type", Integer.toString(network));
        trackData.put("time", time);
    }

    public static XLNetWorkCarrier getNetWorkCarrier(Context context) {
        TelephonyManager telephonyManager = (TelephonyManager) context
                .getSystemService(Context.TELEPHONY_SERVICE);
        XLNetWorkCarrier carrier = XLNetWorkCarrier.NWC_Unknow;
        if (telephonyManager != null) {
            String simOperator = telephonyManager.getSimOperator();
            XLConfig.LOGD("in getNetWorkCarrier simOperator = " + simOperator);
            if ("46000".equals(simOperator) || "46002".equals(simOperator)) {
                carrier = XLNetWorkCarrier.NWC_CMCC;
            } else if ("46001".equals(simOperator)) {
                carrier = XLNetWorkCarrier.NWC_CU;
            } else if ("46003".equals(simOperator)) {
                carrier = XLNetWorkCarrier.NWC_CT;
            }
        }
        return carrier;
    }

    public static int getNetworkClass(int networkType, String networkTypeName) {
        switch (networkType) {
            case TelephonyManager.NETWORK_TYPE_GPRS:
            case TelephonyManager.NETWORK_TYPE_EDGE:
            case TelephonyManager.NETWORK_TYPE_CDMA:
            case TelephonyManager.NETWORK_TYPE_1xRTT:
            case TelephonyManager.NETWORK_TYPE_IDEN:
                return MOBILE_2G;
            case TelephonyManager.NETWORK_TYPE_UMTS:
            case TelephonyManager.NETWORK_TYPE_EVDO_0:
            case TelephonyManager.NETWORK_TYPE_EVDO_A:
            case TelephonyManager.NETWORK_TYPE_HSDPA:
            case TelephonyManager.NETWORK_TYPE_HSUPA:
            case TelephonyManager.NETWORK_TYPE_HSPA:
            case TelephonyManager.NETWORK_TYPE_EVDO_B:
            case TelephonyManager.NETWORK_TYPE_EHRPD:
            case TelephonyManager.NETWORK_TYPE_HSPAP:
                return MOBILE_3G;
            case TelephonyManager.NETWORK_TYPE_LTE:
                return MOBILE_4G;
            default:
                if ("WIFI".equals(networkTypeName))
                    return WIFI;
                else
                    return UNKNOWN_NETWORK;
        }
    }

    public static int getSpecificNetworkType(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);

        if (cm == null)
            return UNKNOWN_NETWORK;

        NetworkInfo networkInfo = cm.getActiveNetworkInfo();
        if (networkInfo != null) {
            int networkSubType = networkInfo.getSubtype();
            String networkType = networkInfo.getTypeName();
            return getNetworkClass(networkSubType, networkType);
        }
        return NO_NETWORK;
    }

    public static void openWarningDialog(Context context) {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setClass(context, WarningActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }


    public static long getUnsignedInt(int data) {
        return data & 0x0FFFFFFFFL;
    }

    public static Uri triggerMediaScan(android.content.ContentProviderClient mediaProviderClient,
            File file) {
        return MediaStore.scanFile(ContentResolver.wrap(mediaProviderClient), file);
    }

    public static final Uri getContentUriForPath(Context context, String path) {
        final android.os.storage.StorageManager sm = context.getSystemService(android.os.storage.StorageManager.class);
        final String volumeName = sm.getStorageVolume(new File(path)).getMediaStoreVolumeName();
        return MediaStore.Downloads.getContentUri(volumeName);
    }

    /**
     * 检测包名为pkgName的应用是否在前台运行，另外如果下载管理程序在前台运行，也返回true
     *
     * @param context
     * @param pkgName
     * @return
     */
    public static boolean isRunningForeground(Context context, String pkgName) {
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> runTasks = am.getRunningTasks(1);
        if (runTasks == null || runTasks.size() <= 0) {
            return false;
        }
        ComponentName cn = runTasks.get(0).topActivity;
        String currentPackageName = cn.getPackageName();
        if (!TextUtils.isEmpty(currentPackageName)
                && (currentPackageName.equals(pkgName) || currentPackageName.equals(context
                .getPackageName()))) {
            return true;
        }

        return false;
    }

    public static boolean isScreenLocked(Context c) {
        KeyguardManager mKeyguardManager = (KeyguardManager) c.getSystemService(c.KEYGUARD_SERVICE);
        return mKeyguardManager.inKeyguardRestrictedInputMode();
    }

    public static Bitmap getIconByPackage(Context context, String packageName) {
        Bitmap bitmap = null;

        try {
            PackageManager pm = context.getPackageManager();
            ApplicationInfo info = pm.getApplicationInfo(packageName, 0);
            Drawable drawable = info.loadIcon(pm);
            if (drawable instanceof BitmapDrawable) {
                bitmap = ((BitmapDrawable) drawable).getBitmap();
            }
            if (drawable != null) {
                drawable.setCallback(null);
            }
        } catch (Exception e) {
            XLConfig.LOGD("exc", e);
        }

        if (bitmap == null) {
            bitmap = BitmapFactory.decodeResource(context.getResources(),
                    R.mipmap.ic_launcher_download);
        }
        return bitmap;
    }

    /**
     * 判断env是否是英文环境(英文环境下不发运营通知)
     */
    public static boolean isEnglishEnv(Context context) {
        return "en".equals(context.getResources().getConfiguration().locale.getLanguage());
    }

    public static NetworkInfo getActiveNetworkInfo(Context context) {
        ConnectivityManager manager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        return manager.getActiveNetworkInfo();
    }

    public static boolean isNetworkAvailable(Context context) {
        ConnectivityManager manager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = manager.getActiveNetworkInfo();
        int type = info != null ? info.getType() : -2;
        boolean isCon = info != null && info.isConnected();
        XLConfig.LOGD("DownloadNotification", "isNetworkAvailable netType="
                + type + " isCon=" + isCon);
        return isCon;
    }

    public static boolean isNetworkWifi(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetInfo = connectivityManager.getActiveNetworkInfo();
        if (activeNetInfo != null && activeNetInfo.isConnected()
                && activeNetInfo.getType() == ConnectivityManager.TYPE_WIFI) {
            return true;
        }
        return false;
    }

    public static String getBssid(Context context) {
        if (!isNetworkWifi(context)) {
            return "";
        }

        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        return wifiInfo != null ? wifiInfo.getBSSID() : "";
    }

    public static boolean isTablet() {
        return BuildUtils.isTablet();
    }



    public static boolean isInCTSMode() {
        return BuildUtils.isCTSVersion() || !hasReadPhoneStatePermission();
    }

    private static boolean hasReadPhoneStatePermission(){
        return PackageManager.PERMISSION_GRANTED ==  DownloadApplication.getGlobalApplication().getPackageManager().checkPermission(Manifest.permission.READ_PHONE_STATE,DownloadApplication.getGlobalApplication().getPackageName());

    }

    public static boolean isBindUid() {
        return true;
    }


    public static void deleteByPackageName(final Context mContext, final String packageName) {
        XLConfig.LOGD(TAG, " deleteByPackageName and packagename: " + packageName);

        if (mContext == null || packageName == null) {
            return;
        }

        CustomThreadPool.asyncWork(new Runnable() {
            @Override
            public void run() {

                Cursor cursor = null;
                try {
                    StringBuilder sel = new StringBuilder();
                    sel.append(COLUMN_APK_PACKGENAME + "=?");
                    String[] args = new String[]{
                            packageName
                    };
                    ContentResolver mResolver = mContext.getContentResolver();
                    cursor = mResolver.query(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, new String[]{Downloads.Impl._ID, Downloads.Impl._DATA,XLConfig.COLUMN_APK_INSTALL_WAY}, sel.toString(), args, DownloadManager.COLUMN_ID + " DESC");
                    if (cursor != null && cursor.moveToFirst()) {
                        long downloadId = cursor.getLong(cursor.getColumnIndexOrThrow(Downloads.Impl._ID));
                        int apkInstallWay = cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_APK_INSTALL_WAY));
                        String fileName = cursor.getString(cursor.getColumnIndexOrThrow(Impl._DATA));
                        synchronized (Helpers.class) {
                            if (apkInstallWay == 1) {
                                XLConfig.LOGD(TAG, " deleteByPackageName and downloadId: " + downloadId);
                                FileUtil.deleteFileIfExists(fileName);
                                deleteDatabase(mResolver, downloadId);
                                DesktopProgressAppInfo appInfo = DesktopProgressAppInfo.appInfoMap.get(packageName);
                                if (appInfo != null) {
                                    appInfo.removeFromShell(packageName);
                                }
                                DesktopProgressAppInfo.appInfoMap.remove(packageName);
                            }
                        }
                    }

                } catch (Exception e) {
                    XLConfig.LOGD(TAG, "exc", e);
                } finally {
                    closeCursor(cursor);
                }
            }
        });


    }

    public static void updateByPackageName(final Context mContext, final String packageName) {
        XLConfig.LOGD(TAG, "updateByPackageName and packagename: " + packageName);

        CustomThreadPool.asyncWork(new Runnable() {
            @Override
            public void run() {

                Cursor cursor = null;
                try {
                    StringBuilder sel = new StringBuilder();
                    sel.append(COLUMN_APK_PACKGENAME + "=?");
                    String[] args = new String[]{
                            packageName
                    };
                    ContentResolver mResolver = mContext.getContentResolver();
                    cursor = mResolver.query(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, new String[]{Downloads.Impl._ID, XLConfig.COLUMN_APK_INSTALL_WAY, Impl.COLUMN_STATUS}, sel.toString(), args, DownloadManager.COLUMN_ID + " DESC");
                    if (cursor != null && cursor.moveToFirst()) {
                        long downloadId = cursor.getLong(cursor.getColumnIndexOrThrow(Downloads.Impl._ID));
                        int apkInstallWay = cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_APK_INSTALL_WAY));
                        int columStatus = cursor.getInt(cursor.getColumnIndexOrThrow(Impl.COLUMN_STATUS));

                        if (apkInstallWay == 1) {
                            XLConfig.LOGD(TAG, " updateByPackageName and downloadId: " + downloadId);
                            updateDatabase(mResolver, downloadId, columStatus);
                        }

                    }

                } catch (Exception e) {
                    XLConfig.LOGD("exc", e);
                } finally {
                    closeCursor(cursor);
                }
            }
        });
    }

    private static void closeCursor(Cursor cursor) {
        if (cursor != null) {
            cursor.close();
        }
    }

    private static void deleteDatabase(ContentResolver contentResolver, long id) {
        StringBuilder where = new StringBuilder();
        where.append(Impl._ID + "=?");

        String[] args = new String[]{
                String.valueOf(id),
        };
        contentResolver.delete(Impl.CONTENT_URI, where.toString(), args);
    }

    private static void updateDatabase(ContentResolver contentResolver, long id, int columStatus) {

        if (columStatus != Impl.STATUS_RUNNING && columStatus != Impl.STATUS_PAUSED_BY_APP) {
            return;
        }

        StringBuilder where = new StringBuilder();
        where.append(Impl._ID + "=?");
        where.append(" AND (");
        where.append(Impl.COLUMN_STATUS + "=?" + " OR ");
        where.append(Impl.COLUMN_STATUS + "=?" + " OR ");
        where.append(Impl.COLUMN_STATUS + "=?" + " OR ");
        where.append(Impl.COLUMN_STATUS + "=?" + ")");

        String[] args = new String[]{
                String.valueOf(id),
                String.valueOf(columStatus),
                String.valueOf(Impl.STATUS_WAITING_TO_RETRY),
                String.valueOf(Impl.STATUS_WAITING_FOR_NETWORK),
                String.valueOf(Impl.STATUS_QUEUED_FOR_WIFI)
        };

        ContentValues contentValues = new ContentValues();

        contentValues.put(Impl.COLUMN_STATUS, columStatus == Impl.STATUS_RUNNING ? Impl.STATUS_PAUSED_BY_APP : Impl.STATUS_RUNNING);
        contentValues.put(Impl.COLUMN_CONTROL, columStatus == Impl.STATUS_RUNNING ? 1 : 0);
        contentResolver.update(Impl.CONTENT_URI, contentValues, where.toString(), args);

    }

    public static boolean isFilenameValidInExternal(Context context, File file) {
        return isFilenameValid(context, file, false);
    }

    /**
     * Test if given file exists in one of the package-specific external storage
     * directories that are always writable to apps, regardless of storage
     * permission.
     */
    static boolean isFilenameValidInExternalPackage(File file, String packageName) {
        try {
            if (containsCanonical(buildExternalStorageAppDataDirs(packageName), file) ||
                    containsCanonical(buildExternalStorageAppObbDirs(packageName), file) ||
                    containsCanonical(buildExternalStorageAppMediaDirs(packageName), file)) {
                return true;
            }
        } catch (IOException e) {
            Log.w(TAG, "Failed to resolve canonical path: " + (file == null ? "null" : file.getAbsolutePath()), e);
            return false;
        }

        return false;
    }

    public static boolean isFilenameValidInExternalObbDir(File file) {
        try {
            if (containsCanonical(buildExternalStorageAndroidObbDirs(), file)) {
                return true;
            }
        } catch (IOException e) {
            Log.w(TAG, "Failed to resolve canonical path: " + file.getAbsolutePath(), e);
            return false;
        }

        return false;
    }

    /**
     * Check if given file exists in one of the private package-specific external storage
     * directories.
     */
    static boolean isFileInPrivateExternalAndroidDirs(File file) {
        try {
            return PATTERN_ANDROID_PRIVATE_DIRS.matcher(file.getCanonicalPath()).matches();
        } catch (IOException e) {
            Log.w(TAG, "Failed to resolve canonical path: " + file.getAbsolutePath(), e);
        }

        return false;
    }

    /**
     * Checks destination file path restrictions adhering to App privacy restrictions
     *
     * Note: This method is extracted to a static method for better test coverage.
     */
    @VisibleForTesting
    public static void checkDestinationFilePathRestrictions(File file, String callingPackage,
                                                     Context context, AppOpsManager appOpsManager, String callingAttributionTag,
                                                     boolean isLegacyMode, boolean allowDownloadsDirOnly) {
        boolean isFileNameValid = allowDownloadsDirOnly ? isFilenameValidInPublicDownloadsDir(file)
                : isFilenameValidInKnownPublicDir(file.getAbsolutePath());
        if (isFilenameValidInExternalPackage(file, callingPackage) || isFileNameValid) {
            // No permissions required for paths belonging to calling package or
            // public downloads dir.
            return;
        } else if (isFilenameValidInExternalObbDir(file) &&
                isCallingAppInstaller(context, appOpsManager, callingPackage)) {
            // Installers are allowed to download in OBB dirs, even outside their own package
            return;
        } else if (isFileInPrivateExternalAndroidDirs(file)) {
            // Positive cases of writing to external Android dirs is covered in the if blocks above.
            // If the caller made it this far, then it cannot write to this path as it is restricted
            // from writing to other app's external Android dirs.
            throw new SecurityException("Unsupported path " + file);
        } else if (isLegacyMode && isFilenameValidInExternal(context, file)) {
            // Otherwise we require write permission
            context.enforceCallingOrSelfPermission(
                    android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    "No permission to write to " + file);

            if (appOpsManager.noteProxyOp(AppOpsManager.OP_WRITE_EXTERNAL_STORAGE,
                    callingPackage, Binder.getCallingUid(), callingAttributionTag, null)
                    != AppOpsManager.MODE_ALLOWED) {
                throw new SecurityException("No permission to write to " + file);
            }
        } else {
            throw new SecurityException("Unsupported path " + file);
        }
    }

    private static boolean isCallingAppInstaller(Context context, AppOpsManager appOpsManager,
                                                 String callingPackage) {
        return (appOpsManager.noteOp(AppOpsManager.OP_REQUEST_INSTALL_PACKAGES,
                Binder.getCallingUid(), callingPackage, null, "obb_download")
                == AppOpsManager.MODE_ALLOWED)
                || (context.checkCallingOrSelfPermission(
                android.Manifest.permission.REQUEST_INSTALL_PACKAGES)
                == PackageManager.PERMISSION_GRANTED);
    }


    private static final String XSPACE_USER_DIR = "/storage/emulated/" + XSpaceUserHandle.USER_XSPACE;


    public static boolean containsCanonical(File dir, File file) throws IOException {
        return contains(dir.getCanonicalFile(), file);
    }

    public static boolean containsCanonical(File[] dirs, File file) throws IOException {
        for (File dir : dirs) {
            if (containsCanonical(dir, file)) {
                return true;
            }
        }
        return false;
    }

    public static boolean contains(File dir, File file) {
        if (file == null) return false;
        String dirPath = dir.getAbsolutePath();
        String filePath = file.getAbsolutePath();

        if (dirPath.equals(filePath)) {
            return true;
        }

        if (!dirPath.endsWith("/")) {
            dirPath += "/";
        }
        return filePath.startsWith(dirPath);
    }

	public static boolean isFilenameValidInPublicDownloadsDir(File file) {
        try {
            if (containsCanonical(buildExternalStoragePublicDirs(
                    Environment.DIRECTORY_DOWNLOADS), file)) {
                return true;
            }
        } catch (IOException e) {
            Log.w(TAG, "Failed to resolve canonical path: " + e);
            return false;
        }

        return false;
    }

    @com.android.internal.annotations.VisibleForTesting
    public static boolean isFilenameValidInKnownPublicDir(@Nullable String filePath) {
        if (filePath == null) {
            return false;
        }
        final Matcher matcher = PATTERN_PUBLIC_DIRS.matcher(filePath);
        if (matcher.matches()) {
            final String publicDir = matcher.group(1);
            return ArrayUtils.contains(Environment.STANDARD_DIRECTORIES, publicDir);
        }
        return false;
    }

    /**
     * Checks whether the filename looks legitimate for security purposes. This
     * prevents us from opening files that aren't actually downloads.
     */
    public static boolean isFilenameValid(Context context, File file, boolean allowInternal) {
        try {
            File canonicalFile = file.getCanonicalFile();

            if (allowInternal && (containsCanonical(context.getFilesDir(), canonicalFile)
                    || containsCanonical(context.getCacheDir(), canonicalFile)
                    || containsCanonical(Environment.getDownloadCacheDirectory(), canonicalFile))) {
                return true;
            }
            android.os.storage.StorageManager storageManager = android.os.storage.StorageManager.from(context);
            final android.os.storage.StorageVolume[] volumes = storageManager.getVolumeList();
            for (android.os.storage.StorageVolume volume : volumes) {
                if (containsCanonical(volume.getPathFile(), canonicalFile)) {
                    return true;
                }
            }
            // Support XSpace
            // In owner space and xspace is actived
            if (context.getUserId() == UserHandle.USER_OWNER
                    && Secure.getBoolean(context.getContentResolver(), Secure.XSPACE_ENABLED, false)) {
                if (containsCanonical(new File(XSPACE_USER_DIR), canonicalFile)) {
                    return true;
                }
            }
        } catch (IOException e) {
            Log.w(TAG, "Failed to resolve canonical path: " + e);
            return false;
        }

        return false;
    }
    public static boolean isFileInExternalAndroidDirs(String filePath) {
        return PATTERN_ANDROID_DIRS.matcher(filePath).matches();
    }

    @VisibleForTesting
    public static void handleRemovedUidEntries(@NonNull Context context,
            @NonNull ContentProvider downloadProvider, int removedUid,
            @Nullable BiConsumer<String, Long> validEntryConsumer) {
        final SparseArray<String> knownUids = new SparseArray<>();
        final ArrayList<Long> idsToDelete = new ArrayList<>();
        final ArrayList<Long> idsToOrphan = new ArrayList<>();
        final String selection = removedUid == -1 ? Constants.UID + " IS NOT NULL"
                : Constants.UID + "='" + removedUid+"'";
        try (Cursor cursor = downloadProvider.query(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI,
                new String[] { Downloads.Impl._ID, Constants.UID, COLUMN_DESTINATION, COLUMN_DATA },
                selection, null, null)) {
            while (cursor.moveToNext()) {
                final long downloadId = cursor.getLong(0);
                final int uid = cursor.getInt(1);

                final String ownerPackageName;
                final int index = knownUids.indexOfKey(uid);
                if (index >= 0) {
                    ownerPackageName = knownUids.valueAt(index);
                } else {
                    ownerPackageName = getPackageForUid(context, uid);
                    knownUids.put(uid, ownerPackageName);
                }

                if (ownerPackageName == null) {
                    final int destination = cursor.getInt(2);
                    final String filePath = cursor.getString(3);

                    if ((destination == DESTINATION_EXTERNAL
                            || destination == DESTINATION_FILE_URI
                            || destination == DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD)
                            && isFilenameValidInKnownPublicDir(filePath)) {
                        idsToOrphan.add(downloadId);
                    } else {
                        idsToDelete.add(downloadId);
                    }
                } else if (validEntryConsumer != null) {
                    validEntryConsumer.accept(ownerPackageName, downloadId);
                }
            }
        }

        if (idsToOrphan.size() > 0) {
            Log.i(Constants.TAG, "Orphaning downloads with ids "
                    + Arrays.toString(idsToOrphan.toArray()) + " as owner package is removed");
            final ContentValues values = new ContentValues();
            values.putNull(Constants.UID);
            for (long id : idsToOrphan) {
                downloadProvider.update(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, values,
                        Impl._ID + " = ?", new String[]{String.valueOf(id)});
            }
        }
        if (idsToDelete.size() > 0) {
            Log.i(Constants.TAG, "Deleting downloads with ids "
                    + Arrays.toString(idsToDelete.toArray()) + " as owner package is removed");
            for (long id : idsToDelete) {
                downloadProvider.delete(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI,
                        Impl._ID + " = ?", new String[]{String.valueOf(id)});
            }
        }
    }

    public static String buildQueryWithIds(ArrayList<Long> downloadIds) {
        final StringBuilder queryBuilder = new StringBuilder(Downloads.Impl._ID + " in (");
        final int size = downloadIds.size();
        for (int i = 0; i < size; i++) {
            queryBuilder.append(downloadIds.get(i));
            queryBuilder.append((i == size - 1) ? ")" : ",");
        }
        return queryBuilder.toString();
    }

    public static String getPackageForUid(Context context, int uid) {
        String[] packages = context.getPackageManager().getPackagesForUid(uid);
        if (packages == null || packages.length == 0) {
            return null;
        }
        // For permission related purposes, any package belonging to the given uid should work.
        return packages[0];
    }

    public static boolean isPad() {
        return miui.os.Build.IS_TABLET;
    }

    public static boolean isFold(Context context) {
        return DeviceHelper.isFoldable() && DeviceHelper.isInternalScreen(context);
    }

    /**
     * Shamelessly borrowed from
     * {@code packages/providers/MediaProvider/src/com/android/providers/media/util/FileUtils.java}.
     */
    private static final Pattern PATTERN_RELATIVE_PATH = Pattern.compile(
            "(?i)^/storage/(?:emulated/[0-9]+/|[^/]+/)(Android/sandbox/([^/]+)/)?");

    /**
     * Shamelessly borrowed from
     * {@code packages/providers/MediaProvider/src/com/android/providers/media/util/FileUtils.java}.
     */
    private static final Pattern PATTERN_VOLUME_NAME = Pattern.compile(
            "(?i)^/storage/([^/]+)");

    /**
     * Shamelessly borrowed from
     * {@code packages/providers/MediaProvider/src/com/android/providers/media/util/FileUtils.java}.
     */
    private static @Nullable String normalizeUuid(@Nullable String fsUuid) {
        return fsUuid != null ? fsUuid.toLowerCase(Locale.ROOT) : null;
    }

    /**
     * Shamelessly borrowed from
     * {@code packages/providers/MediaProvider/src/com/android/providers/media/util/FileUtils.java}.
     */
    public static @Nullable String extractVolumeName(@Nullable String data) {
        if (data == null) return null;
        final Matcher matcher = PATTERN_VOLUME_NAME.matcher(data);
        if (matcher.find()) {
            final String volumeName = matcher.group(1);
            if (volumeName.equals("emulated")) {
                return MediaStore.VOLUME_EXTERNAL_PRIMARY;
            } else {
                return normalizeUuid(volumeName);
            }
        } else {
            return MediaStore.VOLUME_INTERNAL;
        }
    }

    /**
     * Shamelessly borrowed from
     * {@code packages/providers/MediaProvider/src/com/android/providers/media/util/FileUtils.java}.
     */
    public static @Nullable String extractRelativePath(@Nullable String data) {
        if (data == null) return null;
        final Matcher matcher = PATTERN_RELATIVE_PATH.matcher(data);
        if (matcher.find()) {
            final int lastSlash = data.lastIndexOf('/');
            if (lastSlash == -1 || lastSlash < matcher.end()) {
                // This is a file in the top-level directory, so relative path is "/"
                // which is different than null, which means unknown path
                return "/";
            } else {
                return data.substring(matcher.end(), lastSlash + 1);
            }
        } else {
            return null;
        }
    }

    /**
     * Shamelessly borrowed from
     * {@code packages/providers/MediaProvider/src/com/android/providers/media/util/FileUtils.java}.
     */
    public static @Nullable String extractDisplayName(@Nullable String data) {
        if (data == null) return null;
        if (data.indexOf('/') == -1) {
            return data;
        }
        if (data.endsWith("/")) {
            data = data.substring(0, data.length() - 1);
        }
        return data.substring(data.lastIndexOf('/') + 1);
    }
}
