package com.android.providers.downloads.util;

import static com.android.providers.downloads.config.XLDownloadCfg.COLUMN_EXTRA2;

import android.content.ContentValues;
import android.text.TextUtils;

import com.android.providers.downloads.helper.FeatureSwitch;
import com.android.providers.downloads.utils.JSONUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 这个使用来解析和校验下载扩展Extra2的
 * Created by lxy on 15/12/9.
 */
public class DownloadExtra2 {
    private final static String KEY_SUB_PACKAGE = "subPackage";
    private final static String KEY_NOTIFICATION_ICON_URL = "notificationIconUrl";
    private final static String KEY_USEENGINE = "useEngine";
    // MIUI ADD FOR KCG: START
    private final static String KEY_USE_KCGENGINE = "useKcgEngine";
    // END
    private final static String KEY_USESCDN = "useScdn";
    private final static String KEY_BYPASS_RECOMMENDED_SIZE_LIMIT = "bypass_recommended_size_limit";
    private final static String KEY_ENABLE_M3U8 = "enable_m3u8";
    private final static String KEY_COMPLETE_CLASS = "complete_class";
    private final static String KEY_WAKELOCK_TIME = "wakelock_time";
    private final static String KEY_FILE_OPTIMIZE = "file_optimize";

    private final static String url1 = "";//"https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1514383748915&di=30bfd39547974a75cc864ea69a5facfc&imgtype=0&src=http%3A%2F%2Fa.hiphotos.baidu.com%2Fimage%2Fpic%2Fitem%2Ffd039245d688d43fe757c123771ed21b0ff43b60.jpg";
    private final static String url2 = "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1514386188875&di=cba8eb9cbe8bd5a51ca943b675005210&imgtype=0&src=http%3A%2F%2Fpic35.nipic.com%2F20131121%2F2531170_145358633000_2.jpg";
    public final static String ZHIDA_JSON1 = String.format("{\"subPackage\":\"%s\",\"notificationIconUrl\":\"%s\"}", "com.android.app1", url1);
    public final static String ZHIDA_JSON2 = String.format("{\"subPackage\":\"%s\",\"notificationIconUrl\":\"%s\"}", "com.android.app2", url2);

    public static String getNotificationIconUrl(String jsonStr) {
        return JSONUtils.getString(jsonStr, KEY_NOTIFICATION_ICON_URL, null);
    }

    public static String getSubPackage(String jsonStr) {
        return JSONUtils.getString(jsonStr, KEY_SUB_PACKAGE, null);
    }

    public static int getUseEngine(String jsonStr) {
        if (TextUtils.isEmpty(jsonStr)) {
            return -1;
        }
        int UseEngine = -1;
        try {
            JSONObject jsonObject = new JSONObject(jsonStr);
            boolean use = jsonObject.getBoolean(KEY_USEENGINE);
            UseEngine = use ? 1 : 0;
        } catch (JSONException e) {
        }
        return UseEngine;
    }
    public static boolean isFileOptimize(String jsonStr) {
        if (TextUtils.isEmpty(jsonStr)) {
            return false;
        }
        boolean optimize = false;
        try {
            JSONObject jsonObject = new JSONObject(jsonStr);
            if (jsonObject.has(KEY_FILE_OPTIMIZE)) {
                optimize = jsonObject.getBoolean(KEY_FILE_OPTIMIZE);
            }
        } catch (JSONException e) {}
        return optimize;
    }

    /**
     * 插入文件隧道优化标记
     * @param values
     * @param extra2
     */
    public static void fixFileOptimize(ContentValues values,String extra2) {
        if (FeatureSwitch.enableFileOpt()) {
            try {
                JSONObject jsonObject = null;
                if (extra2 == null || extra2.isEmpty()) {
                    jsonObject = new JSONObject();
                } else {
                    jsonObject = new JSONObject(extra2);

                }
                jsonObject.put(KEY_FILE_OPTIMIZE, true);
                values.put(COLUMN_EXTRA2,jsonObject.toString());
            } catch (Exception  e) {}
        }
    }
    public static boolean getUseScdn(String jsonStr) {
        boolean useSdcn = false;
        if (TextUtils.isEmpty(jsonStr)) {
            return useSdcn;
        }
        try {
            JSONObject jsonObject = new JSONObject(jsonStr);
            useSdcn = jsonObject.getBoolean(KEY_USESCDN);
        } catch (Exception e) {
            useSdcn = false;
        }
        return useSdcn;
    }

    // MIUI ADD FOR KCG: START
    public static int getUseKcgEngine(String jsonStr) {
        if (TextUtils.isEmpty(jsonStr)) {
            return -1;
        }
        int UseKcgEngine = -1;
        try {
            JSONObject jsonObject = new JSONObject(jsonStr);
            boolean use = jsonObject.getBoolean(KEY_USE_KCGENGINE);
            UseKcgEngine = use ? 1 : 0;
        } catch (JSONException e) {
        }
        return UseKcgEngine;
    }
    // END

    /**
     * 对外提供无条件下载的接口
     * @param jsonStr
     * @return
     */
    public static boolean getBypassRecommendedSizeLimit(String jsonStr) {
        boolean pass = false;
        if (TextUtils.isEmpty(jsonStr)) {
            return pass;
        }
        try {
            JSONObject jsonObject = new JSONObject(jsonStr);
            Boolean passObj = jsonObject.getBoolean(KEY_BYPASS_RECOMMENDED_SIZE_LIMIT);
            pass = passObj == null ? false : passObj;
        } catch (Exception e) {
            pass = false;
        }
        return pass;
    }

    public static boolean getEnableM3u8(String jsonStr, String packageName) {
        //浏览器测必须传true才启用m3u8下载
        boolean def = TextUtils.equals("com.android.browser", packageName) ? false : true;
        return getBoolean(jsonStr, KEY_ENABLE_M3U8, def);
    }

    private static boolean getEnableM3u8(String jsonStr, boolean def) {
        return getBoolean(jsonStr, KEY_ENABLE_M3U8, def);
    }

    public static String getCompleteClass(String jsonStr) {
        return JSONUtils.getString(jsonStr, KEY_COMPLETE_CLASS, null);
    }

    private static boolean getBoolean(String json, String key, boolean def) {
        boolean value = def;
        if (TextUtils.isEmpty(json)) {
            return def;
        }
        try {
            JSONObject jsonObject = new JSONObject(json);
            Boolean obj = jsonObject.getBoolean(key);
            value = obj == null ? def : obj;
        } catch (Exception e) {
        }
        return value;
    }

    public static int getWakelockTime(String json) {
        if (TextUtils.isEmpty(json)) {
            return 0;
        }
        try {
            JSONObject jsonObject = new JSONObject(json);
            return jsonObject.getInt(KEY_WAKELOCK_TIME);
        } catch (Exception e) {
        }
        return 0;
    }
}
