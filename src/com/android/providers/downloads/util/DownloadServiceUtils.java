package com.android.providers.downloads.util;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.service.DownloadService;
import com.android.providers.downloads.statistics.Statistics;

/**
 * Created by lxy on 15/11/23.
 */
public class DownloadServiceUtils {

    public static void startService(Context context) {
        if (context == null) {
            return;
        }
        context.startService(buildIntentByCMD(context, DownloadService.CMD_DEF));
    }

    public static void notifyNetworkChange(Context context) {
        if (context == null) {
            return;
        }
        context.startService(buildIntentByCMD(context, DownloadService.CMD_NET_CHANGE));
    }

    public static void notifyEngineInitComplete(Context context) {
        if (context == null) {
            return;
        }
        context.startService(buildIntentByCMD(context, DownloadService.CMD_ENGINE_INIT));
    }

    private static Intent buildIntentByCMD(Context context, int cmdEngineInit) {
        Intent intent = new Intent(context, DownloadService.class);
        intent.putExtra("CMD_TYPE", cmdEngineInit);
        return intent;
    }
}
