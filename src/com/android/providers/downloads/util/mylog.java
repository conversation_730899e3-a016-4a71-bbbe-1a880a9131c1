
package com.android.providers.downloads.util;

import android.util.Log;

public class mylog {

    private static final int startIndex = 3;
    private static final int size = 1;
    private static boolean isInDebugMode = false;

    public static void setDebugMode(boolean debug) {
        isInDebugMode = debug;
    }

    public static String getInstanceName(Object instance) {
        return instance.getClass().getSimpleName() + Long.toHexString(instance.hashCode()) + " ";
    }

    protected static enum LogLevel {
        debug,
        info,
        warning,
        error,
    }

    public static void d(String TAG, String msg) {
        if (isInDebugMode) {
            internalPrint(TAG, msg, LogLevel.debug);
        }
    }

    public static void i(String TAG, String msg, int traceSize) {
        // 0 dalvik.system.VMStack.getThreadStackTrace(Native Method)
        // 1 java.lang.Thread.getStackTrace(Thread.java:591)
        // 2 com.lenovo.nova.util.debug.mylog.i(mylog.java:24)
        // 3
        // com.lenovo.nova.childrencontrol.Config.setCurrentUser(Config.java:82)
        // 4
        // com.lenovo.nova.childrencontrol.MainActivity.goToDesktop(MainActivity.java:69)
        internalPrint(TAG, "=========start=================", LogLevel.info);
        try {
            StackTraceElement[] elements = Thread.currentThread().getStackTrace();
            for (int i = 3; i < 3 + traceSize; i++) {
                if (i < elements.length) {
                    internalPrint(TAG, "" + elements[i], LogLevel.info);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
        }

        i(TAG, msg);
        internalPrint(TAG, "=========end=================", LogLevel.info);
    }

    public static void i(String TAG, String msg) {
        String fullMsg;
        StackTraceElement[] elements = Thread.currentThread().getStackTrace();
        fullMsg = getFullLogMsg(msg, elements, startIndex, size);
        internalPrint(TAG, fullMsg, LogLevel.info);
    }

    public static void w(String TAG, String msg) {
        String fullMsg;
        StackTraceElement[] elements = Thread.currentThread().getStackTrace();
        fullMsg = getFullLogMsg(msg, elements, startIndex, size);
        internalPrint(TAG, fullMsg, LogLevel.warning);
    }

    public static void e(String TAG, String msg) {
        String fullMsg;
        StackTraceElement[] elements = Thread.currentThread().getStackTrace();
        fullMsg = getFullLogMsg(msg, elements, startIndex, size);
        internalPrint(TAG, fullMsg, LogLevel.error);
    }

    protected static void internalPrint(String TAG, String msg, LogLevel logLevel) {
        msg = "scenic_log " + msg;
        switch (logLevel) {
            case debug:
                Log.d(TAG, msg);
                break;
            case info:
                Log.i(TAG, msg);
                break;
            case warning:
                Log.w(TAG, msg);
            case error:
                Log.e(TAG, msg);
                break;
        }

    }

    private static String getFullLogMsg(String msg, StackTraceElement[] elements, int startIndex,
                                        int size) {
        String fullMsg = "";
        try {
            final String split = ".";
            for (int i = startIndex; i < startIndex + size; i++) {
                StackTraceElement elem = elements[i];
                int lineNumber = elem.getLineNumber();
                String methodName = elem.getMethodName();
                String className = elem.getClassName();
                className = className.substring(className.lastIndexOf(".") + 1);

                fullMsg += className + split +
                        methodName + ":" +
                        lineNumber + ": " +
                        msg;

            }
        } catch (Exception e) {
            e.printStackTrace();
            return msg;
        }
        return fullMsg;
    }
}
