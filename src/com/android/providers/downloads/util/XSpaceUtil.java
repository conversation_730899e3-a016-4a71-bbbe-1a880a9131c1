package com.android.providers.downloads.util;

import android.content.Context;
import android.os.UserHandle;
import android.provider.MiuiSettings;
import android.util.Log;

import com.android.providers.downloads.config.XLDownloadCfg;

import java.io.File;
import java.io.IOException;

import miui.securityspace.XSpaceUserHandle;

import static com.android.providers.downloads.util.Helpers.containsCanonical;

public class XSpaceUtil implements XLDownloadCfg {
    private static final String XSPACE_USER_DIR = "/storage/emulated/" + XSpaceUserHandle.USER_XSPACE;

    public static boolean isValidInXspace(Context context, File root) {
        try {
            File canonicalFile = root.getCanonicalFile();
            // Support XSpace
            // In owner space and xspace is actived
            if (context.getUserId() == UserHandle.USER_OWNER
                    && MiuiSettings.Secure.getBoolean(context.getContentResolver(), MiuiSettings.Secure.XSPACE_ENABLED, false)) {
                if (containsCanonical(new File(XSPACE_USER_DIR), canonicalFile)) {
                    return true;
                }
            }
        } catch (IOException e) {
            Log.w(TAG, "Failed to check xspace: " + e);
        }
        return false;
    }
}
