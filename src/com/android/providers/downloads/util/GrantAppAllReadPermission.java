package com.android.providers.downloads.util;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.provider.Downloads;
import android.util.Log;

public class GrantAppAllReadPermission {

    private static String TAG = GrantAppAllReadPermission.class.getSimpleName();
    private static final String[] whiteListByPackageName = {
            "com.android.fileexplorer",
            "com.android.providers.downloads.ui"
    };
    private static final String ALL_DOWNLOADS_DOWNLOAD_BT_DETAIL = "all_downloads_download_bt_detail";

    public static void grantUriPermission(Context context) {
        Uri allDownloadsContentUri = Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI;
        int modeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION | Intent.FLAG_GRANT_WRITE_URI_PERMISSION;
        realGrantUriPermission(context, allDownloadsContentUri, modeFlags);
        Uri allBtDetailUri = Uri.parse(ALL_DOWNLOADS_DOWNLOAD_BT_DETAIL);
        realGrantUriPermission(context, allBtDetailUri, modeFlags);
    }

    private static void realGrantUriPermission(Context context, Uri uri, int modeFlags) {
        try {
            context.revokeUriPermission(uri, modeFlags);
            for (String packageName : whiteListByPackageName) {
                context.grantUriPermission(packageName, uri,
                        modeFlags);
            }
        } catch (Exception e) {
            Log.i(TAG, "grantUriPermission fail:" + uri);
        }
    }
}
