package com.android.providers.downloads.util;

import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Binder;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.text.format.DateFormat;
import android.util.Log;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLConfig;
import com.xunlei.downloadlib.XLDownloadManager;
import com.xunlei.downloadlib.parameter.XLConstant;

import java.io.PrintWriter;

/**
 * Created by lxy on 16/8/24.
 * 这个类已经废弃不用了
 */
public class LimitSpeedUtil {

    private final static long MIN_SURVIVAL_MILLIS = 2 * 1000;
    private final static long MIN_LIMIT_SPEED = 32 * 1024;//30k
    public static final String TAG = "LimitSpeed";
    private volatile static LimitSpeedUtil mInstance;
    private static final String PERMISSION_LIMIT_SPEED="xunlei.permission.EXTRA_LIMIT_SPEED";
    private Handler mHandler;
    private final int MSG_REMOVE_LIMIT_SPEED=3000;
    private LimitSpeedUtil() {
    }

    public static LimitSpeedUtil getInstance() {
        if (mInstance == null) {
            synchronized (DownloadSettings.class) {
                if (mInstance == null) {
                    mInstance = new LimitSpeedUtil();
                }
            }
        }
        return mInstance;
    }

    public void initLimitSpeed(final DownloadApplication app) {
        Looper looper = app.getWorkHandler().getLooper();
        mHandler = new Handler(looper) {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what) {
                    case MSG_REMOVE_LIMIT_SPEED:
                        removeLimitSpeed(app);
                        break;
                    default:
                        logD("no handle");
                        break;
                }
            }
        };
        long limitSpeed = DownloadSettings.XLSecureConfigSettings.getLimitSpeed();
        if (limitSpeed < 0) {
            return;
        }
        long limitTime = DownloadSettings.XLSecureConfigSettings.getLimitSpeedTime();
        long startlimitTime = DownloadSettings.XLSecureConfigSettings.getLimitSpeedStartTime();
        long lastLimitTime = startlimitTime + limitTime - System.currentTimeMillis();
        if (lastLimitTime > MIN_SURVIVAL_MILLIS) {
            _setLimitSpeed(app, limitSpeed, lastLimitTime);
        } else {
            removeLimitSpeed(app);
        }
    }

    private void removeLimitSpeed(DownloadApplication app) {
        _setLimitSpeed(app, -1);
    }

    private void _setLimitSpeed(DownloadApplication app, long limitSpeed, long survivalMillis) {
        try {
            setLimitSpeed(app, limitSpeed, survivalMillis);
        } catch (Exception e) {
            logD("exc", e);
        }
    }

    private void _setLimitSpeed(DownloadApplication app, long limitSpeed) {
        try {
            setLimitSpeed(app, limitSpeed);
        } catch (Exception e) {
            logD("exc", e);
        }
    }

    public boolean setLimitSpeed(final DownloadApplication app, long limitSpeed,
                                 long survivalMillis) {
        if (!isSupportLimitSpeed()) {
            return false;
        }
        checkPermission(app);
        if ((limitSpeed < 0 && limitSpeed != -1) || (limitSpeed > 0 && limitSpeed < MIN_LIMIT_SPEED)) {
            throw new IllegalArgumentException("limitSpeed must be greater than " + MIN_LIMIT_SPEED + " or equal to -1");
        }
        if (limitSpeed > 0 && survivalMillis < MIN_SURVIVAL_MILLIS) {
            throw new IllegalArgumentException("survivalMillis must be greater than " + MIN_SURVIVAL_MILLIS);
        }
        logD("args limitSpeed=" + limitSpeed);
        logD("args survivalMillis=" + survivalMillis);
        boolean result;
        if (limitSpeed == -1) {
            result = setLimitSpeed(app, -1);
        } else {
            result = setLimitSpeed(app, limitSpeed);
            mHandler.removeMessages(MSG_REMOVE_LIMIT_SPEED);
            mHandler.sendEmptyMessageDelayed(MSG_REMOVE_LIMIT_SPEED, survivalMillis);
            if (result) {
                saveLimitSpeedConfig(limitSpeed, survivalMillis);
            }
        }
        return result;
    }

    private void checkPermission(Context context) throws SecurityException {
        if (Binder.getCallingUid() != android.os.Process.myUid() && context.checkCallingPermission(PERMISSION_LIMIT_SPEED)
                != PackageManager.PERMISSION_GRANTED) {
            throw new SecurityException("limit speed not allowed, unless " + PERMISSION_LIMIT_SPEED + " is granted");
        }
        if (Binder.getCallingUid() != android.os.Process.myUid() && !canAccessLimitSpeed(context)) {
            throw new SecurityException("limit speed not allowed access");
        }
    }

    private boolean canAccessLimitSpeed(Context context) {
        String appName = ProcessUtil.getAppName(context, Binder.getCallingPid());
        if (TextUtils.equals(appName, "com.miui.securitycenter")) {
            return true;
        }
        if (TextUtils.equals(appName, "com.miui.securitycenter.remote")) {
            return true;
        }
        return false;
    }

    private void removeLimitSpeedConfig() {
        DownloadSettings.XLSecureConfigSettings.removeLimitSpeedConfig();
    }

    private void saveLimitSpeedConfig(long limitSpeed, long limitSpeedTime) {
        DownloadSettings.XLSecureConfigSettings.saveLimitSpeedConfig(limitSpeed, limitSpeedTime);
    }

    private boolean setLimitSpeed(DownloadApplication app, long limitSpeed) {
        if (!isSupportLimitSpeed()) {
            return false;
        }
        XLDownloadManager manager = app.getXlDownloadManager();
        if (manager == null) {
            logD("no init xunlei engine");
            return false;
        }
        int result = manager.setSpeedLimit(limitSpeed, -1);
        if (result != XLConstant.XLErrorCode.NO_ERROR) {
            String msg = manager.getErrorCodeMsg(result);
            logD(String.format("setSpeedLimit fail %s(%d)", msg, result));
        } else {
            String tipStr = limitSpeed == -1 ? "exit limit speed" : "limit speed";
            logD(String.format("%s(%s) success ", tipStr, String.valueOf(limitSpeed)));
            if (limitSpeed == -1) {
                removeLimitSpeedConfig();
            }
        }
        return result == XLConstant.XLErrorCode.NO_ERROR;
    }

    public boolean isLimitSpeedMode() {
        return DownloadSettings.XLSecureConfigSettings.getLimitSpeed() >= 0;
    }

    public static void dump(PrintWriter writer) {
        StringBuilder dump = new StringBuilder();
        dump.append("dump limitSpeed:\n");
        dump.append("limitSpeed=" + DownloadSettings.XLSecureConfigSettings.getLimitSpeed() + "B/s\n");
        toTimeStr(DownloadSettings.XLSecureConfigSettings.getLimitSpeedTime());
        long limitTime = DownloadSettings.XLSecureConfigSettings.getLimitSpeedTime();
        dump.append("limitSpeedTime=" + limitTime + "ms\n");
        long limitStartTime = DownloadSettings.XLSecureConfigSettings.getLimitSpeedStartTime();
        dump.append("limitSpeedStartTime=" + (limitStartTime > 0 ? toTimeStr(limitStartTime) :
                0) + "\n");
        writer.write(dump.toString());
    }

    /**
     *
     * @param limitSpeedTime
     * @return
     */
    private static String toTimeStr(long limitSpeedTime) {
        return String.format("%s(%s)", timeFormat(limitSpeedTime), String.valueOf(limitSpeedTime));

    }

    private static CharSequence timeFormat(long limitSpeedTime) {
        return DateFormat.format("yyyy-MM-dd HH:mm:ss", limitSpeedTime);
    }

    private boolean isSupportLimitSpeed() {
        boolean supprot = BuildUtils.isAlphaVersion() ? true : true;
        if (!supprot) {
            logD("not support limit speed");
        }
        return supprot;
    }

    private static void logD(String msg) {
        Log.d(TAG, msg);
    }

    private static void logD(String msg, Exception e) {
        Log.d(TAG, msg, e);
    }

}

