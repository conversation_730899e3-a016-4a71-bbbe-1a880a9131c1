package com.android.providers.downloads.util;

import android.app.DownloadManager;
import android.content.Context;
import android.database.ContentObserver;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.os.UserManager;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.utils.LogUtil;

import java.io.PrintWriter;
import java.util.HashMap;

import miui.provider.ExtraNetwork;

/**
 * Created by lxy on 16/4/13.
 */
public class NetworkUtils {

    private static final String TAG = "NetworkUtils";

    public static boolean checkIsNetworkTypeDisallowed(Context context, int allowedNetworkTypes, String pkg) {
        NetworkInfo networkType = Statistics.getActiveNetworkInfo(context);
        if (networkType == null) {
            return true;
        }
        int netType = networkType.getType();
        final int flag = translateNetworkTypeToApiFlag(netType);
        final boolean allowAllNetworkTypes = allowedNetworkTypes == ~0;
        //如果允许的网络类型和当前的网络类型不一致的情况下禁止联网
        if (!allowAllNetworkTypes && (flag & allowedNetworkTypes) == 0) {
            XLConfig.LOGD(TAG, "checkIsNetworkTypeAllowed  current netType flag=" + flag
                    + " allow netTypes is " + allowedNetworkTypes);
            return true;
        }
        String localPkg = context.getPackageName();
        //检查当前进程和指定的包名是否有联网权限
        return isNetDisallowed(context, localPkg, netType) |
                isNetDisallowed(context, pkg, netType);
    }

    /**
     * Translate a ConnectivityManager.TYPE_* constant to the corresponding
     * DownloadManager.Request.NETWORK_* bit flag.
     */
    private static int translateNetworkTypeToApiFlag(int networkType) {
        switch (networkType) {
            case ConnectivityManager.TYPE_MOBILE:
                return DownloadManager.Request.NETWORK_MOBILE;

            case ConnectivityManager.TYPE_WIFI:
            case ConnectivityManager.TYPE_ETHERNET:
                return DownloadManager.Request.NETWORK_WIFI;

            case ConnectivityManager.TYPE_BLUETOOTH:
                return DownloadManager.Request.NETWORK_BLUETOOTH;

            default:
                return -1;
        }
    }


    private static FirewallObserver mFirewallObserver;
    private static boolean mMoblileRuleChange = false;
    private static boolean mWifiRuleChange = false;
    private static Object mLock = new Object();

    /**
     * 是工作资料环境
     * @param context
     * @return
     */
    public static boolean isProfileEnv(Context context) {
        UserManager userManager = (UserManager) context.getSystemService(Context.USER_SERVICE);
        boolean isManagedProfile = userManager.isManagedProfile();
        LogUtil.d("profile_test", "" + isManagedProfile);
        return isManagedProfile;
    }

    public static void registerFirewallObserver(Context context, Handler handler) {
        mFirewallObserver = new FirewallObserver(handler);
        registerFirewallContentObserver(context, mFirewallObserver);
    }

    public static void unRegisterFirewallObserver(Context context) {
        unRegisterFirewallContentObserver(context, mFirewallObserver);
    }

    public static void registerFirewallContentObserver(Context context, ContentObserver observer) {
        if (isProfileEnv(context)) {
            return;
        }
        ExtraNetwork.registerFirewallContentObserver(context, observer);
    }

    public static void unRegisterFirewallContentObserver(Context context, ContentObserver observer) {
        if (isProfileEnv(context)) {
            return;
        }
        ExtraNetwork.unRegisterFirewallContentObserver(context, observer);
    }

    private static class FirewallObserver extends ContentObserver {
        public FirewallObserver(Handler handler) {
            super(handler);
        }

        @Override
        public void onChange(final boolean selfChange) {
            XLConfig.LOGD(TAG, "FirewallObserver selfChange=" + selfChange);
            mMoblileRuleChange = true;
            mWifiRuleChange = true;
        }
    }

    private static HashMap<String, Boolean> mWifiRule = new HashMap<String, Boolean>();
    private static HashMap<String, Boolean> mMobileRule = new HashMap<String, Boolean>();

    public static boolean isWifiDisallowed(Context context, String pkg) {
        boolean isDisallowed = false;
        if (mWifiRuleChange) {
            for (String key : mWifiRule.keySet()) {
                isDisallowed = ExtraNetwork.isWifiRestrict(context, key);
                mWifiRule.put(key, isDisallowed);
            }
            if (!mWifiRule.containsKey(pkg)) {
                isDisallowed = ExtraNetwork.isWifiRestrict(context, pkg);
                mWifiRule.put(pkg, isDisallowed);
            }
            mWifiRuleChange = false;
        } else {
            if (mWifiRule.containsKey(pkg)) {
                isDisallowed = mWifiRule.get(pkg);
            } else {
                isDisallowed = ExtraNetwork.isWifiRestrict(context, pkg);
                mWifiRule.put(pkg, isDisallowed);
            }
        }
        return isDisallowed;
    }

    public static boolean isMobileDisallowed(Context context, String pkg) {
        boolean isDisallowed = false;
        if (mMoblileRuleChange) {
            for (String key : mMobileRule.keySet()) {
                isDisallowed = ExtraNetwork.isMobileRestrict(context, key);
                mMobileRule.put(key, isDisallowed);
            }
            if (!mMobileRule.containsKey(pkg)) {
                isDisallowed = ExtraNetwork.isMobileRestrict(context, pkg);
                mMobileRule.put(pkg, isDisallowed);
            } else {
                isDisallowed = mMobileRule.get(pkg);
            }
            mMoblileRuleChange = false;
        } else {
            if (mMobileRule.containsKey(pkg)) {
                isDisallowed = mMobileRule.get(pkg);
            } else {
                isDisallowed = ExtraNetwork.isMobileRestrict(context, pkg);
                mMobileRule.put(pkg, isDisallowed);
            }
        }
        return isDisallowed;
    }

    /**
     * 检查当前网络下，当前包名是否被禁止联网
     *
     * @param context
     * @param pkg
     * @param netType
     * @return
     */
    public static boolean isNetDisallowed(Context context, String pkg, int netType) {
        boolean netDisallowed = false;
        try {
            synchronized (mLock) {
                switch (netType) {
                    case ConnectivityManager.TYPE_MOBILE:
                        netDisallowed = isMobileDisallowed(context, pkg);
                        break;
                    case ConnectivityManager.TYPE_WIFI:
                    case ConnectivityManager.TYPE_ETHERNET:
                        netDisallowed = isWifiDisallowed(context, pkg);
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return netDisallowed;
    }

    public static void dumpNetRule(PrintWriter writer) {
        StringBuilder dump = new StringBuilder();
        dump.append("dump dumpNetRule:\n");
        dump.append("dump mWifiRule==>" + mWifiRule.toString() + "\n");
        dump.append("dump mMobileRule==>" + mMobileRule.toString() + "\n");
        writer.write(dump.toString());
    }

    /**
     * 获取网络类型 0:无网络连接 2:2G网络 3:3G网络 4:4G网络 9:Wifi网络
     */
    public static int getNetworkType(Context context) {
        int networkType = 0;
        ConnectivityManager connectivityManager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
            if (networkInfo != null) {
                int type = networkInfo.getType();
                if (type == ConnectivityManager.TYPE_WIFI) {
                    networkType = 9;
                } else if (type == ConnectivityManager.TYPE_MOBILE) {
                    int subType = networkInfo.getSubtype();
                    switch (subType) {
                        case TelephonyManager.NETWORK_TYPE_GPRS:
                        case TelephonyManager.NETWORK_TYPE_CDMA:
                        case TelephonyManager.NETWORK_TYPE_EDGE:
                        case TelephonyManager.NETWORK_TYPE_1xRTT:
                        case TelephonyManager.NETWORK_TYPE_IDEN:
                        case 16://NETWORK_TYPE_GSM
                            networkType = 2;
                            break;
                        case TelephonyManager.NETWORK_TYPE_EVDO_A:
                        case TelephonyManager.NETWORK_TYPE_UMTS:
                        case TelephonyManager.NETWORK_TYPE_EVDO_0:
                        case TelephonyManager.NETWORK_TYPE_HSDPA:
                        case TelephonyManager.NETWORK_TYPE_HSUPA:
                        case TelephonyManager.NETWORK_TYPE_HSPA:
                        case TelephonyManager.NETWORK_TYPE_EVDO_B:
                        case TelephonyManager.NETWORK_TYPE_EHRPD:
                        case TelephonyManager.NETWORK_TYPE_HSPAP:
                        case 17://NETWORK_TYPE_TD_SCDMA
                            networkType = 3;
                            break;
                        case TelephonyManager.NETWORK_TYPE_LTE:
                        case 18://NETWORK_TYPE_IWLAN
                        case 19://NETWORK_TYPE_LTE_CA
                            networkType = 4;
                            break;
                        case 20://对应Q NETWORK_TYPE_NR
                            networkType = 5;
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        return networkType;
    }
}
