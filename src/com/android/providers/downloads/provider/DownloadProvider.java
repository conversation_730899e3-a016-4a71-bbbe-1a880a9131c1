/*
 * Copyright (C) 2007 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.providers.downloads.provider;

import static com.android.providers.downloads.util.Helpers.convertToMediaStoreDownloadsUri;
import static com.android.providers.downloads.util.Helpers.triggerMediaScan;

import android.annotation.NonNull;
import android.app.AppOpsManager;
import android.app.DownloadManager;
import android.app.DownloadManager.ExtraDownloads;

import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.MobileDataConfig;
import com.android.providers.downloads.helper.DownloadEngineRule;

import android.app.DownloadManager.Request;
import android.content.ContentProvider;
import android.content.ContentProviderClient;
import android.content.ContentProviderOperation;
import android.content.ContentProviderResult;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.OperationApplicationException;
import android.content.UriMatcher;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.database.Cursor;
import android.database.DatabaseUtils;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteOpenHelper;
import android.net.Uri;
import android.os.Binder;
import android.os.Bundle;
import android.os.Environment;
import android.os.ParcelFileDescriptor;
import android.os.ParcelFileDescriptor.OnCloseListener;
import android.os.Process;
import android.os.RemoteException;
import android.os.SELinux;
import android.provider.BaseColumns;
import android.provider.Downloads;
import android.provider.MediaStore;
import android.provider.OpenableColumns;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.RealSystemFacade;
import com.android.providers.downloads.StorageManager;
import com.android.providers.downloads.SystemFacade;
import com.android.providers.downloads.alarm.LimitSpeed;
import com.android.providers.downloads.config.Constants;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.service.DesktopProgressAppInfo;
import com.android.providers.downloads.service.DownloadInfo;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.statistics.DownloadItem;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.util.BTDatabaseHelper;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.DownloadExtra;
import com.android.providers.downloads.util.DownloadExtra2;
import com.android.providers.downloads.util.DownloadServiceUtils;
import com.android.providers.downloads.util.FileUtil;
import com.android.providers.downloads.util.GrantAppAllReadPermission;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.IndentingPrintWriter;
import com.android.providers.downloads.util.NetworkUtils;
import com.android.providers.downloads.util.ProcessUtil;
import com.android.providers.downloads.util.XLDownloadHelper;
import com.android.providers.downloads.util.XLUtil;
import com.android.providers.downloads.utils.LogUtil;
import com.android.providers.downloads.MediaScanTriggerJob;
import com.android.providers.downloads.kcg.KCGDownloadCfg;
import com.android.providers.downloads.kcg.utils.KCGLog;
import com.android.providers.downloads.kcg.utils.KcgHelper;
import com.google.android.collect.Maps;
import com.google.common.annotations.VisibleForTesting;

import java.io.File;
import java.io.FileDescriptor;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Array;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import com.android.internal.util.ArrayUtils;
import com.michael.corelib.coreutils.CustomThreadPool;

/**
 * Allows application to interact with the download manager.
 */
public final class DownloadProvider extends ContentProvider implements XLDownloadCfg, KCGDownloadCfg {
    /**
     * Database filename
     */
    private static final String DB_NAME = "downloads.db";
    /**
     * Current database version
     */
    private static final int DB_VERSION = 125;
    /**
     * Name of table in the database
     */
    public static final String DB_TABLE = "downloads";

    /**
     * MIME type for the entire download list
     */
    private static final String DOWNLOAD_LIST_TYPE = "vnd.android.cursor.dir/download";
    /**
     * MIME type for an individual download
     */
    private static final String DOWNLOAD_TYPE = "vnd.android.cursor.item/download";

    /**
     * URI matcher used to recognize URIs sent by applications
     */
    private static final UriMatcher sURIMatcher = new UriMatcher(UriMatcher.NO_MATCH);
    /**
     * URI matcher constant for the URI of all downloads belonging to the
     * calling UID
     */
    private static final int MY_DOWNLOADS = 1;
    /**
     * URI matcher constant for the URI of an individual download belonging to
     * the calling UID
     */
    private static final int MY_DOWNLOADS_ID = 2;
    /**
     * URI matcher constant for the URI of all downloads in the system
     */
    private static final int ALL_DOWNLOADS = 3;
    /**
     * URI matcher constant for the URI of an individual download
     */
    private static final int ALL_DOWNLOADS_ID = 4;
    /**
     * URI matcher constant for the URI of a download's request headers
     */
    private static final int REQUEST_HEADERS_URI = 5;
    /**
     * URI matcher constant for the URI of setting for download bytes limit over
     * mobile
     */
    private static final int XL_DOWNLOAD_BYTES_LIMIT_OVER_MOBILE = 7;
    private static final int DOWNLOAD_BT_DETAIL = 8;

    static {
        sURIMatcher.addURI("downloads", "my_downloads", MY_DOWNLOADS);
        sURIMatcher.addURI("downloads", "my_downloads/#", MY_DOWNLOADS_ID);
        sURIMatcher.addURI("downloads", "all_downloads", ALL_DOWNLOADS);
        sURIMatcher.addURI("downloads", "all_downloads/#", ALL_DOWNLOADS_ID);
        sURIMatcher.addURI("downloads",
                "my_downloads/#/" + Downloads.Impl.RequestHeaders.URI_SEGMENT,
                REQUEST_HEADERS_URI);
        sURIMatcher.addURI("downloads",
                "all_downloads/#/" + Downloads.Impl.RequestHeaders.URI_SEGMENT,
                REQUEST_HEADERS_URI);
        // temporary, for backwards compatibility
        sURIMatcher.addURI("downloads", "download", MY_DOWNLOADS);
        sURIMatcher.addURI("downloads", "download/#", MY_DOWNLOADS_ID);
        sURIMatcher.addURI("downloads",
                "download/#/" + Downloads.Impl.RequestHeaders.URI_SEGMENT,
                REQUEST_HEADERS_URI);
        sURIMatcher.addURI("downloads", "xl_download_bytes_limit_over_mobile",
                XL_DOWNLOAD_BYTES_LIMIT_OVER_MOBILE);
        sURIMatcher.addURI("downloads", "all_downloads_download_bt_detail",
                DOWNLOAD_BT_DETAIL);

    }

    /**
     * Different base URIs that could be used to access an individual download
     */
    private static final Uri[] BASE_URIS = new Uri[] {
            Downloads.Impl.CONTENT_URI,
            Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI,
    };

    public static final String COLUMN_FILE_HASH = "download_file_hash";
    public static final String COLUMN_APK_INSTALL_WAY = "download_apk_install_way";
    public static final String COLUMN_EXTRA = "download_extra";
    public static final String COLUMN_APK_PACKGENAME = "apk_package_name";
    public static final String COLUMN_FLAGS = "flags" ;//from android n

    private static final String[] sAppReadableColumnsArray = new String[] {
            Downloads.Impl._ID,
            Downloads.Impl.COLUMN_APP_DATA,
            Downloads.Impl._DATA,
            Downloads.Impl.COLUMN_MIME_TYPE,
            Downloads.Impl.COLUMN_VISIBILITY,
            Downloads.Impl.COLUMN_DESTINATION,
            Downloads.Impl.COLUMN_CONTROL,
            Downloads.Impl.COLUMN_STATUS,
            Downloads.Impl.COLUMN_LAST_MODIFICATION,
            Downloads.Impl.COLUMN_NOTIFICATION_PACKAGE,
            Downloads.Impl.COLUMN_NOTIFICATION_CLASS,
            Downloads.Impl.COLUMN_NOTIFICATION_EXTRAS,
            Downloads.Impl.COLUMN_TOTAL_BYTES,
            Downloads.Impl.COLUMN_CURRENT_BYTES,
            Downloads.Impl.COLUMN_TITLE,
            Downloads.Impl.COLUMN_DESCRIPTION,
            Downloads.Impl.COLUMN_URI,
            Downloads.Impl.COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI,
            Downloads.Impl.COLUMN_FILE_NAME_HINT,
            Downloads.Impl.COLUMN_MEDIAPROVIDER_URI,
            Downloads.Impl.COLUMN_DELETED,
            Downloads.Impl.COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT,
            Downloads.Impl.COLUMN_ALLOWED_NETWORK_TYPES,
            Downloads.Impl.COLUMN_NO_INTEGRITY,
            Downloads.Impl.COLUMN_FAILED_CONNECTIONS,
            Downloads.Impl.COLUMN_COOKIE_DATA,
            Downloads.Impl.COLUMN_USER_AGENT,
            Downloads.Impl.COLUMN_REFERER,
            Downloads.Impl.COLUMN_OTHER_UID,
            ExtraDownloads.COLUMN_IF_RANGE_ID,
            OpenableColumns.DISPLAY_NAME,
            OpenableColumns.SIZE,
            COLUMN_FILE_HASH,
            COLUMN_APK_INSTALL_WAY,
            COLUMN_EXTRA,
            COLUMN_APK_PACKGENAME,
            Downloads.Impl.COLUMN_ERROR_MSG,
            COLUMN_TRYSPEEDUP_TIME,
            COLUMN_TRYSPEEDUP_STATUS,
            COLUMN_TRYSPEEDUP_MODE,
            COLUMN_EXTRA2,
            COLUMN_PERCENT,
            COLUMN_DOWNLOAD_TYPE,
            Downloads.Impl.COLUMN_MEDIASTORE_URI,
            Constants.ETAG,
            Constants.UID,
            KCGDownloadCfg.COLUMN_KCG_TASK_OPEN_MARK,
            COLUMN_FILE_SIZE
    };

    private static final HashSet<String> sAppReadableColumnsSet;
    private static final HashMap<String, String> sColumnsMap;

    static {
        sAppReadableColumnsSet = new HashSet<String>();
        for (int i = 0; i < sAppReadableColumnsArray.length; ++i) {
            sAppReadableColumnsSet.add(sAppReadableColumnsArray[i]);
        }

        sColumnsMap = Maps.newHashMap();
        sColumnsMap.put(OpenableColumns.DISPLAY_NAME,
                Downloads.Impl.COLUMN_TITLE + " AS " + OpenableColumns.DISPLAY_NAME);
        sColumnsMap.put(OpenableColumns.SIZE,
                Downloads.Impl.COLUMN_TOTAL_BYTES + " AS " + OpenableColumns.SIZE);
    }

    private static final List<String> downloadManagerColumnsList =
            Arrays.asList(DownloadManager.MIUI_UNDERLYING_COLUMNS);
    public static final String TAG = "DownloadProvider";

    /**
     * The database that lies underneath this content provider
     */
    private SQLiteOpenHelper mOpenHelper = null;

    /**
     * List of uids that can access the downloads
     */
    private int mSystemUid = -1;
    private int mDefContainerUid = -1;
    private File mDownloadsDataDir;

    @VisibleForTesting
    SystemFacade mSystemFacade;
    private AppOpsManager mAppOpsManager;

    public static final class XLColumns {
        private XLColumns(){}
        /**
         * add four column by xl hsh for xl vip sevice
         */
        /**
         * This file create time
         */
        public static final String COLUMN_FILE_CREATE_TIME = "file_create_time";
        /**
         * This file downloading current speed
         */
        public static final String COLUMN_DOWNLOADING_CURRENT_SPEED = "downloading_current_speed";
        /**
         * This file download surplus time
         */
        public static final String COLUMN_DOWNLOAD_SURPLUS_TIME = "download_surplus_time";
        /**
         * This file download accelerate data
         */
        public static final String COLUMN_XL_ACCELERATE_SPEED = "xl_accelerate_speed";
        /**
         * This file download use time
         */
        public static final String COLUMN_DOWNLOADED_TIME = "downloaded_time";
        /**
         * This xl vip status
         */
        public static final String COLUMN_XL_VIP_STATUS = "xl_vip_status";
        /**
         * This xl vip accelerate cdn url
         */
        public static final String COLUMN_XL_VIP_CDN_URL = "xl_vip_cdn_url";
        /**
         * This xl sdk open mark
         */
        public static final String COLUMN_XL_TASK_OPEN_MARK = "xl_task_open_mark";
        /**
         * This task for Thumbnail
         */
        public static final String COLUMN_TASK_FOR_THUMBNAIL = "download_task_thumbnail";
    }

    /**
     * This class encapsulates a SQL where clause and its parameters. It makes
     * it possible for shared methods (like
     * {@link DownloadProvider#getWhereClause(Uri, String, String[], int)}) to
     * return both pieces of information, and provides some utility logic to
     * ease piece-by-piece construction of selections.
     */
    private static class SqlSelection {
        public StringBuilder mWhereClause = new StringBuilder();
        public List<String> mParameters = new ArrayList<String>();

        public <T> void appendClause(String newClause, final T... parameters) {
            if (newClause == null || newClause.isEmpty()) {
                return;
            }
            if (mWhereClause.length() != 0) {
                mWhereClause.append(" AND ");
            }
            mWhereClause.append("(");
            mWhereClause.append(newClause);
            mWhereClause.append(")");
            if (parameters != null) {
                for (Object parameter : parameters) {
                    mParameters.add(parameter != null ? parameter.toString() : "");
                }
            }
        }

        public String getSelection() {
            return mWhereClause.toString();
        }

        public String[] getParameters() {
            String[] array = new String[mParameters.size()];
            return mParameters.toArray(array);
        }
    }

    /**
     * Creates and updated database on demand when opening it. Helper class to
     * create database the first time the provider is initialized and upgrade it
     * when a new version of the provider needs an updated version of the
     * database.
     */
    private final class DatabaseHelper extends SQLiteOpenHelper {

        public DatabaseHelper(final Context context) {
            super(context, DB_NAME, null, DB_VERSION);
        }

        /**
         * Creates database the first time we try to open it.
         */
        @Override
        public void onCreate(final SQLiteDatabase db) {
            XLConfig.LOGD("populating new database", Statistics.getStackTraceMsg());
            onUpgrade(db, 0, DB_VERSION);
        }

        @Override
        public void onDowngrade(SQLiteDatabase db, int oldV, int newV) {
            XLConfig.LOGD(TAG, "db downgrade");
            deleteDownloadsTable(db);
        }

        /**
         * Updates the database format when a content provider is used with a
         * database that was created with a different format. Note: to support
         * downgrades, creating a table should always drop it first if it
         * already exists.
         */
        @Override
        public void onUpgrade(final SQLiteDatabase db, int oldV, final int newV) {
            int vNo = oldV;
            if (oldV == 31) {
                // 31 and 100 are identical, just in different codelines.
                // Upgrading from 31 is the
                // same as upgrading from 100.
                vNo = 100;
            } else if (oldV < 100) {
                // no logic to upgrade from these older version, just recreate
                // the DB
                XLConfig.LOGD(TAG, "Upgrading downloads database from version " + oldV
                        + " to version " + newV + ", which will destroy all old data");
                vNo = 99;
            } else if (oldV == 119) {
                //增加COLUMN_PERCENT字段，防止跳过119版本更新
                vNo = 118;
            } else if (oldV == 122) {
                //增加COLUMN_PERCENT字段，防止跳过122版本更新
                vNo = 121;
            } else if (oldV > newV) {
                // user must have downgraded software; we have no way to know
                // how to downgrade the
                // DB, so just recreate it
                XLConfig.LOGD(TAG, "Downgrading downloads database from version " + oldV
                        + " (current version is " + newV + "), destroying all old data");
                vNo = 99;
            }

            for (int version = vNo + 1; version <= newV; version++) {
                upgradeTo(db, version);
            }
        }

        /**
         * Upgrade database from (version - 1) to version.
         */
        private void upgradeTo(SQLiteDatabase db, int version) {
            switch (version) {
                case 100:
                    createDownloadsTable(db);
                    break;

                case 101:
                    createHeadersTable(db);
                    break;

                case 102:
                    addColumn(db, DB_TABLE, Downloads.Impl.COLUMN_IS_PUBLIC_API,
                            "INTEGER NOT NULL DEFAULT 0");
                    addColumn(db, DB_TABLE, Downloads.Impl.COLUMN_ALLOW_ROAMING,
                            "INTEGER NOT NULL DEFAULT 0");
                    addColumn(db, DB_TABLE, Downloads.Impl.COLUMN_ALLOWED_NETWORK_TYPES,
                            "INTEGER NOT NULL DEFAULT 0");
                    break;

                case 103:
                    addColumn(db, DB_TABLE, Downloads.Impl.COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI,
                            "INTEGER NOT NULL DEFAULT 1");
                    makeCacheDownloadsInvisible(db);
                    break;

                case 104:
                    addColumn(db, DB_TABLE, Downloads.Impl.COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT,
                            "INTEGER NOT NULL DEFAULT 0");
                    break;

                case 105:
                    fillNullValues(db);
                    break;

                case 106:
                    addColumn(db, DB_TABLE, Downloads.Impl.COLUMN_MEDIAPROVIDER_URI, "TEXT");
                    addColumn(db, DB_TABLE, Downloads.Impl.COLUMN_DELETED,
                            "BOOLEAN NOT NULL DEFAULT 0");
                    break;

                case 107:
                    addColumn(db, DB_TABLE, Downloads.Impl.COLUMN_ERROR_MSG, "TEXT");
                    break;

                case 108:
                    addColumn(db, DB_TABLE, ExtraDownloads.COLUMN_IF_RANGE_ID, "TEXT");
                    break;

                case 109:
                    addColumn(db, DB_TABLE, Downloads.Impl.COLUMN_ALLOW_METERED,
                            "INTEGER NOT NULL DEFAULT 1");
                    break;

                case 110:
                    try {
                        addColumn(db, DB_TABLE, Downloads.Impl.COLUMN_ALLOW_WRITE,
                                "BOOLEAN NOT NULL DEFAULT 0");
                    } catch (Exception e) {
                        XLConfig.LOGD("exc", e);
                    }
                    break;

                case 111:
                    addColumn(db, DB_TABLE, XLColumns.COLUMN_FILE_CREATE_TIME,
                            "INTEGER NOT NULL DEFAULT 0");
                    addColumn(db, DB_TABLE, XLColumns.COLUMN_DOWNLOADING_CURRENT_SPEED,
                            "INTEGER NOT NULL DEFAULT 0");
                    addColumn(db, DB_TABLE, XLColumns.COLUMN_DOWNLOAD_SURPLUS_TIME,
                            "INTEGER NOT NULL DEFAULT 0");
                    addColumn(db, DB_TABLE, XLColumns.COLUMN_XL_ACCELERATE_SPEED,
                            "INTEGER NOT NULL DEFAULT 0");
                    addColumn(db, DB_TABLE, XLColumns.COLUMN_DOWNLOADED_TIME,
                            "INTEGER NOT NULL DEFAULT 0");
                    addColumn(db, DB_TABLE, XLColumns.COLUMN_XL_VIP_STATUS,
                            "INTEGER NOT NULL DEFAULT 0");
                    addColumn(db, DB_TABLE, XLColumns.COLUMN_XL_VIP_CDN_URL, "TEXT");
                    addColumn(db, DB_TABLE, XLColumns.COLUMN_XL_TASK_OPEN_MARK,
                            "INTEGER NOT NULL DEFAULT 0");
                    addColumn(db, DB_TABLE, XLColumns.COLUMN_TASK_FOR_THUMBNAIL, "TEXT");
                    break;
                case 112:
                    addColumn(db, DB_TABLE, ExtraDownloads.COLUMN_APK_PACKGENAME, "TEXT");
                    break;
                case 113:
                    addColumn(db, DB_TABLE, TORRENT_FILE_INFOS_HASH, "TEXT");
                    addColumn(db, DB_TABLE, TORRENT_FILE_COUNT, "INTEGER NOT NULL DEFAULT 0");
                    BTDatabaseHelper.createBTTable(db);
                    break;
                case 114:
                    addColumn(db, DB_TABLE, COLUMN_DOWNLOAD_TYPE, "INTEGER NOT NULL DEFAULT -1");
                    break;
                case 115:
                    addColumn(db, DB_TABLE, COLUMN_FILE_HASH, "TEXT");
                    addColumn(db, DB_TABLE, COLUMN_EXTRA, "TEXT");
                    addColumn(db, DB_TABLE, COLUMN_APK_INSTALL_WAY, "INTEGER NOT NULL DEFAULT -1");
                    break;
                case 116:
                    addColumn(db, DB_TABLE, COLUMN_TRYSPEEDUP_TIME, "TEXT");
                    addColumn(db, DB_TABLE, COLUMN_TRYSPEEDUP_STATUS, "INTEGER NOT NULL DEFAULT -1");
                    addColumn(db, DB_TABLE, COLUMN_TRYSPEEDUP_MODE, "INTEGER NOT NULL DEFAULT -1");
                    break;
                case 117:
                    addColumn(db, DB_TABLE, COLUMN_FLAGS, "INTEGER NOT NULL DEFAULT 0");
                    break;
                case 118:
                    addColumn(db, DB_TABLE, COLUMN_EXTRA2, "TEXT");
                    break;
                case 119:
                    addColumnNoError(db, DB_TABLE, Downloads.Impl.COLUMN_MEDIASTORE_URI,
                            "TEXT DEFAULT NULL");
                    addMediaStoreUris();
                    break;

                case 120:
                    updateMediaStoreUrisFromFilesToDownloads(db);
                    break;

                case 121:
                    canonicalizeDataPaths(db);
                    break;
                case 122:
                    nullifyMediaStoreUris(db);
                    MediaScanTriggerJob.schedule(getContext());
                    break;
                case 123:
                // MIUI ADD FOR KCG: START
                    addColumnNoError(db, DB_TABLE, COLUMN_KCG_TASK_OPEN,
                            "INTEGER NOT NULL DEFAULT 0");
                    break;
                // END
                case 124:
                    addColumnNoError(db, DB_TABLE, COLUMN_PERCENT,
                            "INTEGER NOT NULL DEFAULT 0");
                    break;
                case 125:
                    addColumnNoError(db, DB_TABLE, COLUMN_FILE_SIZE,
                            "INTEGER NOT NULL DEFAULT -1");
                    break;
                default:
                    throw new IllegalStateException("Don't know how to upgrade to " + version);
            }
        }

        /**
         * insert() now ensures these four columns are never null for new
         * downloads, so this method makes that true for existing columns, so
         * that code can rely on this assumption.
         */
        private void fillNullValues(SQLiteDatabase db) {
            ContentValues values = new ContentValues();
            values.put(Downloads.Impl.COLUMN_CURRENT_BYTES, 0);
            fillNullValuesForColumn(db, values);
            values.put(Downloads.Impl.COLUMN_TOTAL_BYTES, -1);
            fillNullValuesForColumn(db, values);
            values.put(Downloads.Impl.COLUMN_TITLE, "");
            fillNullValuesForColumn(db, values);
            values.put(Downloads.Impl.COLUMN_DESCRIPTION, "");
            fillNullValuesForColumn(db, values);
        }

        private void fillNullValuesForColumn(SQLiteDatabase db, ContentValues values) {
            String column = values.valueSet().iterator().next().getKey();
            db.update(DB_TABLE, values, column + " is null", null);
            values.clear();
        }

        /**
         * Set all existing downloads to the cache partition to be invisible in
         * the downloads UI.
         */
        private void makeCacheDownloadsInvisible(SQLiteDatabase db) {
            ContentValues values = new ContentValues();
            values.put(Downloads.Impl.COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI, false);
            String cacheSelection = Downloads.Impl.COLUMN_DESTINATION
                    + " != " + Downloads.Impl.DESTINATION_EXTERNAL;
            db.update(DB_TABLE, values, cacheSelection, null);
        }

        /**
         * Add {@link Downloads.Impl#COLUMN_MEDIASTORE_URI} for all successful downloads and
         * add/update corresponding entries in MediaProvider.
         */
        private void addMediaStoreUris() {
            CustomThreadPool.asyncWork(new Runnable() {
                @Override
                public void run() {
                    final String[] selectionArgs = new String[] {
                            Integer.toString(Downloads.Impl.DESTINATION_EXTERNAL),
                            Integer.toString(Downloads.Impl.DESTINATION_FILE_URI),
                            Integer.toString(Downloads.Impl.DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD),
                    };
                    final long token = Binder.clearCallingIdentity();
                    SQLiteDatabase db = getWritableDatabase();
                    try (Cursor cursor = db.query(DB_TABLE, null,
                            "_data IS NOT NULL AND is_visible_in_downloads_ui != '0'"
                                    + " AND (destination=? OR destination=? OR destination=?)",
                            selectionArgs, null, null, null);
                         ContentProviderClient client = getContext().getContentResolver()
                                 .acquireContentProviderClient(MediaStore.AUTHORITY)) {
                        if (cursor.getCount() == 0) {
                            return;
                        }
                        final DownloadInfo.Reader reader
                                = new DownloadInfo.Reader(getContext().getContentResolver(), cursor);
                        final DownloadInfo info = new DownloadInfo(getContext());
                        final ContentValues updateValues = new ContentValues();
                        while (cursor.moveToNext()) {
                            reader.updateFromDatabase(getContext().getContentResolver(),info);
                            final ContentValues mediaValues;
                            try {
                                mediaValues = convertToMediaProviderValues(info);
                            } catch (IllegalArgumentException e) {
                                Log.e(Constants.TAG, "Error getting media content values from " + info, e);
                                continue;
                            }
                            final Uri mediaStoreUri = updateMediaProvider(client, mediaValues);
                            if (mediaStoreUri != null) {
                                updateValues.clear();
                                updateValues.put(Downloads.Impl.COLUMN_MEDIASTORE_URI,
                                        mediaStoreUri.toString());
                                db.update(DB_TABLE, updateValues, Downloads.Impl._ID + "=?",
                                        new String[] { Long.toString(info.mId) });
                            }
                        }
                    } finally {
                        Binder.restoreCallingIdentity(token);
                    }
                }
            });
        }

        /**
         * DownloadProvider has been updated to use MediaStore.Downloads based uris
         * for COLUMN_MEDIASTORE_URI but the existing entries would still have MediaStore.Files
         * based uris. It's possible that in the future we might incorrectly assume that all the
         * uris are MediaStore.DownloadColumns based and end up querying some
         * MediaStore.Downloads specific columns. To avoid this, update the existing entries to
         * use MediaStore.Downloads based uris only.
         */
        private void updateMediaStoreUrisFromFilesToDownloads(SQLiteDatabase db) {
            try (Cursor cursor = db.query(DB_TABLE,
                    new String[] { Downloads.Impl._ID, COLUMN_MEDIASTORE_URI },
                    COLUMN_MEDIASTORE_URI + " IS NOT NULL", null, null, null, null)) {
                final ContentValues updateValues = new ContentValues();
                while (cursor.moveToNext()) {
                    final long id = cursor.getLong(0);
                    final Uri mediaStoreFilesUri = Uri.parse(cursor.getString(1));

                    final long mediaStoreId = ContentUris.parseId(mediaStoreFilesUri);
                    final String volumeName = MediaStore.getVolumeName(mediaStoreFilesUri);
                    final Uri mediaStoreDownloadsUri
                            = MediaStore.Downloads.getContentUri(volumeName, mediaStoreId);

                    updateValues.clear();
                    updateValues.put(COLUMN_MEDIASTORE_URI, mediaStoreDownloadsUri.toString());
                    db.update(DB_TABLE, updateValues, Downloads.Impl._ID + "=?",
                            new String[] { Long.toString(id) });
                }
            }
        }

        private void canonicalizeDataPaths(SQLiteDatabase db) {
            try (Cursor cursor = db.query(DB_TABLE,
                    new String[] { Downloads.Impl._ID, Downloads.Impl._DATA},
                    Downloads.Impl._DATA + " IS NOT NULL", null, null, null, null)) {
                final ContentValues updateValues = new ContentValues();
                while (cursor.moveToNext()) {
                    final long id = cursor.getLong(0);
                    final String filePath = cursor.getString(1);
                    final String canonicalPath;
                    try {
                        canonicalPath = new File(filePath).getCanonicalPath();
                    } catch (IOException e) {
                        Log.e(Constants.TAG, "Found invalid path='" + filePath + "' for id=" + id);
                        continue;
                    }

                    updateValues.clear();
                    updateValues.put(Downloads.Impl._DATA, canonicalPath);
                    db.update(DB_TABLE, updateValues, Downloads.Impl._ID + "=?",
                            new String[] { Long.toString(id) });
                }
            }
        }

        /**
         * Set mediastore uri column to null before the clean-up job and fill it again while
         * running the job so that if the clean-up job gets preempted, we could use it
         * as a way to know the entries which are already handled when the job gets restarted.
         */
        private void nullifyMediaStoreUris(SQLiteDatabase db) {
            final String whereClause = Downloads.Impl._DATA + " IS NOT NULL"
                    + " AND (" + COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI + "=1"
                    + " OR " + COLUMN_MEDIA_SCANNED + "=" + MEDIA_SCANNED + ")"
                    + " AND (" + COLUMN_DESTINATION + "=" + Downloads.Impl.DESTINATION_EXTERNAL
                    + " OR " + COLUMN_DESTINATION + "=" + DESTINATION_FILE_URI
                    + " OR " + COLUMN_DESTINATION + "=" + DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD
                    + ")";
            final ContentValues values = new ContentValues();
            values.putNull(COLUMN_MEDIASTORE_URI);
            db.update(DB_TABLE, values, whereClause, null);
        }

        /**
         * Add a column to a table using ALTER TABLE.
         *
         * @param dbTable name of the table
         * @param columnName name of the column to add
         * @param columnDefinition SQL for the column definition
         */
        private void addColumn(SQLiteDatabase db, String dbTable, String columnName,
                String columnDefinition) {
            db.execSQL("ALTER TABLE " + dbTable + " ADD COLUMN " + columnName + " "
                    + columnDefinition);
        }

        private void addColumnNoError(SQLiteDatabase db, String dbTable, String columnName,
                                      String columnDefinition) {
            try {
                db.execSQL("ALTER TABLE " + dbTable + " ADD COLUMN " + columnName + " "
                        + columnDefinition);
            } catch (Throwable e) {
                Log.w(Constants.TAG, "update", e);
            }
        }

        /**
         * Creates the table that'll hold the download information.
         */
        private void createDownloadsTable(SQLiteDatabase db) {
            try {
                db.execSQL("DROP TABLE IF EXISTS " + DB_TABLE);
                db.execSQL("CREATE TABLE " + DB_TABLE + "(" +
                        Downloads.Impl._ID + " INTEGER PRIMARY KEY AUTOINCREMENT," +
                        Downloads.Impl.COLUMN_URI + " TEXT, " +
                        Constants.RETRY_AFTER_X_REDIRECT_COUNT + " INTEGER, " +
                        Downloads.Impl.COLUMN_APP_DATA + " TEXT, " +
                        Downloads.Impl.COLUMN_NO_INTEGRITY + " BOOLEAN, " +
                        Downloads.Impl.COLUMN_FILE_NAME_HINT + " TEXT, " +
                        Constants.OTA_UPDATE + " BOOLEAN, " +
                        Downloads.Impl._DATA + " TEXT, " +
                        Downloads.Impl.COLUMN_MIME_TYPE + " TEXT, " +
                        Downloads.Impl.COLUMN_DESTINATION + " INTEGER, " +
                        Constants.NO_SYSTEM_FILES + " BOOLEAN, " +
                        Downloads.Impl.COLUMN_VISIBILITY + " INTEGER, " +
                        Downloads.Impl.COLUMN_CONTROL + " INTEGER, " +
                        Downloads.Impl.COLUMN_STATUS + " INTEGER, " +
                        Downloads.Impl.COLUMN_FAILED_CONNECTIONS + " INTEGER, " +
                        Downloads.Impl.COLUMN_LAST_MODIFICATION + " BIGINT, " +
                        Downloads.Impl.COLUMN_NOTIFICATION_PACKAGE + " TEXT, " +
                        Downloads.Impl.COLUMN_NOTIFICATION_CLASS + " TEXT, " +
                        Downloads.Impl.COLUMN_NOTIFICATION_EXTRAS + " TEXT, " +
                        Downloads.Impl.COLUMN_COOKIE_DATA + " TEXT, " +
                        Downloads.Impl.COLUMN_USER_AGENT + " TEXT, " +
                        Downloads.Impl.COLUMN_REFERER + " TEXT, " +
                        Downloads.Impl.COLUMN_TOTAL_BYTES + " INTEGER, " +
                        Downloads.Impl.COLUMN_CURRENT_BYTES + " INTEGER, " +
                        Constants.ETAG + " TEXT, " +
                        Constants.UID + " INTEGER, " +
                        Downloads.Impl.COLUMN_OTHER_UID + " INTEGER, " +
                        Downloads.Impl.COLUMN_TITLE + " TEXT, " +
                        Downloads.Impl.COLUMN_DESCRIPTION + " TEXT, " +
                        COLUMN_MEDIA_SCANNED + " BOOLEAN);");
            } catch (SQLException ex) {
                XLConfig.LOGD(TAG, "couldn't create table in downloads database", ex);
                throw ex;
            }
        }

        public void deleteDownloadsTable(SQLiteDatabase db) {
            try {
                if (db != null) {
                    db.execSQL("DROP TABLE IF EXISTS " + DB_TABLE);
                    db.execSQL("DROP TABLE IF EXISTS "
                            + Downloads.Impl.RequestHeaders.HEADERS_DB_TABLE);
                    onUpgrade(db, 0, DB_VERSION);
                }
            } catch (SQLException e) {
                XLConfig.LOGD(TAG, "couldn't delete table in downloads database", e);
            }
        }

        private void createHeadersTable(SQLiteDatabase db) {
            db.execSQL("DROP TABLE IF EXISTS " + Downloads.Impl.RequestHeaders.HEADERS_DB_TABLE);
            db.execSQL("CREATE TABLE " + Downloads.Impl.RequestHeaders.HEADERS_DB_TABLE + "(" +
                    "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                    Downloads.Impl.RequestHeaders.COLUMN_DOWNLOAD_ID + " INTEGER NOT NULL," +
                    Downloads.Impl.RequestHeaders.COLUMN_HEADER + " TEXT NOT NULL," +
                    Downloads.Impl.RequestHeaders.COLUMN_VALUE + " TEXT NOT NULL" +
                    ");");
        }

    }

    /**
     * Initializes the content provider when it is created.
     */
    @Override
    public boolean onCreate() {
        DownloadApplication.setProviderContext(getContext());
        GrantAppAllReadPermission.grantUriPermission(getContext());
        if(!KcgHelper.getInstance().isKCGEngineInit()) {
            KcgHelper.getInstance().initKCGEngine(DownloadApplication.getGlobalApplication());
            KCGLog.LogI(TAG, "onCreate initKCGEngine");
        }
        LogUtil.forceD(TAG, "onCreate " + DownloadApplication.getGlobalApplication());
        if (mSystemFacade == null) {
            mSystemFacade = new RealSystemFacade(getContext());
        }

        initDatabaseHelper();
        // Initialize the system uid
        mSystemUid = Process.SYSTEM_UID;
        // Initialize the default container uid. Package name hardcoded
        // for now.
        ApplicationInfo appInfo = null;
        try {
            appInfo = getContext().getPackageManager().
                    getApplicationInfo("com.android.defcontainer", 0);
        } catch (NameNotFoundException e) {
            LogUtil.forceD(TAG, "Could not get ApplicationInfo for com.android.defconatiner", e);
        }
        if (appInfo != null) {
            mDefContainerUid = appInfo.uid;
        }

        mAppOpsManager = getContext().getSystemService(AppOpsManager.class);

        // Grant access permissions for all known downloads to the owning apps
        final SQLiteDatabase db = mOpenHelper.getReadableDatabase();
        final Cursor cursor = db.query(DB_TABLE, new String[] {
                Downloads.Impl._ID, Constants.UID }, null, null, null, null, null);
        final ArrayList<Long> idsToDelete = new ArrayList<Long>();
        try {
            while (cursor.moveToNext()) {
                final long downloadId = cursor.getLong(0);
                final int uid = cursor.getInt(1);
                final String ownerPackage = getPackageForUid(uid);
                if (ownerPackage == null) {
                   idsToDelete.add(downloadId);
                } else {
                    grantAllDownloadsPermission(ownerPackage, downloadId);
                }
            }
        } finally {
            cursor.close();
        }
        if (idsToDelete.size() > 0) {
            Log.i(Constants.TAG,
                    "Deleting downloads with ids " + idsToDelete + " as owner package is missing");
            CustomThreadPool.asyncWork(new Runnable() {
                @Override
                public void run() {
                    deleteDownloadsWithIds(idsToDelete);
                }
            });
        }

        // start the DownloadService class. don't wait for the 1st download to
        // be issued.
        // saves us by getting some initialization code in DownloadService out
        // of the way.
        DownloadServiceUtils.startService(getContext());
        mDownloadsDataDir = StorageManager.getDownloadDataDirectory(getContext());
        try {
            SELinux.restorecon(mDownloadsDataDir.getCanonicalPath());
        } catch (IOException e) {
            LogUtil.forceD(TAG, "Could not get canonical path for download directory", e);
        }
        return true;
    }

    private void initDatabaseHelper() {
        SQLiteDatabase db = null;
        try {
            mOpenHelper = new DatabaseHelper(getContext());
            db = mOpenHelper.getWritableDatabase();
            LogUtil.forceD(TAG, "initDatabaseHelper");
        } catch (Exception exc) {
            LogUtil.forceD(TAG, "initDatabaseHelper Init database helper error", exc);
            File file = getContext().getDatabasePath(DB_NAME);
            if (file != null && file.exists()) {
                boolean result = file.delete();
                if (!result) {
                    LogUtil.forceD(TAG, String.format("delete %s file fail", file.getPath()));
                }
            }
        } finally {
            if (mOpenHelper == null) {
                mOpenHelper = new DatabaseHelper(getContext());
            }
        }
    }

    private void deleteDownloadsWithIds(ArrayList<Long> downloadIds) {
        final int N = downloadIds.size();
        if (N == 0) {
            return;
        }
        final StringBuilder queryBuilder = new StringBuilder(Downloads.Impl._ID + " in (");
        for (int i = 0; i < N; i++) {
            queryBuilder.append(downloadIds.get(i));
            queryBuilder.append((i == N - 1) ? ")" : ",");
        }
        delete(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, queryBuilder.toString(), null);
    }

    /**
     * Returns the content-provider-style MIME types of the various types
     * accessible through this content provider.
     */
    @Override
    public String getType(final Uri uri) {
        int match = sURIMatcher.match(uri);
        XLConfig.LOGD(TAG, "uri=" + uri + " getType" + Statistics.getStackTraceMsg());
        switch (match) {
            case MY_DOWNLOADS:
            case DOWNLOAD_BT_DETAIL:
            case ALL_DOWNLOADS:
                return DOWNLOAD_LIST_TYPE;
            case MY_DOWNLOADS_ID:
            case ALL_DOWNLOADS_ID:
                // return the mimetype of this id from the database
                final String id = getDownloadIdFromUri(uri);
                final SQLiteDatabase db = mOpenHelper.getReadableDatabase();
                final String mimeType = DatabaseUtils.stringForQuery(db,
                        "SELECT " + Downloads.Impl.COLUMN_MIME_TYPE + " FROM " + DB_TABLE +
                                " WHERE " + Downloads.Impl._ID + " = ?",
                        new String[]{
                                id
                        });
                if (TextUtils.isEmpty(mimeType)) {
                    return DOWNLOAD_TYPE;
                } else {
                    return mimeType;
                }
            default:
                XLConfig.LOGD(TAG, "calling getType on an unknown URI: " + uri);
                throw new IllegalArgumentException("Unknown URI: " + uri);

        }
    }

    @Override
    public Bundle call(String method, String arg, Bundle extras) {
        switch (method) {
            case Downloads.CALL_MEDIASTORE_DOWNLOADS_DELETED: {
                getContext().enforceCallingOrSelfPermission(
                        android.Manifest.permission.WRITE_MEDIA_STORAGE, Constants.TAG);
                final long[] deletedDownloadIds = extras.getLongArray(Downloads.EXTRA_IDS);
                final String[] mimeTypes = extras.getStringArray(Downloads.EXTRA_MIME_TYPES);
                DownloadStorageProvider.onMediaProviderDownloadsDelete(getContext(),
                        deletedDownloadIds, mimeTypes);
                return null;
            }
            case Downloads.CALL_CREATE_EXTERNAL_PUBLIC_DIR: {
                final String dirType = extras.getString(Downloads.DIR_TYPE);
                if (!ArrayUtils.contains(Environment.STANDARD_DIRECTORIES, dirType)) {
                    throw new IllegalStateException("Not one of standard directories: " + dirType);
                }
                final File file = Environment.getExternalStoragePublicDirectory(dirType);
                if (file.exists()) {
                    if (!file.isDirectory()) {
                        throw new IllegalStateException(file.getAbsolutePath() +
                                " already exists and is not a directory");
                    }
                } else if (!file.mkdirs()) {
                    throw new IllegalStateException("Unable to create directory: " +
                            file.getAbsolutePath());
                }
                return null;
            }
            case Downloads.CALL_REVOKE_MEDIASTORE_URI_PERMS : {
                getContext().enforceCallingOrSelfPermission(
                        android.Manifest.permission.WRITE_MEDIA_STORAGE, Constants.TAG);
                DownloadStorageProvider.revokeAllMediaStoreUriPermissions(getContext());
                return null;
            }
            default:
                throw new UnsupportedOperationException("Unsupported call: " + method);
        }
    }

    /**
     * Inserts a row in the database
     */
    @Override
    public Uri insert(final Uri uri, final ContentValues values) {
        LogUtil.d("insert " + DownloadApplication.getGlobalApplication());
        long start = System.currentTimeMillis();
        printProcessInfo("insert");
        Statistics.trackDownloadProviderCall(getContext(), "insert");
        checkInsertPermissions(values);
        DownloadSettings.XLSecureConfigSettings.setUsedFirstDownload();
        SQLiteDatabase db = mOpenHelper.getWritableDatabase();
        Context context = getContext();

        // note we disallow inserting into ALL_DOWNLOADS
        int match = sURIMatcher.match(uri);
        if (match == DOWNLOAD_BT_DETAIL) {
            long id = insertItem(db, values);
            Uri uriToNotify = ContentUris.withAppendedId(uri, id);
            getContext().getContentResolver().notifyChange(uri, null,NOTIFY_NO_DELAY);
            return uriToNotify;
        } else if (match != MY_DOWNLOADS) {
            XLConfig.LOGD(TAG, "calling insert on an unknown/invalid URI: " + uri);
            throw new IllegalArgumentException("Unknown/Invalid URI " + uri);
        }
        // if an identical task already exists, do not insert again
        long existId = checkDownloadTaskExist(db, uri, values);
        XLConfig.LOGD("install in insert url=" + uri.toString()
                + ", existsId=" + existId
                + " values=" + (values != null ? values.toString() : "null"));
        if (existId > 0) {
            return ContentUris.withAppendedId(Downloads.Impl.CONTENT_URI, existId);
        }
        ensureDefaultColumns(values);

        checkInstallPermissions(values);
        // copy some of the input values as it
        ContentValues filteredValues = new ContentValues();
        copyString(Downloads.Impl.COLUMN_URI, values, filteredValues);
        String tempUrl = values.getAsString(Downloads.Impl.COLUMN_URI);
        if (!values.containsKey(COLUMN_DOWNLOAD_TYPE)) {
            String pkgName = values.getAsString(Downloads.Impl.COLUMN_NOTIFICATION_PACKAGE);
            String extra2 = values.getAsString(COLUMN_EXTRA2);
            DownloadType downloadType = XLDownloadHelper.getDownloadType(tempUrl, extra2, pkgName);
            values.put(COLUMN_DOWNLOAD_TYPE, downloadType.ordinal());
        }
        copyString(COLUMN_DOWNLOAD_TYPE, values, filteredValues);
        copyString(Downloads.Impl.COLUMN_APP_DATA, values, filteredValues);
        copyBoolean(Downloads.Impl.COLUMN_NO_INTEGRITY, values, filteredValues);
        copyString(Downloads.Impl.COLUMN_FILE_NAME_HINT, values, filteredValues);
        copyString(Downloads.Impl.COLUMN_MIME_TYPE, values, filteredValues);
        copyBoolean(Downloads.Impl.COLUMN_IS_PUBLIC_API, values, filteredValues);

        boolean isPublicApi =
                values.getAsBoolean(Downloads.Impl.COLUMN_IS_PUBLIC_API) == Boolean.TRUE;

        // validate the destination column
        Integer dest = values.getAsInteger(Downloads.Impl.COLUMN_DESTINATION);
        if (dest != null) {
            if (getContext().checkCallingOrSelfPermission(Downloads.Impl.PERMISSION_ACCESS_ADVANCED)
                    != PackageManager.PERMISSION_GRANTED
                    && (dest == Downloads.Impl.DESTINATION_CACHE_PARTITION
                            || dest == Downloads.Impl.DESTINATION_CACHE_PARTITION_NOROAMING
                            || dest == Downloads.Impl.DESTINATION_SYSTEMCACHE_PARTITION)) {
                throw new SecurityException("setting destination to : " + dest +
                        " not allowed, unless PERMISSION_ACCESS_ADVANCED is granted");
            }
            // for public API behavior, if an app has CACHE_NON_PURGEABLE permission, automatically
            // switch to non-purgeable download
            boolean hasNonPurgeablePermission =
                    getContext().checkCallingOrSelfPermission(
                            Downloads.Impl.PERMISSION_CACHE_NON_PURGEABLE)
                            == PackageManager.PERMISSION_GRANTED;
            if (isPublicApi && dest == Downloads.Impl.DESTINATION_CACHE_PARTITION_PURGEABLE
                    && hasNonPurgeablePermission) {
                dest = Downloads.Impl.DESTINATION_CACHE_PARTITION;
            }
            if (dest == Downloads.Impl.DESTINATION_FILE_URI) {
                checkFileUriDestination(values);
            } else if (dest == DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD) {
                checkDownloadedFilePath(values);
            } else if (dest == Downloads.Impl.DESTINATION_EXTERNAL) {
                getContext().enforceCallingOrSelfPermission(
                        android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        "No permission to write");

                final AppOpsManager appOps = getContext().getSystemService(AppOpsManager.class);
                if (mAppOpsManager.noteProxyOp(AppOpsManager.OP_WRITE_EXTERNAL_STORAGE,
                        getCallingPackage(), Binder.getCallingUid(), getCallingAttributionTag(),
                        null) != AppOpsManager.MODE_ALLOWED) {
                    throw new SecurityException("No permission to write");
                }
            } else if (dest == Downloads.Impl.DESTINATION_SYSTEMCACHE_PARTITION) {
                getContext().enforcePermission(
                        android.Manifest.permission.ACCESS_CACHE_FILESYSTEM,
                        Binder.getCallingPid(), Binder.getCallingUid(),
                        "need ACCESS_CACHE_FILESYSTEM permission to use system cache");
            }
            filteredValues.put(Downloads.Impl.COLUMN_DESTINATION, dest);
        }

        // validate the visibility column
        Integer vis = values.getAsInteger(Downloads.Impl.COLUMN_VISIBILITY);
        if (vis == null) {
            if (dest == Downloads.Impl.DESTINATION_EXTERNAL) {
                filteredValues.put(Downloads.Impl.COLUMN_VISIBILITY,
                        Downloads.Impl.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
            } else {
                filteredValues.put(Downloads.Impl.COLUMN_VISIBILITY,
                        Downloads.Impl.VISIBILITY_HIDDEN);
            }
        } else {
            filteredValues.put(Downloads.Impl.COLUMN_VISIBILITY, vis);
        }

        // copy the control column as is
        copyInteger(Downloads.Impl.COLUMN_CONTROL, values, filteredValues);

        boolean needCheckLimitDialog = false;
        long fileSize = -1;
        /*
         * requests coming from DownloadManager.addCompletedDownload(String,
         * String, String, boolean, String, String, long) need special treatment
         */
        if (values.getAsInteger(Downloads.Impl.COLUMN_DESTINATION) == Downloads.Impl.DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD) {
            // these requests always are marked as 'completed'
            filteredValues.put(Downloads.Impl.COLUMN_STATUS, Downloads.Impl.STATUS_SUCCESS);
            filteredValues.put(Downloads.Impl.COLUMN_CURRENT_BYTES, 0);
            copyInteger(Downloads.Impl.COLUMN_MEDIA_SCANNED, values, filteredValues);
            copyString(Downloads.Impl._DATA, values, filteredValues);
            copyBoolean(Downloads.Impl.COLUMN_ALLOW_WRITE, values, filteredValues);
            copyLong(Downloads.Impl.COLUMN_TOTAL_BYTES, values, filteredValues);
        } else {
            copyLong(Downloads.Impl.COLUMN_TOTAL_BYTES, values, filteredValues);
            Long fileLongSize = filteredValues.getAsLong(Downloads.Impl.COLUMN_TOTAL_BYTES);
            fileSize = fileLongSize != null ? fileLongSize.longValue() : -1;
            filteredValues.put(Downloads.Impl.COLUMN_TOTAL_BYTES, fileSize);
            filteredValues.put(COLUMN_FILE_SIZE, fileSize);
            needCheckLimitDialog = true;
            filteredValues.put(Downloads.Impl.COLUMN_CURRENT_BYTES, 0);
        }

        // set lastupdate to current time
        long lastMod = mSystemFacade.currentTimeMillis();
        filteredValues.put(Downloads.Impl.COLUMN_LAST_MODIFICATION, lastMod);
        if (Helpers.isCmTestBuilder()) {
            filteredValues.put(ExtraDownloads.COLUMN_DOWNLOADED_TIME, lastMod);
        }

        // use packagename of the caller to set the notification columns
        String pckg = values.getAsString(Downloads.Impl.COLUMN_NOTIFICATION_PACKAGE);
        String clazz = values.getAsString(Downloads.Impl.COLUMN_NOTIFICATION_CLASS);
        if (pckg != null && (clazz != null || isPublicApi)) {
            int uid = Binder.getCallingUid();
            try {
                if (uid == 0 || mSystemFacade.userOwnsPackage(uid, pckg)
                        || miui.securityspace.XSpaceUserHandle.isUidBelongtoXSpace(uid)) {
                    filteredValues.put(Downloads.Impl.COLUMN_NOTIFICATION_PACKAGE, pckg);
                    if (clazz != null) {
                        filteredValues.put(Downloads.Impl.COLUMN_NOTIFICATION_CLASS, clazz);
                    }
                }
            } catch (NameNotFoundException ex) {
                XLConfig.LOGD("exc", ex);
            }
        }else{
            String[] pkgsUid = context.getPackageManager().getPackagesForUid(Binder.getCallingUid());
            if (pkgsUid != null && pkgsUid.length > 0) {
                pckg = pkgsUid[0];
                filteredValues.put(Downloads.Impl.COLUMN_NOTIFICATION_PACKAGE, pckg);
            }
        }

        if (isShowTaskWithUiAndNotification(pckg)) {
            filteredValues.put(Downloads.Impl.COLUMN_VISIBILITY,
                    Downloads.Impl.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
        }
        // copy some more columns as is
        copyString(Downloads.Impl.COLUMN_NOTIFICATION_EXTRAS, values, filteredValues);
        copyString(Downloads.Impl.COLUMN_COOKIE_DATA, values, filteredValues);
        copyString(Downloads.Impl.COLUMN_USER_AGENT, values, filteredValues);
        copyString(Downloads.Impl.COLUMN_REFERER, values, filteredValues);

        // UID, PID columns
        if (getContext().checkCallingOrSelfPermission(Downloads.Impl.PERMISSION_ACCESS_ADVANCED)
                == PackageManager.PERMISSION_GRANTED) {
            copyInteger(Downloads.Impl.COLUMN_OTHER_UID, values, filteredValues);
        }
        filteredValues.put(Constants.UID, Binder.getCallingUid());
        if (Binder.getCallingUid() == 0) {
            copyInteger(Constants.UID, values, filteredValues);
        }

        // copy some more columns as is
        copyStringWithDefault(Downloads.Impl.COLUMN_TITLE, values, filteredValues, "");
        copyStringWithDefault(Downloads.Impl.COLUMN_DESCRIPTION, values, filteredValues, "");
        copyStringWithDefault(ExtraDownloads.COLUMN_IF_RANGE_ID, values, filteredValues, "");

        // is_visible_in_downloads_ui column
        if (values.containsKey(Downloads.Impl.COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI)) {
            copyBoolean(Downloads.Impl.COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI, values, filteredValues);
        } else {
            // by default, make external downloads visible in the UI
            boolean isExternal = dest == null || dest == Downloads.Impl.DESTINATION_EXTERNAL;
            filteredValues.put(Downloads.Impl.COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI, isExternal);
        }

        if (isShowTaskWithUiAndNotification(pckg)) {
            filteredValues.put(Downloads.Impl.COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI, true);
        }

        // public api requests and networktypes/roaming columns
        if (isPublicApi) {
            copyInteger(Downloads.Impl.COLUMN_ALLOWED_NETWORK_TYPES, values, filteredValues);
            copyBoolean(Downloads.Impl.COLUMN_ALLOW_ROAMING, values, filteredValues);
            copyBoolean(Downloads.Impl.COLUMN_ALLOW_METERED, values, filteredValues);
            copyInteger(COLUMN_FLAGS, values, filteredValues);
        }

        final Integer mediaScanned = values.getAsInteger(Downloads.Impl.COLUMN_MEDIA_SCANNED);
        filteredValues.put(COLUMN_MEDIA_SCANNED,
                (mediaScanned == null ? MEDIA_NOT_SCANNED : mediaScanned));

        final boolean shouldBeVisibleToUser
                = filteredValues.getAsInteger(COLUMN_MEDIA_SCANNED) == MEDIA_NOT_SCANNED;
        if (shouldBeVisibleToUser && filteredValues.getAsInteger(COLUMN_DESTINATION)
                == DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD) {
            final CallingIdentity token = clearCallingIdentity();
            try {
                final Uri mediaStoreUri = MediaStore.scanFile(getContext().getContentResolver(),
                        new File(filteredValues.getAsString(Downloads.Impl._DATA)));
                if (mediaStoreUri != null) {
                    final ContentValues mediaValues = new ContentValues();
                    mediaValues.put(MediaStore.Downloads.DOWNLOAD_URI,
                            filteredValues.getAsString(Downloads.Impl.COLUMN_URI));
                    mediaValues.put(MediaStore.Downloads.REFERER_URI,
                            filteredValues.getAsString(Downloads.Impl.COLUMN_REFERER));
                    mediaValues.put(MediaStore.Downloads.OWNER_PACKAGE_NAME,
                            Helpers.getPackageForUid(getContext(),
                                    filteredValues.getAsInteger(Constants.UID)));
                    getContext().getContentResolver().update(
                            convertToMediaStoreDownloadsUri(mediaStoreUri),
                            mediaValues, null, null);

                    filteredValues.put(Downloads.Impl.COLUMN_MEDIASTORE_URI,
                            mediaStoreUri.toString());
                    filteredValues.put(Downloads.Impl.COLUMN_MEDIAPROVIDER_URI,
                            mediaStoreUri.toString());
                    filteredValues.put(COLUMN_MEDIA_SCANNED, MEDIA_SCANNED);
                }
            } finally {
                restoreCallingIdentity(token);
            }
        }

        //如果没有设置，则走默认值允许所有网络下载
        if (!filteredValues.containsKey(Downloads.Impl.COLUMN_ALLOWED_NETWORK_TYPES)) {
            filteredValues.put(Downloads.Impl.COLUMN_ALLOWED_NETWORK_TYPES, ~0);
        }
        // Set whether use xunlei engine mask.
        boolean isRunningForeground = Helpers.isRunningForeground(context, pckg);

        // MIUI ADD FOR KCG: START
        String kcgExtra2 = values.getAsString(COLUMN_EXTRA2);
        boolean isKcgEngineOn = useKcgEngine(kcgExtra2);
        filteredValues.put(KCGDownloadCfg.COLUMN_KCG_TASK_OPEN_MARK, isKcgEngineOn);
        // END

        boolean isXunleiEngineOn = useXunleiEngine(pckg,values.getAsString(COLUMN_EXTRA2));
        filteredValues.put(DownloadManager.ExtraDownloads.COLUMN_XL_TASK_OPEN_MARK, isXunleiEngineOn);
        String extra2 = values.getAsString(COLUMN_EXTRA2);
        if (DownloadExtra2.getBypassRecommendedSizeLimit(extra2)) {
            filteredValues.put(Downloads.Impl.COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT,true);
            needCheckLimitDialog = false;
        }
        copyString(COLUMN_TASK_FOR_THUMBNAIL, values, filteredValues);
        copyString(ExtraDownloads.COLUMN_APK_PACKGENAME, values, filteredValues);
        copyString(TORRENT_FILE_INFOS_HASH, values, filteredValues);
        copyString(TORRENT_FILE_COUNT, values, filteredValues);
        copyInteger(COLUMN_APK_INSTALL_WAY, values, filteredValues);
        copyString(COLUMN_FILE_HASH, values, filteredValues);
        copyString(COLUMN_EXTRA, values, filteredValues);
        DownloadExtra2.fixFileOptimize(values,extra2);
        copyString(COLUMN_EXTRA2, values, filteredValues);

        boolean queuedForWifi = false;
        if (needCheckLimitDialog) {
            int allowedNetType = filteredValues.getAsInteger(COLUMN_ALLOWED_NETWORK_TYPES);
            String extraStr = filteredValues.getAsString(COLUMN_EXTRA);
            String refPkgName = DownloadExtra.getRefererPkgName(extraStr);
            if (needShowLimitDialog(pckg, refPkgName, fileSize, allowedNetType)) {
                filteredValues.put(Downloads.Impl.COLUMN_STATUS,
                        Downloads.Impl.STATUS_QUEUED_FOR_WIFI);
                queuedForWifi = true;
            } else {
                filteredValues.put(Downloads.Impl.COLUMN_STATUS, Downloads.Impl.STATUS_PENDING);
            }
        } else {
            //需要下载的任务才修改状态
            if (values.getAsInteger(Downloads.Impl.COLUMN_DESTINATION) != Downloads.Impl.DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD) {
                filteredValues.put(Downloads.Impl.COLUMN_STATUS, Downloads.Impl.STATUS_PENDING);
            }
        }

        XLConfig.LOGD(TAG, "in insert filteredValues: " + filteredValues.toString());

        long rowID = db.insert(DB_TABLE, null, filteredValues);
        long end = System.currentTimeMillis();
        Statistics.printCallTime("insert", start, end, rowID);
        Statistics.reportUseSetExtra2(getContext(),rowID,tempUrl,pckg,extra2,isXunleiEngineOn);
        if (rowID == -1) {
            XLConfig.LOGD(TAG, "couldn't insert into downloads database");
            return null;
        }

        if (queuedForWifi) {
            if (XLDownloadHelper.isBtTask(values.getAsString(Downloads.Impl.COLUMN_URI))) {
                //为了规避移动网络下插入bt任务出现黑屏的情况
                DownLoadProviderUtils.notifyPauseDueToSize(context, rowID, false, 300);
            } else {
                DownLoadProviderUtils.notifyPauseDueToSize(context, rowID, false);
            }
        }

        insertRequestHeaders(db, rowID, values);
        final String callingPackage = getPackageForUid(Binder.getCallingUid());
        if (callingPackage == null) {
            Log.e(Constants.TAG, "Package does not exist for calling uid");
            return null;
        }
        grantAllDownloadsPermission(callingPackage, rowID);
        notifyContentChanged(uri, match);

        // Always start service to handle notifications and/or scanning
        DownloadServiceUtils.startService(context);

        return ContentUris.withAppendedId(Downloads.Impl.CONTENT_URI, rowID);
    }

    private String FLAG_CURRENTBYTES = "flag_currentbytes";
    /**
     * If an entry corresponding to given mediaValues doesn't already exist in MediaProvider,
     * add it, otherwise update that entry with the given values.
     */
    private Uri updateMediaProvider(@NonNull ContentProviderClient mediaProvider,
                                    @NonNull ContentValues mediaValues) {
        final String filePath = mediaValues.getAsString(MediaStore.DownloadColumns.DATA);
        Uri mediaStoreUri = getMediaStoreUri(mediaProvider, filePath);

        try {

            if (mediaStoreUri == null) {
                long currentBytes = mediaValues.getAsLong(FLAG_CURRENTBYTES);
                if (currentBytes > 0) {
                    Uri uri = Helpers.getContentUriForPath(getContext(), filePath);
                    mediaValues.remove(FLAG_CURRENTBYTES);
                    mediaStoreUri = mediaProvider.insert(uri, mediaValues);
                    if (mediaStoreUri == null) {
                        Log.e(Constants.TAG, "Error inserting into mediaProvider: " + mediaValues);
                    }
                    return mediaStoreUri;
                }
            } else {
                mediaValues.remove(FLAG_CURRENTBYTES);
                Bundle extras = new Bundle();
                //给媒体库传 不允许移动标记
                extras.putBoolean("android:query-arg-allow-movement",false);
                if (mediaProvider.update(mediaStoreUri, mediaValues, extras) != 1) {
                    Log.e(Constants.TAG, "Error updating MediaProvider, uri: " + mediaStoreUri
                            + ", values: " + mediaValues);
                }
                return mediaStoreUri;
            }
        } catch (Exception e) {
            Log.w(Constants.TAG, "Error updateMediaProvider, uri: " + mediaStoreUri
                    + ", values: " + mediaValues, e);
        }
        return null;
    }

    private Uri getMediaStoreUri(@NonNull ContentProviderClient mediaProvider,
                                 @NonNull String filePath) {
        final Uri filesUri = MediaStore.setIncludePending(
                Helpers.getContentUriForPath(getContext(), filePath));
        try (Cursor cursor = mediaProvider.query(filesUri,
                new String[] { MediaStore.Files.FileColumns._ID },
                MediaStore.Files.FileColumns.DATA + "=?", new String[] { filePath }, null, null)) {
            if (cursor.moveToNext()) {
                return ContentUris.withAppendedId(filesUri, cursor.getLong(0));
            }
        } catch (RemoteException e) {
            // Should not happen
        }
        return null;
    }

    private ContentValues convertToMediaProviderValues(DownloadInfo info) {
        final String filePath;
        try {
            filePath = new File(info.mFileName).getCanonicalPath();
        } catch (IOException e) {
            throw new IllegalArgumentException(e);
        }
        final boolean downloadCompleted = Downloads.Impl.isStatusCompleted(info.mStatus);
        final ContentValues mediaValues = new ContentValues();
        mediaValues.put(MediaStore.Downloads.DATA,  filePath);
        mediaValues.put(MediaStore.Downloads.VOLUME_NAME, Helpers.extractVolumeName(filePath));
        mediaValues.put(MediaStore.Downloads.RELATIVE_PATH, Helpers.extractRelativePath(filePath));
        mediaValues.put(MediaStore.Downloads.DISPLAY_NAME, Helpers.extractDisplayName(filePath));
        mediaValues.put(MediaStore.Downloads.SIZE,
                downloadCompleted ? info.mTotalBytes : info.mCurrentBytes);
        mediaValues.put(FLAG_CURRENTBYTES, info.mCurrentBytes);
        mediaValues.put(MediaStore.Downloads.DOWNLOAD_URI, info.mUri);
        mediaValues.put(MediaStore.Downloads.REFERER_URI, info.mReferer);
        mediaValues.put(MediaStore.Downloads.MIME_TYPE, info.mMimeType);
        mediaValues.put(MediaStore.Downloads.IS_PENDING, downloadCompleted ? 0 : 1);
        mediaValues.put(MediaStore.Downloads.OWNER_PACKAGE_NAME,
                Helpers.getPackageForUid(getContext(), info.mUid));
        mediaValues.put(MediaStore.Files.FileColumns.IS_DOWNLOAD, info.mIsVisibleInDownloadsUi);
        return mediaValues;
    }

    private static Uri getFileUri(String uriString) {
        final Uri uri = Uri.parse(uriString);
        return TextUtils.equals(uri.getScheme(), ContentResolver.SCHEME_FILE) ? uri : null;
    }

    private void ensureDefaultColumns(ContentValues values) {
        final Integer dest = values.getAsInteger(COLUMN_DESTINATION);
        if (dest != null) {
            final int mediaScannable;
            if (dest == Downloads.Impl.DESTINATION_EXTERNAL) {
                mediaScannable = MEDIA_NOT_SCANNED;
            } else if (dest != DESTINATION_FILE_URI
                    && dest != DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD) {
                mediaScannable = MEDIA_NOT_SCANNABLE;
            } else {
                final File file;
                if (dest == Downloads.Impl.DESTINATION_FILE_URI) {
                    final String fileUri = values.getAsString(Downloads.Impl.COLUMN_FILE_NAME_HINT);
                    file = new File(getFileUri(fileUri).getPath());
                } else {
                    file = new File(values.getAsString(Downloads.Impl._DATA));
                }

                if (Helpers.isFileInExternalAndroidDirs(file.getAbsolutePath())) {
                    mediaScannable = MEDIA_NOT_SCANNABLE;
                } else if (Helpers.isFilenameValidInPublicDownloadsDir(file)) {
                    mediaScannable = MEDIA_NOT_SCANNED;
                } else {
                    mediaScannable = MEDIA_NOT_SCANNED;
                }
            }
            values.put(COLUMN_MEDIA_SCANNED, mediaScannable);
        }
        if (!values.containsKey(COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI)) {
            values.put(COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI, true);
        }

    }

    /**
     * Check that the file URI provided for DESTINATION_FILE_URI is valid.
     */
    private void checkFileUriDestination(ContentValues values) {
        String fileUri = values.getAsString(Downloads.Impl.COLUMN_FILE_NAME_HINT);
        if (fileUri == null) {
            throw new IllegalArgumentException(
                    "DESTINATION_FILE_URI must include a file URI under COLUMN_FILE_NAME_HINT");
        }
        Uri uri = Uri.parse(fileUri);
        String scheme = uri.getScheme();
        if (scheme == null || !"file".equals(scheme)) {
            throw new IllegalArgumentException("Not a file URI: " + uri);
        }
        final String path = uri.getPath();
        if (path == null || ("/" + path + "/").contains("/../")) {
            throw new IllegalArgumentException("Invalid file URI: " + uri);
        }

        final File file;
        try {
            file = new File(path).getCanonicalFile();
            values.put(Downloads.Impl.COLUMN_FILE_NAME_HINT, Uri.fromFile(file).toString());
        } catch (IOException e) {
            throw new SecurityException(e);
        }

        final boolean isLegacyMode = mAppOpsManager.checkOp(AppOpsManager.OP_LEGACY_STORAGE,
                Binder.getCallingUid(), getCallingPackage()) == AppOpsManager.MODE_ALLOWED;
        Helpers.checkDestinationFilePathRestrictions(file, getCallingPackage(), getContext(),
                mAppOpsManager, getCallingAttributionTag(), isLegacyMode,
                /* allowDownloadsDirOnly */ false);
    }

    private void checkDownloadedFilePath(ContentValues values) {
        final String path = values.getAsString(Downloads.Impl._DATA);
        if (path == null || ("/" + path + "/").contains("/../")) {
            throw new IllegalArgumentException("Invalid file path: "
                    + (path == null ? "null" : path));
        }

        final File file;
        try {
            file = new File(path).getCanonicalFile();
            values.put(Downloads.Impl._DATA, file.getPath());
        } catch (IOException e) {
            throw new SecurityException(e);
        }

        if (!file.exists()) {
            throw new IllegalArgumentException("File doesn't exist: " + file);
        }

        if (Binder.getCallingPid() == Process.myPid()) {
            return;
        }

        final boolean isLegacyMode = mAppOpsManager.checkOp(AppOpsManager.OP_LEGACY_STORAGE,
                Binder.getCallingUid(), getCallingPackage()) == AppOpsManager.MODE_ALLOWED;
        Helpers.checkDestinationFilePathRestrictions(file, getCallingPackage(), getContext(),
                mAppOpsManager, getCallingAttributionTag(), isLegacyMode,
                /* allowDownloadsDirOnly */ true);
        // check whether record already exists in MP or getCallingPackage owns this file
        checkWhetherCallingAppHasAccess(file.getPath(), Binder.getCallingUid());
    }

    private void checkWhetherCallingAppHasAccess(String filePath, int uid) {
        try (ContentProviderClient client = getContext().getContentResolver()
                .acquireContentProviderClient(MediaStore.AUTHORITY)) {
            if (client == null) {
                Log.w(Constants.TAG, "Failed to acquire ContentProviderClient for MediaStore");
                return;
            }

            Uri filesUri = MediaStore.setIncludePending(
                    Helpers.getContentUriForPath(getContext(), filePath));

            try (Cursor cursor = client.query(filesUri,
                    new String[]{MediaStore.Files.FileColumns._ID,
                            MediaStore.Files.FileColumns.OWNER_PACKAGE_NAME},
                    MediaStore.Files.FileColumns.DATA + "=?", new String[]{filePath},
                    null)) {
                if (cursor != null && cursor.moveToFirst()) {
                    String fetchedOwnerPackageName = cursor.getString(
                            cursor.getColumnIndexOrThrow(
                                    MediaStore.Files.FileColumns.OWNER_PACKAGE_NAME));
                    String[] packageNames = getContext().getPackageManager().getPackagesForUid(uid);

                    if (fetchedOwnerPackageName != null && packageNames != null) {
                        boolean isCallerAuthorized = Arrays.asList(packageNames)
                                .contains(fetchedOwnerPackageName);
                        if (!isCallerAuthorized) {
                            throw new SecurityException("Caller does not have access to this path");
                        }
                    }
                }
            }
        } catch (RemoteException e) {
            Log.w(Constants.TAG, "Failed to query MediaStore: " + e.getMessage());
        }
    }

    boolean isFilenameValidInPublicDownloadsDir(File file) {
        try {
            if (Helpers.containsCanonical(Environment.buildExternalStoragePublicDirs(
                    Environment.DIRECTORY_DOWNLOADS), file)) {
                return true;
            }
        } catch (IOException e) {
            Log.w(TAG, "Failed to resolve canonical path: " + e);
            return false;
        }

        return false;
    }

    private String getPackageForUid(int uid) {
        String[] packages = getContext().getPackageManager().getPackagesForUid(uid);
        if (packages == null || packages.length == 0) {
            return null;
        }
        // For permission related purposes, any package belonging to the given uid should work.
        return packages[0];
    }

    private boolean needShowLimitDialog(String notifyPkg,String refPkg,long fileSize,int allowedNetType) {
        if (BuildUtils.isCmTestBuilder()) {
            return false;
        }
        if (BuildUtils.isInternationalVersion()) {
            return false;
        }

        Context context = getContext();
        boolean isfgNotifyPkg = Helpers.isRunningForeground(context, notifyPkg);
        boolean isfgRefPkg = Helpers.isRunningForeground(context, refPkg);
        if (!isfgNotifyPkg && !isfgRefPkg) {
            LogUtil.d(String.format("isfgNotifyPkg=%s isfgRefPkg=%s", String.valueOf(isfgNotifyPkg),
                    String.valueOf(isfgRefPkg)));
            return false;
        }

        boolean mobileActive = Statistics.isMobileActive(context);
        if (!mobileActive) {
            return false;
        }

        Long overLongSize = MobileDataConfig.getLimit(context);
        long overSize = overLongSize != null ? overLongSize.longValue() : -1;
        if (overSize == Long.MAX_VALUE) {
            return false;
        }

        if (fileSize > 0 && fileSize < overSize) {
            LogUtil.d(String.format("fileSize=%s overSize=%s", String.valueOf(fileSize),
                    String.valueOf(overSize)));
            return false;
        }

        if (NetworkUtils.checkIsNetworkTypeDisallowed(getContext(), allowedNetType, notifyPkg)) {
            LogUtil.d(String.format("allowedNetType=%s overSize=%s", String.valueOf(allowedNetType),
                    notifyPkg));
            return false;
        }
        return true;
    }

    private void printProcessInfo(String fuc) {
        if (Process.myPid() == Binder.getCallingPid()) {
            return;
        }
        XLConfig.LOGD_INFO("call " + fuc + " is " +
                ProcessUtil.getProcessInfo(getContext()));
    }

    long insertItem(SQLiteDatabase db, ContentValues value) {
        XLConfig.LOGD("call insertItem value=>" +value);
        long insert = -1;
        long btId = value.getAsLong(TORRENT_DOWNLOAD_ID);
        int fileIndex = value.getAsInteger(TORRENT_FILE_INDEX);
        insert = checkTask(db, btId, fileIndex);
        if (insert < 0) {
            Boolean isLast = value.getAsBoolean(TORRENT_LAST);
            Long total = value.getAsLong(COLUMN_TOTAL_BYTES);
            value.remove(TORRENT_LAST);
            value.remove(COLUMN_TOTAL_BYTES);
            insert = db.insert(TABLE_BT_DETAIL, null, value);
            if (isLast != null && isLast) {
                StringBuilder sel = new StringBuilder();
                sel.append(String.format("%s = ?", Downloads.Impl._ID));
                String[] args = new String[]{
                        String.valueOf(btId)
                };
                Cursor cursor = null;
                try {
                    cursor = db.query(DB_TABLE, null, sel.toString(), args, null, null, null);
                    if (cursor != null && cursor.moveToFirst()) {
                        int index = cursor.getColumnIndex(TORRENT_STATUS);
                        int status = cursor.getInt(index);
                        ContentValues mainValue = new ContentValues();
                        int totalIndex = cursor.getColumnIndex(COLUMN_TOTAL_BYTES);
                        long totalSize = cursor.getLong(totalIndex);
                        if (total != null && total.longValue() > totalSize) {
                            mainValue.put(COLUMN_TOTAL_BYTES, total.longValue());
                        }
                        if (Downloads.Impl.isStatusSuccess(status)) {
                            mainValue.put(COLUMN_STATUS, STATUS_PENDING);
                        }
                        if (mainValue.size() > 0) {
                            update(Downloads.Impl.CONTENT_URI, mainValue, sel.toString(), args);
                        }
                    }
                } catch (Exception e) {
                    XLConfig.LOGD("exc",e);
                } finally {
                    closeCursor(cursor);
                }
            }
        }
        if(insert>0) {
            DownloadServiceUtils.startService(getContext());
        }
        return insert;
    }

    @Override
    public ContentProviderResult[] applyBatch(ArrayList<ContentProviderOperation> operations)
            throws OperationApplicationException {
        SQLiteDatabase db = mOpenHelper.getWritableDatabase();
        final int numOperations = operations.size();
        ContentProviderResult[] results = new ContentProviderResult[numOperations];

        long start = System.currentTimeMillis();
        if (numOperations > 1) {
            db.beginTransaction();
        }
        int i = 0;
        try {
            for (i = 0; i < numOperations; i++) {
                results[i] = operations.get(i).apply(this, results, i);
            }

            if (numOperations > 1) {
                db.setTransactionSuccessful();
            }
        } catch (SQLException e) {
            logD("DB applyBatch failed.", e);
            throw new OperationApplicationException(e);
        } finally {
            if (numOperations > 1) {
                db.endTransaction();
            }
        }

        logD(String.format("applyBatch durration=%d, count:%d",
                System.currentTimeMillis() - start, numOperations));
        return results;

    }

    @Override
    public int bulkInsert(Uri uri, ContentValues[] values) {
        int match = sURIMatcher.match(uri);
        if (match != DOWNLOAD_BT_DETAIL) {
            logD("calling insert on an unknown/invalid URI: " + uri);
            throw new IllegalArgumentException("Unknown/Invalid URI " + uri);
        }
        logD("bulkInsert");
        long start = System.currentTimeMillis();
        final boolean useTran = values.length > 2;
        HashSet<Uri> inserted = new HashSet<Uri>();
        int numValues = 0;
        SQLiteDatabase db = mOpenHelper.getWritableDatabase();
        if (useTran) {
            db.beginTransaction();
        }
        try {
            numValues = values.length;
            for (int i = 0; i < numValues; i++) {
                ContentValues value = values[i];
                long btId = value.getAsLong(TORRENT_DOWNLOAD_ID);
                int fileIndex = value.getAsInteger(TORRENT_FILE_INDEX);
                long insertId = checkTask(db, btId, fileIndex);
                if (insertId < 0) {
                    long insert = db.insert(TABLE_BT_DETAIL, null, values[i]);
                    inserted.add(ContentUris.withAppendedId(URI_BT_DETAIL, insert));
                }
            }
            if (useTran) {
                db.setTransactionSuccessful();
            }
        } catch (Exception e) {
            logD("bulkInsert fail", e);
            inserted.clear();
        } finally {

            if (useTran) {
                db.endTransaction();
            }
        }
        if (!inserted.isEmpty()) {
            for (Uri notifyUri : inserted) {
                getContext().getContentResolver().notifyChange(notifyUri, null,NOTIFY_NO_DELAY);
            }
        }
        logD(String.format("bulkInsert durration=%d, count:%d",
                System.currentTimeMillis() - start, inserted.size()));
        return inserted.size();
    }

    private long checkTask(SQLiteDatabase db, long btId, int fileIndex) {
        StringBuilder sel = new StringBuilder();
        sel.append(String.format("(%s = ?)", TORRENT_DOWNLOAD_ID));
        sel.append(" AND ");
        sel.append(String.format("(%s = ?)", TORRENT_FILE_INDEX));
        String[] args = new String[]{
                String.valueOf(btId), String.valueOf(fileIndex)
        };
        int rowId = -1;
        Cursor cursor = null;
        try {
            cursor = BTDatabaseHelper.queryBtDetails(db, URI_BT_DETAIL, sel.toString(), args, null);
            if (cursor != null) {
                int count = cursor.getCount();
                if (count == 1) {
                    int columnIndex = cursor.getColumnIndex(TORRENT_ID);
                    rowId = cursor.getInt(columnIndex);
                } else if (count > 1) {
                    XLConfig.LOGD("table data duplication:btId=" + btId + " fileIndex="
                            + fileIndex + " count=" + count);
                } else {
                    XLConfig.LOGD(TAG, "checkTask count=" + count);
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return rowId;
    }

    /**
     * Check whether there is an identical download task.
     */
    private long checkDownloadTaskExist(SQLiteDatabase db, final Uri uri, final ContentValues values) {
        // get values of fields url
        String url = values.getAsString(Downloads.Impl.COLUMN_URI);
        String localUri = values.getAsString(Downloads.Impl.COLUMN_FILE_NAME_HINT);
        int destination = values.getAsInteger(Downloads.Impl.COLUMN_DESTINATION);
        if (TextUtils.isEmpty(url)
                || destination == Downloads.Impl.DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD) {
            return -1;
        }
        long ret = -1;
        // If url or localUri contains single quote, then Lexer validation will
        // throw exception, and sql execute will fail.
        url = url.replaceAll("'", "''");
        if (!TextUtils.isEmpty(localUri)) {
            localUri = localUri.replaceAll("'", "''");
        }
        SqlSelection selection = new SqlSelection();
        if (XLDownloadHelper.isBtTask(url)) {
            String hash = values.getAsString(TORRENT_FILE_INFOS_HASH);
            selection.appendClause(String.format("%s = ?", TORRENT_FILE_INFOS_HASH), hash);
        } else {
            selection.appendClause(String.format("%s = ?", Downloads.Impl.COLUMN_URI), url);
            selection.appendClause(String.format("%s = ?", Downloads.Impl.COLUMN_FILE_NAME_HINT),
                    !TextUtils.isEmpty(localUri) ? localUri : "");
        }
        selection.appendClause(String.format("%s = ?",Downloads.Impl.COLUMN_DELETED),0);
        selection.appendClause(String.format("%s = ?", Constants.UID), Binder.getCallingUid());
        Cursor cursor = null;
        try {
            cursor = db.query(DB_TABLE, null, selection.getSelection(), selection.getParameters(),
                    null, null, null);
            // There are repetitive tasks existing.
            if (cursor != null && cursor.moveToFirst()) {
                int statusColumnId = cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_STATUS);
                int idColumnId = cursor.getColumnIndexOrThrow(Downloads.Impl._ID);
                int uriColumn = cursor.getColumnIndexOrThrow(COLUMN_URI);
                for (; !cursor.isAfterLast(); cursor.moveToNext()) {
                    int status = cursor.getInt(statusColumnId);
                    String urlStr = cursor.getString(uriColumn);
                    // 检查bt任务是否完成，如果完成改为未完成状态
                    if (XLDownloadHelper.isBtTask(urlStr) && Downloads.Impl.isStatusSuccess(status)) {
                        ret = cursor.getInt(idColumnId);
                        break;
                        // check whether unfinished download task exists.
                    } else if (Downloads.Impl.isStatusInformational(status)) {
                        ret = cursor.getInt(idColumnId);
                        // if task is not running, then invoke it.
                        if (status != Downloads.Impl.STATUS_PENDING
                                && status != Downloads.Impl.STATUS_RUNNING) {
                            ContentValues updateValues = new ContentValues();
                            DownLoadProviderUtils.addRunningStatusAndControlRun(updateValues);
                            String whereClause = "( " + getWhereClauseForId() + " AND " +
                                    getWhereClauseForStatuses(new String[] {
                                            "=", "=", "=", "="
                                    },
                                            new String[] {
                                                    "OR", "OR", "OR"
                                            }) + ")";
                            String[] whereArgs = concatArrays(getWhereArgsForId(ret),
                                    getWhereArgsForStatuses(new int[] {
                                            Downloads.Impl.STATUS_PAUSED_BY_APP,
                                            Downloads.Impl.STATUS_WAITING_TO_RETRY,
                                            Downloads.Impl.STATUS_WAITING_FOR_NETWORK,
                                            Downloads.Impl.STATUS_QUEUED_FOR_WIFI
                                    }), String.class);
                            update(uri, updateValues, whereClause, whereArgs);
                        }
                        break;
                    } else if (Downloads.Impl.isStatusError(status)) {
                        // restart the failed task.
                        ret = cursor.getInt(idColumnId);
                        ContentValues updateValues = new ContentValues();
                        updateValues.put(Downloads.Impl.COLUMN_CURRENT_BYTES, 0);
                        updateValues.put(Downloads.Impl.COLUMN_TOTAL_BYTES, -1);
                        updateValues.putNull(Downloads.Impl._DATA);
                        updateValues.put(Downloads.Impl.COLUMN_STATUS,
                                Downloads.Impl.STATUS_PENDING);
                        updateValues.put(Downloads.Impl.COLUMN_CONTROL, Downloads.Impl.CONTROL_RUN);
                        updateValues.put(Downloads.Impl.COLUMN_FAILED_CONNECTIONS, 0);
                        update(uri, updateValues, getWhereClauseForId(), getWhereArgsForId(ret));
                        break;
                    }
                }
            } else {
                ret = checkApkDownloadTaskIsExistByPackageName(db, values);
            }
        } catch (Exception e) {
            XLConfig.LOGD("exc", e);
        } finally {
            closeCursor(cursor);
        }
        return ret;
    }


    private long checkApkDownloadTaskIsExistByPackageName(SQLiteDatabase db, final ContentValues values) {
        long ret = -1;
        String localUri = values.getAsString(Downloads.Impl.COLUMN_FILE_NAME_HINT);
        String apkPackageName = values.getAsString(COLUMN_APK_PACKGENAME);

        if (TextUtils.isEmpty(apkPackageName) || TextUtils.isEmpty(localUri)) {
            return ret;
        }
        SqlSelection selection = new SqlSelection();
        selection.appendClause(String.format("%s = ?", COLUMN_APK_PACKGENAME), apkPackageName);
        selection.appendClause(String.format("%s = ?", Downloads.Impl.COLUMN_FILE_NAME_HINT),
                !TextUtils.isEmpty(localUri) ? localUri : "");
        selection.appendClause(String.format("%s = ?", Constants.UID), Binder.getCallingUid());
        Cursor cursor = null;
        try {
            cursor = db.query(DB_TABLE, null, selection.getSelection(), selection.getParameters(),
                    null, null, null);
            // There are repetitive tasks existing.
            if (cursor != null && cursor.moveToFirst()) {
                int statusColumnId = cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_STATUS);
                int idColumnId = cursor.getColumnIndexOrThrow(Downloads.Impl._ID);
                for (; !cursor.isAfterLast(); cursor.moveToNext()) {
                    int status = cursor.getInt(statusColumnId);
                    if (Downloads.Impl.isStatusInformational(status)) {
                        ret = cursor.getInt(idColumnId);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            XLConfig.LOGD("exc", e);
        } finally {
            closeCursor(cursor);
        }
    return ret;
    }

    /**
     * 如果没有静默安装权限，既不处理
     *
     * @param values
     */
    private void checkInstallPermissions(ContentValues values) {
        if (!values.containsKey(COLUMN_APK_INSTALL_WAY)) {
            return;
        }

        ContentValues tempValues = new ContentValues(values);
        enforceAllowedValues(tempValues, COLUMN_APK_INSTALL_WAY,
                INSTALL_WAY_NONE,
                INSTALL_WAY_Manual,
                INSTALL_WAY_SILENCE);
        if (getContext().checkCallingOrSelfPermission("android.permission.XL_SILENCE_INSTALL")
                != PackageManager.PERMISSION_GRANTED) {
            Integer installWay = values.getAsInteger(COLUMN_APK_INSTALL_WAY);
            if (installWay == INSTALL_WAY_SILENCE) {
                values.put(COLUMN_APK_INSTALL_WAY, INSTALL_WAY_NONE);
            }
        }
    }

    /**
     * Apps with the ACCESS_DOWNLOAD_MANAGER permission can access this provider
     * freely, subject to constraints in the rest of the code. Apps without that
     * may still access this provider through the public API, but additional
     * restrictions are imposed. We check those restrictions here.
     *
     * @param values ContentValues provided to insert()
     * @throws SecurityException if the caller has insufficient permissions
     */
    private void checkInsertPermissions(ContentValues values) {
        String extras = values.getAsString(COLUMN_EXTRA);
        DownloadExtra.checkExtraValid(extras);
        DownloadExtra.checkDownloadFromItem(extras);

        if (getContext().checkCallingOrSelfPermission(Downloads.Impl.PERMISSION_ACCESS)
                == PackageManager.PERMISSION_GRANTED) {
            return;
        }

        getContext().enforceCallingOrSelfPermission(android.Manifest.permission.INTERNET,
                "INTERNET permission is required to use the download manager");

        // ensure the request fits within the bounds of a public API request
        // first copy so we can remove values
        ContentValues cloneValues = new ContentValues(values);

        // check columns whose values are restricted
        enforceAllowedValues(cloneValues, Downloads.Impl.COLUMN_IS_PUBLIC_API, Boolean.TRUE);

        // validate the destination column
        if (cloneValues.getAsInteger(Downloads.Impl.COLUMN_DESTINATION) == Downloads.Impl.DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD) {
            /*
             * this row is inserted by
             * DownloadManager.addCompletedDownload(String, String, String,
             * boolean, String, String, long)
             */
            cloneValues.remove(Downloads.Impl.COLUMN_TOTAL_BYTES);
            cloneValues.remove(Downloads.Impl._DATA);
            cloneValues.remove(Downloads.Impl.COLUMN_STATUS);
        }
        enforceAllowedValues(cloneValues, Downloads.Impl.COLUMN_DESTINATION,
                Downloads.Impl.DESTINATION_CACHE_PARTITION_PURGEABLE,
                Downloads.Impl.DESTINATION_FILE_URI,
                Downloads.Impl.DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD);

        if (getContext().checkCallingOrSelfPermission(Downloads.Impl.PERMISSION_NO_NOTIFICATION)
                == PackageManager.PERMISSION_GRANTED) {
            enforceAllowedValues(cloneValues, Downloads.Impl.COLUMN_VISIBILITY,
                    Request.VISIBILITY_HIDDEN,
                    Request.VISIBILITY_VISIBLE,
                    Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED,
                    Request.VISIBILITY_VISIBLE_NOTIFY_ONLY_COMPLETION);
        } else {
            enforceAllowedValues(cloneValues, Downloads.Impl.COLUMN_VISIBILITY,
                    Request.VISIBILITY_VISIBLE,
                    Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED,
                    Request.VISIBILITY_VISIBLE_NOTIFY_ONLY_COMPLETION);
        }

        // remove the rest of the columns that are allowed (with any value)
        cloneValues.remove(Downloads.Impl.COLUMN_URI);
        cloneValues.remove(Downloads.Impl.COLUMN_TITLE);
        cloneValues.remove(Downloads.Impl.COLUMN_DESCRIPTION);
        cloneValues.remove(Downloads.Impl.COLUMN_MIME_TYPE);
        cloneValues.remove(Downloads.Impl.COLUMN_FILE_NAME_HINT); // checked later in
        // insert()
        cloneValues.remove(Downloads.Impl.COLUMN_NOTIFICATION_PACKAGE); // checked
        // later in
        // insert()
        cloneValues.remove(Downloads.Impl.COLUMN_ALLOWED_NETWORK_TYPES);
        cloneValues.remove(Downloads.Impl.COLUMN_ALLOW_ROAMING);
        cloneValues.remove(Downloads.Impl.COLUMN_ALLOW_METERED);
        cloneValues.remove(Downloads.Impl.COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI);
        cloneValues.remove(Downloads.Impl.COLUMN_MEDIA_SCANNED);
        cloneValues.remove(Downloads.Impl.COLUMN_ALLOW_WRITE);
        cloneValues.remove(Downloads.Impl.COLUMN_TOTAL_BYTES);
        cloneValues.remove(ExtraDownloads.COLUMN_TASK_FOR_THUMBNAIL);
        cloneValues.remove(ExtraDownloads.COLUMN_APK_PACKGENAME);
        cloneValues.remove(COLUMN_FLAGS);
        cloneValues.remove(COLUMN_APK_INSTALL_WAY);
        cloneValues.remove(COLUMN_FILE_HASH);
        cloneValues.remove(COLUMN_EXTRA);
        cloneValues.remove(COLUMN_EXTRA2);
        Iterator<Map.Entry<String, Object>> iterator = cloneValues.valueSet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next().getKey();
            if (key.startsWith(Downloads.Impl.RequestHeaders.INSERT_KEY_PREFIX)) {
                iterator.remove();
            }
        }

        // any extra columns are extraneous and disallowed
        if (cloneValues.size() > 0) {
            StringBuilder invalidColumns = new StringBuilder("Invalid columns in request: ");
            boolean first = true;
            for (Map.Entry<String, Object> entry : cloneValues.valueSet()) {
                if (!first) {
                    invalidColumns.append(", ");
                }
                invalidColumns.append(entry.getKey());
                first = false;
            }
            XLConfig.LOGD(TAG, "DownloadThread checkInsertPermissions throw SecurityException: "
                    + invalidColumns.toString());
            throw new SecurityException(invalidColumns.toString());
        }
    }

    /**
     * Remove column from values, and throw a SecurityException if the value
     * isn't within the specified allowedValues.
     */
    private void enforceAllowedValues(ContentValues values, String column,
            Object... allowedValues) {
        Object value = values.get(column);
        values.remove(column);
        for (Object allowedValue : allowedValues) {
            if (value == null && allowedValue == null) {
                return;
            }
            if (value != null && value.equals(allowedValue)) {
                return;
            }
        }
        XLConfig.LOGD(TAG, "enforceAllowedValues throw SecurityException: Invalid value for "
                + column
                + ": " + value);
        throw new SecurityException("Invalid value for " + column + ": " + value);
    }

    private Cursor queryCleared(Uri uri, String[] projection, String selection,
                                String[] selectionArgs, String sort) {
        final long token = Binder.clearCallingIdentity();
        try {
            return query(uri, projection, selection, selectionArgs, sort);
        } finally {
            Binder.restoreCallingIdentity(token);
        }
    }

    /**
     * Starts a database query
     */
    @Override
    public Cursor query(final Uri uri, String[] projection,
            final String selection, final String[] selectionArgs,
            final String sort) {
        SQLiteDatabase db = null;
        Statistics.trackDownloadProviderCall(getContext(), "query");
        try {
            db = mOpenHelper.getReadableDatabase();
        } catch (SQLiteException e) {
            XLConfig.LOGD("query exc",e);
            return null;
        }

        int match = sURIMatcher.match(uri);
        if (match == -1) {
            XLConfig.LOGD(TAG, "querying unknown URI: " + uri);
            throw new IllegalArgumentException("Unknown URI: " + uri);
        }

        if (match == REQUEST_HEADERS_URI) {
            if (projection != null || selection != null || sort != null) {
                throw new UnsupportedOperationException("Request header queries do not support "
                        + "projections, selections or sorting");
            }
            return queryRequestHeaders(db, uri);
        } else if (match == DOWNLOAD_BT_DETAIL) {
            return BTDatabaseHelper.queryBtDetails(db, uri, selection, selectionArgs, sort);
        }

        validateSelection(selection);
        SqlSelection fullSelection = getWhereClause(uri, selection, selectionArgs, match);

        if (shouldRestrictVisibility()) {
            if (projection == null) {
                projection = sAppReadableColumnsArray.clone();
            } else {
                // check the validity of the columns in projection
                for (int i = 0; i < projection.length; ++i) {
                    if (!sAppReadableColumnsSet.contains(projection[i]) &&
                            !downloadManagerColumnsList.contains(projection[i])) {
                        throw new IllegalArgumentException(
                                "column " + projection[i] + " is not allowed in queries");
                    }
                }
            }

            for (int i = 0; i < projection.length; i++) {
                final String newColumn = sColumnsMap.get(projection[i]);
                if (newColumn != null) {
                    projection[i] = newColumn;
                }
            }
        }

        if (XLConfig.isDebug()) {
//            logVerboseQueryInfo(projection, selection, selectionArgs, sort, db);
        }

        if (projection != null) {
            for (int i = 0; i < projection.length; i++) {
                if (TextUtils.equals(projection[i], "local_filename")) {
                    logVerboseQueryInfo(projection, selection, selectionArgs, sort, db);
                    printProcessInfo("logVerboseQueryInfo ");
                    projection[i] = "_data AS local_filename";
                } else if (TextUtils.equals(projection[i], "media_type")) {
                    logVerboseQueryInfo(projection, selection, selectionArgs, sort, db);
                    printProcessInfo("logVerboseQueryInfo ");
                    projection[i] = "mimetype AS media_type";
                }

            }
        }
        Cursor ret = null;
        try {
            ret = db.query(DB_TABLE, projection, fullSelection.getSelection(),
                    fullSelection.getParameters(), null, null, sort);
        }catch (Exception e){
            XLConfig.LOGD(TAG, "query 1 failed in downloads database",e);
        }
        if (ret != null) {
            ret.setNotificationUri(getContext().getContentResolver(), uri);
        } else {
            XLConfig.LOGD(TAG, "query failed in downloads database");
        }

        return ret;
    }

    private void validateSelection(String selection) {
        if (Binder.getCallingPid() != Process.myPid()) {
            Helpers.validateSelection(selection, sAppReadableColumnsSet);
        }
    }

    private void logVerboseQueryInfo(String[] projection, final String selection,
            final String[] selectionArgs,
            final String sort, SQLiteDatabase db) {
        StringBuilder sb = new StringBuilder();
        sb.append("starting query, database is ");
        if (db != null) {
            sb.append("not ");
        }
        sb.append("null; ");
        if (projection == null) {
            sb.append("projection is null; ");
        } else if (projection.length == 0) {
            sb.append("projection is empty; ");
        } else {
            for (int i = 0; i < projection.length; ++i) {
                sb.append("projection[");
                sb.append(i);
                sb.append("] is ");
                sb.append(projection[i]);
                sb.append("; ");
            }
        }
        sb.append("selection is ");
        sb.append(selection);
        sb.append("; ");
        if (selectionArgs == null) {
            sb.append("selectionArgs is null; ");
        } else if (selectionArgs.length == 0) {
            sb.append("selectionArgs is empty; ");
        } else {
            for (int i = 0; i < selectionArgs.length; ++i) {
                sb.append("selectionArgs[");
                sb.append(i);
                sb.append("] is ");
                sb.append(selectionArgs[i]);
                sb.append("; ");
            }
        }
        sb.append("sort is ");
        sb.append(sort);
        sb.append(".");
        //Log.v(Constants.TAG, sb.toString());
    }

    private String getDownloadIdFromUri(final Uri uri) {
        return uri.getPathSegments().get(1);
    }

    /**
     * Insert request headers for a download into the DB.
     */
    private void insertRequestHeaders(SQLiteDatabase db, long downloadId, ContentValues values) {
        if (values == null) {
            return;
        }
        ContentValues rowValues = new ContentValues();
        rowValues.put(Downloads.Impl.RequestHeaders.COLUMN_DOWNLOAD_ID, downloadId);
        String notifyPkg = (String) values.get(Downloads.Impl.COLUMN_NOTIFICATION_PACKAGE);
        boolean isMarket = TextUtils.equals("com.xiaomi.market", notifyPkg);
        for (Map.Entry<String, Object> entry : values.valueSet()) {
            String key = entry.getKey();
            Object objValue = entry.getValue();
            String headerLine = objValue != null ? objValue.toString() : null;
            if (key != null && headerLine != null && key.startsWith(Downloads.Impl.RequestHeaders.INSERT_KEY_PREFIX)) {
                XLConfig.LOGD("packageInstalled http ref key=" + key + " value="
                        + (!TextUtils.isEmpty(headerLine) ? headerLine : ""));
                if (headerLine != null && !headerLine.contains(":")) {
                    throw new IllegalArgumentException("Invalid HTTP header line: " + headerLine);
                }
                String[] parts = headerLine.split(":", 2);
                // 为了区分下载来源是应用商店的广告DOWNLOAD_MANAGER-1234
                XLConfig.LOGD(TAG, "packageInstalled insertRequestHeaders notifyPkg=" + notifyPkg
                        + " "
                        + parts[0] + " " + parts[1]);
                if (isMarket && TextUtils.equals("ref", parts[0])) {
                    String value = parts[1].trim();
                    if (value != null && value.startsWith("DownloadManager_detail_")) {
                        String appID = value.substring("DownloadManager_detail_".length());
                        Statistics.reportDetailDownload(getContext(), appID, downloadId);
                    } else if (value != null && value.startsWith("DownloadManager_direct_")) {
                        String appID = value.substring("DownloadManager_direct_".length());
                        Statistics.reportDirectDownload(getContext(), appID, downloadId);
                    }
                } else {
                    rowValues.put(Downloads.Impl.RequestHeaders.COLUMN_HEADER, parts[0].trim());
                    rowValues.put(Downloads.Impl.RequestHeaders.COLUMN_VALUE, parts[1].trim());
                    db.insert(Downloads.Impl.RequestHeaders.HEADERS_DB_TABLE, null, rowValues);
                }
            }
        }
    }

    /**
     * Handle a query for the custom request headers registered for a download.
     */
    private Cursor queryRequestHeaders(SQLiteDatabase db, Uri uri) {
        String where = Downloads.Impl.RequestHeaders.COLUMN_DOWNLOAD_ID + "="
                + getDownloadIdFromUri(uri);
        String[] projection = new String[] {
                Downloads.Impl.RequestHeaders.COLUMN_HEADER,
                Downloads.Impl.RequestHeaders.COLUMN_VALUE
        };
        return db.query(Downloads.Impl.RequestHeaders.HEADERS_DB_TABLE, projection, where,
                null, null, null, null);
    }

    /**
     * Delete request headers for downloads matching the given query.
     */
    private void deleteRequestHeaders(SQLiteDatabase db, String where, String[] whereArgs) {
        String[] projection = new String[] {
                Downloads.Impl._ID
        };
        Cursor cursor = null;
        try {
            cursor = db.query(DB_TABLE, projection, where, whereArgs, null, null, null, null);
            for (cursor.moveToFirst(); !cursor.isAfterLast(); cursor.moveToNext()) {
                long id = cursor.getLong(0);
                String idWhere = Downloads.Impl.RequestHeaders.COLUMN_DOWNLOAD_ID + "=" + id;
                db.delete(Downloads.Impl.RequestHeaders.HEADERS_DB_TABLE, idWhere, null);
            }
        } finally {
            closeCursor(cursor);
        }
    }

    /**
     * @return true if we should restrict the columns readable by this caller
     */
    private boolean shouldRestrictVisibility() {
        int callingUid = Binder.getCallingUid();
        return Binder.getCallingPid() != Process.myPid() &&
                callingUid != mSystemUid &&
                callingUid != mDefContainerUid;
    }

    /**
     * Updates a row in the database
     */
    @Override
    public int update(final Uri uri, final ContentValues values,
            final String where, final String[] whereArgs) {
        boolean fromUi = DownLoadProviderUtils.fromUi(values);
        printProcessInfo("update");
        Statistics.trackDownloadProviderCall(getContext(), "update");
        int match = sURIMatcher.match(uri);
        // set recommended max download bytes over mobile
        if (match == XL_DOWNLOAD_BYTES_LIMIT_OVER_MOBILE) {
            Long recommednSizeLimit = values
                    .getAsLong(Settings.Global.DOWNLOAD_RECOMMENDED_MAX_BYTES_OVER_MOBILE);
            if (recommednSizeLimit != null) {
                boolean success = MobileDataConfig.setLimit(getContext(),
                        recommednSizeLimit.longValue());
                if (success) {
                    getContext().getContentResolver().notifyChange(uri, null,NOTIFY_NO_DELAY);
                    DownloadServiceUtils.startService(getContext());
                }
            }
            return -1;
        }

        final Context context = getContext();
        final ContentResolver resolver = context.getContentResolver();

        SQLiteDatabase db = mOpenHelper.getWritableDatabase();
        if (match == DOWNLOAD_BT_DETAIL) {
            int count = BTDatabaseHelper.updateBtDetail(db, values, where, whereArgs);
            if (count > 0) {
                getContext().getContentResolver().notifyChange(uri, null,NOTIFY_NO_DELAY);
            }
            logD("update", uri, "count=" + count, values, where, whereArgs);
            return count;
        }
        validateSelection(where);

        int count;
        boolean startService = false;


        if (values.containsKey(Downloads.Impl.COLUMN_DELETED) && (values.getAsInteger(Downloads.Impl.COLUMN_DELETED) == 1)) {
            // some rows are to be 'deleted'. need to start DownloadService.
            startService = true;
            //在此处删除文件的MediaStore
            deleteMediaStore(uri);
        }

        if (values.containsKey(COLUMN_CONTROL) && (values.getAsInteger(COLUMN_CONTROL) == Downloads.Impl.CONTROL_RUN)
                && values.containsKey(COLUMN_STATUS) && (values.getAsInteger(COLUMN_STATUS) == STATUS_RUNNING)) {
            values.put(COLUMN_STATUS, STATUS_PENDING);
            values.put(COLUMN_ERROR_MSG, "");
        }

        ContentValues filteredValues;
        if (Binder.getCallingPid() != Process.myPid() || fromUi) {
            filteredValues = new ContentValues();
            copyString(Downloads.Impl.COLUMN_APP_DATA, values, filteredValues);
            copyInteger(Downloads.Impl.COLUMN_VISIBILITY, values, filteredValues);
            copyInteger(Downloads.Impl.COLUMN_CONTROL, values, filteredValues);
            copyInteger(Downloads.Impl.COLUMN_STATUS, values, filteredValues);
            copyString(Downloads.Impl.COLUMN_TITLE, values, filteredValues);
            copyString(Downloads.Impl.COLUMN_MEDIAPROVIDER_URI, values, filteredValues);
            copyString(Downloads.Impl.COLUMN_DESCRIPTION, values, filteredValues);
            copyInteger(Downloads.Impl.COLUMN_DELETED, values, filteredValues);
            if (DownLoadProviderUtils.allowControlDownload(getContext())){
                //forceDownload
                copyInteger(Downloads.Impl.COLUMN_ALLOWED_NETWORK_TYPES,values, filteredValues);
                copyBoolean(Downloads.Impl.COLUMN_ALLOW_METERED,values, filteredValues);
                copyBoolean(Downloads.Impl.COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT,values, filteredValues);
                //for ui restart
                copyInteger(Downloads.Impl.COLUMN_CURRENT_BYTES, values, filteredValues);
                copyInteger(Downloads.Impl.COLUMN_TOTAL_BYTES, values, filteredValues);
                copyAllString(Downloads.Impl._DATA, values, filteredValues);
                copyInteger(Downloads.Impl.COLUMN_FAILED_CONNECTIONS, values, filteredValues);
            }
        } else {
            filteredValues = values;
            String filename = values.getAsString(Downloads.Impl._DATA);
            if (filename != null) {
                try {
                    filteredValues.put(Downloads.Impl._DATA, new File(filename).getCanonicalPath());
                } catch (IOException e) {
                    throw new IllegalStateException("Invalid path: " + filename);
                }
                Cursor c = null;
                try {
                    c = query(uri, new String[]{Downloads.Impl.COLUMN_TITLE}, null, null, null);
                    if (c == null || !c.moveToFirst() || c.getString(0).isEmpty()) {
                        String title = new File(filename).getName();
                        try {
                            // Need decode to avoid error code.
                            title = URLDecoder.decode(title, "UTF-8");
                        } catch (Exception e) {
                            XLConfig.LOGD("decode exc " + e.getMessage());
                        }
                        values.put(Downloads.Impl.COLUMN_TITLE, title);
                    }
                } finally {
                    if (c != null) {
                        c.close();
                    }
                }
            }
        }

        Integer status = values.getAsInteger(Downloads.Impl.COLUMN_STATUS);
        boolean isRestart = status != null && status == Downloads.Impl.STATUS_PENDING;
        boolean isUserBypassingSizeLimit =
                values.containsKey(Downloads.Impl.COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT);
        if (isRestart || isUserBypassingSizeLimit) {
            startService = true;
        }

        Integer i = values.getAsInteger(Downloads.Impl.COLUMN_CONTROL);
        if (i != null) {
            startService = true;
        }

        switch (match) {
            case MY_DOWNLOADS:
            case MY_DOWNLOADS_ID:
            case ALL_DOWNLOADS:
            case ALL_DOWNLOADS_ID:
                SqlSelection selection = getWhereClause(uri, where, whereArgs, match);
                if (filteredValues.size() > 0) {
                    try {
                        HashSet<Long> idsSizeLimite = new HashSet<Long>();
                        HashSet<Long> idsBt = null;
                        if (BTDatabaseHelper.needCheckBtTask(filteredValues)) {
                            idsBt = new HashSet<Long>();
                        }
                        //收集需要处理隐私弹框的ids和需要处理bt任务的ids
                        DownLoadProviderUtils.collectNeedHandIds(db,
                                selection.getSelection(), selection.getParameters(), idsSizeLimite, idsBt);
                        long[] ids = DownLoadProviderUtils.queryIds(db, DB_TABLE,
                                selection.getSelection(),
                                selection.getParameters());
                        count = db.update(DB_TABLE, filteredValues, selection.getSelection(),
                                selection.getParameters());

                        long token = Binder.clearCallingIdentity();
                        try (Cursor cursor = db.query(DB_TABLE, null, selection.getSelection(), selection.getParameters(), null, null, null);
                             ContentProviderClient client = getContext().getContentResolver()
                                     .acquireContentProviderClient(MediaStore.AUTHORITY)) {
                            final DownloadInfo.Reader reader = new DownloadInfo.Reader(resolver,
                                    cursor);
                            final DownloadInfo info = new DownloadInfo(context);
                            final ContentValues updateValues = new ContentValues();
                            while (cursor.moveToNext()) {
                                reader.updateFromDatabase(getContext().getContentResolver(),info);
                                //注意这里visbleToUser和原生逻辑区别，由于COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI是让全部下载在ui显示，所以这块不能用COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI字段判断
                                //因此只用MEDIA_NOT_SCANNABLE判断
                                final boolean visibleToUser = info.mMediaScanned != MEDIA_NOT_SCANNABLE;
                                if (info.mFileName == null) {
                                    if (info.mMediaStoreUri != null) {
                                        // If there was a mediastore entry, it would be deleted in it's
                                        // next idle pass.
                                        updateValues.clear();
                                        updateValues.putNull(Downloads.Impl.COLUMN_MEDIASTORE_URI);
                                        db.update(DB_TABLE, updateValues, Downloads.Impl._ID + "=?",
                                                new String[] { Long.toString(info.mId) });
                                    }
                                } else if ((info.mDestination == Downloads.Impl.DESTINATION_EXTERNAL
                                        || info.mDestination == Downloads.Impl.DESTINATION_FILE_URI
                                        || info.mDestination == Downloads.Impl
                                        .DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD)
                                        && visibleToUser) {
                                    final ContentValues mediaValues = convertToMediaProviderValues(info);
                                    Uri mediaStoreUri = null;
                                    if (Downloads.Impl.isStatusCompleted(info.mStatus)) {
                                        // Set size to 0 to ensure MediaScanner will scan this file.
                                        mediaValues.put(MediaStore.Downloads.SIZE, 0);
                                        updateMediaProvider(client, mediaValues);
                                        mediaStoreUri = triggerMediaScan(client, new File(info.mFileName));
                                    }
                                    if (!TextUtils.equals(info.mMediaStoreUri,
                                            mediaStoreUri == null ? null : mediaStoreUri.toString())) {
                                        updateValues.clear();
                                        if (mediaStoreUri == null) {
                                            updateValues.putNull(Downloads.Impl.COLUMN_MEDIASTORE_URI);
                                            updateValues.putNull(Downloads.Impl.COLUMN_MEDIAPROVIDER_URI);
                                            updateValues.put(COLUMN_MEDIA_SCANNED, MEDIA_NOT_SCANNED);
                                        } else {
                                            updateValues.put(Downloads.Impl.COLUMN_MEDIASTORE_URI,
                                                    mediaStoreUri.toString());
                                            updateValues.put(Downloads.Impl.COLUMN_MEDIAPROVIDER_URI,
                                                    mediaStoreUri.toString());
                                            updateValues.put(COLUMN_MEDIA_SCANNED, MEDIA_SCANNED);
                                        }
                                        db.update(DB_TABLE, updateValues, Downloads.Impl._ID + "=?",
                                                new String[] { Long.toString(info.mId) });
                                    }
                                }
                            }
                        } finally {
                            Binder.restoreCallingIdentity(token);
                        }


                        if(values.containsKey(Downloads.Impl.COLUMN_DELETED)) {
                            XLConfig.LOGD_INFO(Statistics.toString(uri, "count=" + count, values, where, whereArgs));
                        }else {
                            logD(uri, "count=" + count, values, where, whereArgs);
                        }
                        //对于bt任务，如果是非app触发的应该同步更新子任务状态
                        if (idsBt != null && !idsBt.isEmpty()) {
                            long[] idsArray = DownLoadProviderUtils.setToArray(idsBt);
                            if (DownLoadProviderUtils.isPauseOper(filteredValues)) {
                                BTDatabaseHelper.pauseBtSubTask(db, idsArray);
                            } else if (DownLoadProviderUtils.isResumeOper(filteredValues)) {
                                BTDatabaseHelper.resumeBtSubTask(db, idsArray);
                            } else if (DownLoadProviderUtils.isReStartOper(filteredValues)) {
                                BTDatabaseHelper.restartBtSubTask(db, idsArray);
                            }
                        }
                        if (!Helpers.isCmTestBuilder() && !Helpers.isInternationalBuilder() && count > 0
                                && DownLoadProviderUtils.isDownloadResume(getContext(), filteredValues,fromUi)) {
                            token = Binder.clearCallingIdentity();
                            try {
                                DownLoadProviderUtils.handleSizeLimite(getContext(), ids);
                            } finally {
                                Binder.restoreCallingIdentity(token);
                            }
                        }

                    } catch (Exception e) {
                        XLConfig.LOGD(TAG, "error when update!", e);
                        count = 0;
                    }
                } else {
                    count = 0;
                }
                break;
            default:
                XLConfig.LOGD(TAG, "updating unknown/invalid URI: " + uri);
                throw new UnsupportedOperationException("Cannot update URI: " + uri);
        }

        if (count > 0) {
            notifyContentChanged(uri, match);
        }
        if (startService) {
            DownloadServiceUtils.startService(getContext());
        }
        return count;
    }

    /**
     * 在update中删除MediaStore记录，因为cts问题
     * cts直接调用delete方法，在miuiDownloadManager中会标记delete，之后由Service，调用delete，造成不同步问题
     * android.app.cts.downloads.api28 android.app.cts.DownloadManagerApi28Test#testAddCompletedDownload_mediaStoreEntry FAILURE: java.lang.AssertionError: expected:<0> but was:<1>
     * @param uri
     */
    private void deleteMediaStore(Uri uri) {
        if (uri == null) {
            return;
        }
        Cursor c = null;
        String path = null;
        try {
            c = query(uri, new String[]{COLUMN_DATA}, null, null, null);
            if (c != null) {
                while (c.moveToNext()) {
                    path = c.getString(0);
                }
            }
        } finally {
            if (c != null) {
                c.close();
            }
        }
        if (!TextUtils.isEmpty(path)) {
            try {
                final File file = new File(path).getCanonicalFile();
                if (Helpers.isFilenameValid(getContext(), file,true)) {
                    deleteMediaStoreEntry(file);
                } else {
                    Log.d(Constants.TAG, "Ignoring invalid file: " + file);
                }
            } catch (IOException e) {
                Log.e(Constants.TAG, "Couldn't delete deleteMediaStoreEntry: " + path, e);
            }
        }
    }

    private void deleteMediaStoreEntry(File file) {
        final long token = Binder.clearCallingIdentity();
        try {
            final String path = file.getAbsolutePath();
            Uri.Builder builder = MediaStore.Files.getContentUri(getVolumeName(new File(path))).buildUpon();
            builder.appendQueryParameter("deletedata", "false");
            final Uri filesUri = builder.build();
            getContext().getContentResolver().delete(filesUri,
                    MediaStore.Files.FileColumns.DATA + "=?", new String[] { path });
        } catch (Exception e) {
            Log.d(Constants.TAG, "Failed to delete mediastore entry for file:" + file, e);
        } finally {
            Binder.restoreCallingIdentity(token);
        }
    }

    private   String getVolumeName(File path) {
        // Ideally we'd find the relevant StorageVolume, but we don't have a
        // Context to obtain it from, so the best we can do is assume
        if (path.getAbsolutePath()
                .startsWith(Environment.getStorageDirectory().getAbsolutePath())) {
            return MediaStore.VOLUME_EXTERNAL;
        } else {
            return MediaStore.VOLUME_INTERNAL;
        }
    }

    /**
     * Notify of a change through both URIs (/my_downloads and /all_downloads)
     *
     * @param uri either URI for the changed download(s)
     * @param uriMatch the match ID from {@link #sURIMatcher}
     */

    private void notifyContentChanged(final Uri uri, int uriMatch) {
        Long downloadId = null;
        if (uriMatch == MY_DOWNLOADS_ID || uriMatch == ALL_DOWNLOADS_ID) {
            downloadId = Long.parseLong(getDownloadIdFromUri(uri));
        }
        for (Uri uriToNotify : BASE_URIS) {
            if (downloadId != null) {
                uriToNotify = ContentUris.withAppendedId(uriToNotify, downloadId);
            }
            getContext().getContentResolver().notifyChange(uriToNotify, null,NOTIFY_NO_DELAY);
        }
    }

    private SqlSelection getWhereClause(final Uri uri, final String where,
            final String[] whereArgs,
            int uriMatch) {
        SqlSelection selection = new SqlSelection();
        selection.appendClause(where, whereArgs);
        if (uriMatch == MY_DOWNLOADS_ID || uriMatch == ALL_DOWNLOADS_ID) {
            selection.appendClause(Downloads.Impl._ID + " = ?", getDownloadIdFromUri(uri));
        }
        if ((uriMatch == MY_DOWNLOADS || uriMatch == MY_DOWNLOADS_ID)
                && getContext().checkCallingOrSelfPermission(Downloads.Impl.PERMISSION_ACCESS_ALL)
                    != PackageManager.PERMISSION_GRANTED) {
            selection.appendClause(
                    Constants.UID + "= ? OR " + Downloads.Impl.COLUMN_OTHER_UID + "= ?",
                    Binder.getCallingUid(), Binder.getCallingUid());
        }
        return selection;
    }

    /**
     * Deletes a row in the database
     */
    @Override
    public int delete(final Uri uri, final String where,
            final String[] whereArgs) {
        printProcessInfo("delete");
        Statistics.trackDownloadProviderCall(getContext(), "delete");
        Statistics.logD(TAG, "delete " + uri, where, whereArgs);
        if (shouldRestrictVisibility()) {
            validateSelection(where);
        }
        String packageName = null;
        final Context context = getContext();
        final ContentResolver resolver = context.getContentResolver();
        int installWay = 0;
        SQLiteDatabase db = mOpenHelper.getWritableDatabase();
        int count = 0;
        int match = sURIMatcher.match(uri);
        switch (match) {
            case MY_DOWNLOADS:
            case MY_DOWNLOADS_ID:
            case ALL_DOWNLOADS:
            case ALL_DOWNLOADS_ID:
                SqlSelection selection = getWhereClause(uri, where, whereArgs, match);
                deleteRequestHeaders(db, selection.getSelection(), selection.getParameters());
                BTDatabaseHelper.deleteBtDetailById(db, selection.getSelection(),
                        selection.getParameters());
                Cursor cursor = null;
                try {
                    cursor = db.query(DB_TABLE, null, selection.getSelection(), selection.getParameters(),
                            null, null, null);
                    if (cursor != null) {
                        ArrayList<DownloadItem> items = new ArrayList<DownloadItem>();
                        final DownloadInfo.Reader reader = new DownloadInfo.Reader(resolver, cursor);
                        final DownloadItem.Reader reader2 = new DownloadItem.Reader(cursor);
                        final DownloadInfo info = new DownloadInfo(context);
                        while (cursor.moveToNext()) {
                            reader.updateFromDatabase(resolver, info);
                            final long id = cursor.getLong(0);
                            revokeAllDownloadsPermission(id);
                            DownloadStorageProvider.onDownloadProviderDelete(getContext(), id);

                            int columnStatus = cursor.getColumnIndex(COLUMN_STATUS);
                            int columnData = cursor.getColumnIndex(COLUMN_DATA);
                            int columnUri = cursor.getColumnIndex(COLUMN_URI);
                            packageName = cursor.getString(cursor
                                    .getColumnIndex(COLUMN_APK_PACKGENAME));
                            String columnPath = cursor.getString(columnData);
                            String url = cursor.getString(columnUri);
                            installWay = cursor.getInt(cursor.getColumnIndex(COLUMN_APK_INSTALL_WAY));
                            if (XLDownloadHelper.isBtTask(url)) {
                                FileUtil.deleteFileIfExists(columnPath);
                            }

                            final String path = info.mFileName;
                            if (!TextUtils.isEmpty(path)) {
                                try {
                                    final File file = new File(path).getCanonicalFile();
                                    if (Helpers.isFilenameValid2(getContext(), file)) {
                                        Log.v(Constants.TAG,
                                                "Deleting " + file + " via provider delete");
                                        MediaStore.scanFile(getContext().getContentResolver(), file);
                                    }
                                } catch (IOException ignored) {
                                }
                            }

                            int status = cursor.getInt(columnStatus);
                            if (status != STATUS_RUNNING) {
                                //上报非运行状态下被删除的任务
                                items.add(reader2.newDownloadItem());
                            }
                        }
                        Statistics.trackDownloadDelete(getContext(), items);
                    }
                } catch (Exception e) {
                    Log.w(TAG, "delete fail ", e);
                } finally {
                    closeCursor(cursor);
                }

                count = db.delete(DB_TABLE, selection.getSelection(), selection.getParameters());
                break;
            case DOWNLOAD_BT_DETAIL:
                SqlSelection sel = getWhereClause(uri, where, whereArgs, match);
                BTDatabaseHelper.deleteBtDetail(db, sel.getSelection(),
                        sel.getParameters());
                break;
            default:
                XLConfig.LOGD(TAG, "deleting unknown/invalid URI: " + uri);
                throw new UnsupportedOperationException("Cannot delete URI: " + uri);
        }
        if (count > 0) {
            notifyContentChanged(uri, match);
            if (installWay == 1) {
                DesktopProgressAppInfo appInfo = DesktopProgressAppInfo.appInfoMap.get(packageName);
                if (appInfo != null) {
                    DesktopProgressAppInfo.deleteApkFromDownloadlistSet.add(packageName);
                    appInfo.removeFromShell(packageName);
                }
            }
        }
        return count;
    }

    /**
     * Remotely opens a file
     */
    @Override
    public ParcelFileDescriptor openFile(final Uri uri, String mode) throws FileNotFoundException {
        if (XLConfig.isDebug()) {
            logVerboseOpenFileInfo(uri, mode);
        }
        // Perform normal query to enforce caller identity access before
        // clearing it to reach internal-only columns
        final Cursor probeCursor = query(uri, new String[] {
                Downloads.Impl._DATA }, null, null, null);
        try {
            if ((probeCursor == null) || (probeCursor.getCount() == 0)) {
                throw new FileNotFoundException(
                        "No file found for " + uri + " as UID " + Binder.getCallingUid());
            }
        } finally {
            closeCursor(probeCursor);
        }

        Cursor cursor = null;
        String path = null;
        boolean shouldScan = false;
        try {
            cursor = queryCleared(uri, new String[] {
                    Downloads.Impl._DATA ,Downloads.Impl.COLUMN_STATUS,
                    Downloads.Impl.COLUMN_DESTINATION, Downloads.Impl.COLUMN_MEDIA_SCANNED
            }, null, null, null);
            int count = (cursor != null) ? cursor.getCount() : 0;
            if (count != 1) {
                // If there is not exactly one result, throw an appropriate
                // exception.
                if (count == 0) {
                    throw new FileNotFoundException("No entry for " + uri);
                }
                throw new FileNotFoundException("Multiple items at " + uri);
            }

            if (cursor != null && cursor.moveToFirst()) {
                path = cursor.getString(0);
                final int status = cursor.getInt(1);
                final int destination = cursor.getInt(2);
                final int mediaScanned = cursor.getInt(3);

                shouldScan = Downloads.Impl.isStatusSuccess(status) && (
                        destination == Downloads.Impl.DESTINATION_EXTERNAL
                                || destination == Downloads.Impl.DESTINATION_FILE_URI
                                || destination == Downloads.Impl.DESTINATION_NON_DOWNLOADMANAGER_DOWNLOAD)
                        && mediaScanned != Downloads.Impl.MEDIA_NOT_SCANNABLE;
            }
        } finally {
            closeCursor(cursor);
        }

        if (path == null) {
            throw new FileNotFoundException("No filename found.");
        }

        final File file;
        try {
            file = new File(path).getCanonicalFile();
        } catch (IOException e) {
            throw new SecurityException(e);
        }
        if ("r".equals(mode)) {
            return ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY);
        } else {
            try {
                // When finished writing, update size and timestamp
                final boolean finalShouldScan = shouldScan;
                return ParcelFileDescriptor.open(file, ParcelFileDescriptor.parseMode(mode),
                        Helpers.getAsyncHandler(), new OnCloseListener() {
                            @Override
                            public void onClose(IOException e) {
                                XLConfig.LOGD(TAG, "in openFile onClose!", e);
                                final ContentValues values = new ContentValues();
                                values.put(Downloads.Impl.COLUMN_LAST_MODIFICATION,
                                        System.currentTimeMillis());
                                update(uri, values, null, null);

                                if (finalShouldScan) {
                                    final Intent intent = new Intent(
                                            Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
                                    intent.setData(Uri.fromFile(file));
                                    getContext().sendBroadcast(intent);
                                }
                            }
                        });
            } catch (IOException e) {
                throw new FileNotFoundException("Failed to open for writing: " + e);
            }
        }
    }

    @Override
    public void dump(FileDescriptor fd, PrintWriter writer, String[] args) {
        LimitSpeed.dump(writer);
        dumpDownloads(fd, writer, args);
        dumpDetails(fd, writer, args);
    }

    public void dumpDownloads(FileDescriptor fd, PrintWriter writer, String[] args) {
        final IndentingPrintWriter pw = new IndentingPrintWriter(writer, "  ", 120);

        pw.println(String.format("Downloads dump(%s):", XLUtil.getAppVersion(getContext())));
        pw.increaseIndent();

        final SQLiteDatabase db = mOpenHelper.getReadableDatabase();
        Cursor cursor = null;
        try {
            cursor = db.query(DB_TABLE, null, null, null, null, null, Downloads.Impl._ID + " ASC");
            final String[] cols = cursor.getColumnNames();
            final int idCol = cursor.getColumnIndex(BaseColumns._ID);
            while (cursor.moveToNext()) {
                pw.println("Download #" + cursor.getInt(idCol) + ":");
                pw.increaseIndent();
                for (int i = 0; i < cols.length; i++) {
                    // Omit sensitive data when dumping
                    if (Downloads.Impl.COLUMN_COOKIE_DATA.equals(cols[i])) {
                        continue;
                    }
                    pw.printPair(cols[i], cursor.getString(i));
                }
                pw.println();
                pw.decreaseIndent();
            }
        } finally {
            closeCursor(cursor);
        }

        pw.decreaseIndent();
    }

    public void dumpDetails(FileDescriptor fd, PrintWriter writer, String[] args) {
        final IndentingPrintWriter pw = new IndentingPrintWriter(writer, "  ", 120);

        pw.println("Downloads dumpDetails:");
        pw.increaseIndent();

        final SQLiteDatabase db = mOpenHelper.getReadableDatabase();
        Cursor cursor = null;
        try {
            cursor = db.query(TABLE_BT_DETAIL, null, null, null, null, null,
                    Downloads.Impl._ID + " ASC");
            final String[] cols = cursor.getColumnNames();
            final int idCol = cursor.getColumnIndex(BaseColumns._ID);
            while (cursor.moveToNext()) {
                pw.println("bt_detail #" + cursor.getInt(idCol) + ":");
                pw.increaseIndent();
                for (int i = 0; i < cols.length; i++) {
                    pw.printPair(cols[i], cursor.getString(i));
                }
                pw.println();
                pw.decreaseIndent();
            }
        } finally {
            closeCursor(cursor);
        }
        pw.decreaseIndent();
    }

    private void logVerboseOpenFileInfo(Uri uri, String mode) {
        XLConfig.LOGD(TAG, "openFile uri: " + uri + ", mode: " + mode + ", uid: "
                + Binder.getCallingUid());
        Cursor cursor = query(Downloads.Impl.CONTENT_URI,
                new String[] { "_id" }, null, null, "_id");
        if (cursor == null) {
            Log.v(Constants.TAG, "null cursor in openFile");
        } else {
            try {
                if (!cursor.moveToFirst()) {
                    Log.v(Constants.TAG, "empty cursor in openFile");
                } else {
                    do {
                        Log.v(Constants.TAG, "row " + cursor.getInt(0) + " available");
                    } while(cursor.moveToNext());
                }
            } finally {
                cursor.close();
            }
        }
        cursor = query(uri, new String[] { "_data" }, null, null, null);
        if (cursor == null) {
            Log.v(Constants.TAG, "null cursor in openFile");
        } else {
            try {
                if (!cursor.moveToFirst()) {
                    Log.v(Constants.TAG, "empty cursor in openFile");
                } else {
                    String filename = cursor.getString(0);
                    Log.v(Constants.TAG, "filename in openFile: " + filename);
                    if (new java.io.File(filename).isFile()) {
                        Log.v(Constants.TAG, "file exists in openFile");
                    }
                }
            } finally {
                closeCursor(cursor);
            }
        }
    }

    private static void closeCursor(Cursor cursor) {
        if (cursor != null) {
            cursor.close();
        }
    }


    private static final void copyInteger(String key, ContentValues from, ContentValues to) {
        Integer i = from.getAsInteger(key);
        if (i != null) {
            to.put(key, i);
        }
    }

    private static final void copyLong(String key, ContentValues from, ContentValues to) {
        Long i = from.getAsLong(key);
        if (i != null) {
            to.put(key, i);
        }
    }

    private static final void copyBoolean(String key, ContentValues from, ContentValues to) {
        Boolean b = from.getAsBoolean(key);
        if (b != null) {
            to.put(key, b);
        }
    }

    private static final void copyString(String key, ContentValues from, ContentValues to) {
        String s = from.getAsString(key);
        if (s != null) {
            to.put(key, s);
        }
    }

    private static final void copyAllString(String key, ContentValues from, ContentValues to) {
        if (from.containsKey(key)) {
            String s = from.getAsString(key);
            to.put(key, s);
        }
    }

    private static final void copyStringWithDefault(String key, ContentValues from,
            ContentValues to, String defaultValue) {
        copyString(key, from, to);
        if (!to.containsKey(key)) {
            to.put(key, defaultValue);
        }
    }

    /**
     * Get a parameterized SQL WHERE clause to select a bunch of IDs.
     */
    static String getWhereClauseForId() {
        StringBuilder whereClause = new StringBuilder();
        whereClause.append("(" + Downloads.Impl._ID + " = ? )");
        return whereClause.toString();
    }

    /**
     * Get the selection args for a clause returned by
     * {@link #getWhereClauseForIds(long[])}.
     */
    static String[] getWhereArgsForId(long id) {
        String[] whereArgs = new String[1];
        whereArgs[0] = Long.toString(id);
        return whereArgs;
    }

    private static String getWhereClauseForStatuses(String[] operators, String[] jointConditions) {
        StringBuilder whereClause = new StringBuilder();
        whereClause.append("(");
        for (int i = 0; i < operators.length; i++) {
            if (i > 0) {
                whereClause.append(jointConditions[i - 1] + " ");
            }
            whereClause.append(Downloads.Impl.COLUMN_STATUS);
            whereClause.append(" " + operators[i] + " ? ");
        }
        whereClause.append(")");
        return whereClause.toString();
    }

    private static String[] getWhereArgsForStatuses(int[] statuses) {
        String[] whereArgs = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            whereArgs[i] = Integer.toString(statuses[i]);
        }
        return whereArgs;
    }

    /**
     * concatenate two arrays and return
     */
    private static <T> T[] concatArrays(T[] src1, T[] src2, Class<T> type) {
        T dst[] = (T[]) Array.newInstance(type, src1.length + src2.length);
        System.arraycopy(src1, 0, dst, 0, src1.length);
        System.arraycopy(src2, 0, dst, src1.length, src2.length);
        return dst;
    }

    private void logD(Object... msg) {
        Statistics.logD(TAG, msg);
    }

    private void grantAllDownloadsPermission(String toPackage, long id) {
        final Uri uri = ContentUris.withAppendedId(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, id);
        getContext().grantUriPermission(toPackage, uri,
                Intent.FLAG_GRANT_READ_URI_PERMISSION | Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
    }

    private void revokeAllDownloadsPermission(long id) {
        final Uri uri = ContentUris.withAppendedId(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, id);
        getContext().revokeUriPermission(uri, ~0);
    }

    private static  boolean isShowTaskWithUiAndNotification(String pkg) {
        return CloudConfigPreference.getInstance().isShowTaskWithUiAndNotification(pkg);
    }

    private boolean useXunleiEngine(String pkg,String appCfg) {
        return DownloadEngineRule.getInstance().useXunleiEngine(pkg,appCfg);
    }

    private boolean useKcgEngine(String appCfg) {
        return /*CloudConfigPreference.getInstance().useKcgEngine(appCfg)*/false;
    }

    private ContentValues convertToMediaProviderValues(ContentValues downloadValues) {
        final String filePath;
        try {
            filePath = new File(downloadValues.getAsString(Downloads.Impl._DATA))
                    .getCanonicalPath();
        } catch (IOException e) {
            throw new IllegalArgumentException(e);
        }
        final ContentValues mediaValues = new ContentValues();
        mediaValues.put(MediaStore.Downloads.DATA, filePath);
        mediaValues.put(MediaStore.Downloads.SIZE,
                downloadValues.getAsLong(Downloads.Impl.COLUMN_TOTAL_BYTES));
        mediaValues.put(MediaStore.Downloads.DOWNLOAD_URI,
                downloadValues.getAsString(Downloads.Impl.COLUMN_URI));
        mediaValues.put(MediaStore.Downloads.REFERER_URI,
                downloadValues.getAsString(Downloads.Impl.COLUMN_REFERER));
        mediaValues.put(MediaStore.Downloads.MIME_TYPE,
                downloadValues.getAsString(Downloads.Impl.COLUMN_MIME_TYPE));
        final boolean isPending = downloadValues.getAsInteger(Downloads.Impl.COLUMN_STATUS)
                != Downloads.Impl.STATUS_SUCCESS;
        mediaValues.put(MediaStore.Downloads.IS_PENDING, isPending ? 1 : 0);
        mediaValues.put(MediaStore.Downloads.OWNER_PACKAGE_NAME,
                Helpers.getPackageForUid(getContext(), downloadValues.getAsInteger(Constants.UID)));
        mediaValues.put(MediaStore.Files.FileColumns.IS_DOWNLOAD,
                downloadValues.getAsBoolean(COLUMN_IS_VISIBLE_IN_DOWNLOADS_UI));
        return mediaValues;
    }

}
