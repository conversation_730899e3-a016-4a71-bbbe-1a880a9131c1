
package com.android.providers.downloads.provider;

import java.util.HashSet;

import com.android.providers.downloads.activity.SizeLimitActivity;
import com.android.providers.downloads.config.DownloadColumn;
import com.android.providers.downloads.config.MobileDataConfig;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.statistics.Statistics;

import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Binder;
import android.os.Handler;
import android.text.TextUtils;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.util.NetworkUtils;
import com.android.providers.downloads.util.ProcessUtil;
import com.android.providers.downloads.util.XLDownloadHelper;
import com.android.providers.downloads.xunlei.speedup.XLSpeedUpManager;

public class DownLoadProviderUtils implements DownloadColumn {

    public static final String FROM_UI = "from_ui";

    public static long[] queryIds(SQLiteDatabase db, String table, String where, String[] whereArgs) {
        if (db == null || TextUtils.isEmpty(table)) {
            return null;
        }

        HashSet<Long> ids = new HashSet<Long>();
        Cursor cursor = null;
        try {
            cursor = db.query(table, null, where, whereArgs, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                int indexId = cursor.getColumnIndexOrThrow(COLUMN_ID);
                int indexLimit = cursor.getColumnIndexOrThrow(COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT);
                for (int i = 0; !cursor.isAfterLast(); i++, cursor.moveToNext()) {
                    int limit = cursor.getInt(indexLimit);
                    if (limit == 0) {
                        ids.add(cursor.getLong(indexId));
                    }
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        long[] idsArray = null;
        if (ids.size() > 0) {
            idsArray = new long[ids.size()];
            int i = 0;
            for (Long id : ids) {
                idsArray[i] = id;
                i++;
            }
        }
        return idsArray;
    }

    public static boolean isDownloadResume(Context context, ContentValues values, boolean fromUi) {
        //模拟非下载管理服务用户点击
        int pid = Binder.getCallingPid();
        Statistics.logD("isDownloadResume", "pid=" + pid, "myPid=" + android.os.Process.myPid());
        if (pid == android.os.Process.myPid() && !fromUi) {
            return false;
        }
        if (!Statistics.isMobileActive(context)) {
            Statistics.logD("isDownloadResume", "isMobileActive is false");
            return false;
        }

        if (MobileDataConfig.isUnlimited(context)) {
            Statistics.logD("isDownloadResume", "isUnlimited");
            return false;
        }
        return isExistsResume(values);
    }

    private static boolean isExistsResume(ContentValues values) {
        //是否是用户点击
        if (values == null) {
            return false;
        }
        if (values.containsKey(COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT)) {
            return false;
        }
        if (!values.containsKey(COLUMN_STATUS) || !values.containsKey(COLUMN_CONTROL)) {
            return false;
        }
        boolean statuIsRun = values.getAsInteger(COLUMN_STATUS) == STATUS_PENDING;
        boolean ctlIsRun = values.getAsInteger(COLUMN_CONTROL) == CONTROL_RUN;
        Statistics.logD("isExistsResume", "isExists is values＝" + values, " statuIsRun="
                + statuIsRun, " ctlIsRun=" + ctlIsRun);
        return statuIsRun && ctlIsRun;
    }

    public static void handleSizeLimite(Context context, long... ids) {
        if (context == null || ids == null || ids.length < 1) {
            return;
        }
        Statistics.logD("handleSizeLimite", ids);
        Long overLongSize = MobileDataConfig.getLimit(context);
        long overSize = overLongSize != null ? overLongSize.longValue() : -1;
        for (long id : ids) {
            if (needShowLimitDialog(context, id, overSize)) {
                notifyPauseDueToSize(context, id, false);
            }
        }
    }

    private static boolean needShowLimitDialog(Context context, long id,long maxOverSize) {
        if (context == null) {
            return false;
        }
        long fileSize = -2;
        int allowNetType = ~0;//默认允许所有网络
        String pkg = null;

        long[] ids = new long[]{
                id
        };
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver().query(mBaseUri, new String[]{COLUMN_ID,
                            COLUMN_NOTIFICATION_PACKAGE, COLUMN_TOTAL_SIZE_BYTES,
                            COLUMN_ALLOWED_NETWORK_TYPES}, getWhereClauseForIds(ids),
                    getWhereArgsForIds(ids), null);
            if (cursor != null) {
                int indexTotalSize = cursor.getColumnIndex(COLUMN_TOTAL_SIZE_BYTES);
                int indexPkg = cursor.getColumnIndex(COLUMN_NOTIFICATION_PACKAGE);
                int indexAllowNetType = cursor.getColumnIndex(COLUMN_ALLOWED_NETWORK_TYPES);


                for (cursor.moveToFirst(); !cursor.isAfterLast(); cursor.moveToNext()) {
                    fileSize = cursor.getLong(indexTotalSize);
                    pkg = cursor.getString(indexPkg);
                    allowNetType = cursor.getInt(indexAllowNetType);
                    break;
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        if (NetworkUtils.checkIsNetworkTypeDisallowed(context, allowNetType, pkg)) {
            return false;
        }

        return fileSize == -1 || fileSize > maxOverSize;
    }

    /**
     *
     * @param context
     * @param id
     * @param isWifiRequired
     * @param delayMillis
     */
    public static void notifyPauseDueToSize(final Context context,final long id, final boolean isWifiRequired,long delayMillis) {
        Statistics.logD("notifyPauseDueToSize", "id=" + id, "isWifiRequired=" + isWifiRequired + " delayMillis=" + delayMillis);
        DownloadApplication app = (DownloadApplication) context.getApplicationContext();
        Handler h = app.getWorkHandler();
        h.postDelayed(new Runnable() {
            @Override
            public void run() {
                notifyPauseDueToSize(context, id, isWifiRequired);
            }
        }, delayMillis);
    }

    public static void notifyPauseDueToSize(Context context, long id, boolean isWifiRequired) {
        if (context == null && id >= 0) {
            return;
        }

        Statistics.logD("notifyPauseDueToSize", "id=" + id, "isWifiRequired=" + isWifiRequired);
        Statistics.logD("notifyPauseDueToSize", "pid=" + Binder.getCallingPid(), "myPid=" + android.os.Process.myPid());
        Statistics.logD("notifyPauseDueToSize", Statistics.getStackTraceMsg());
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setData(ContentUris.withAppendedId(mAllDownloadsUri, id));
        intent.setClass(context, SizeLimitActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static String getWhereClauseForIds(long[] ids) {
        if (ids == null || ids.length <= 0) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("(");
        for (int i = 0; i < ids.length; i++) {
            if (i > 0) {
                sb.append("OR ");
            }
            sb.append(String.format("%s = ?", COLUMN_ID));
        }
        sb.append(")");
        return sb.toString();
    }

    public static String[] getWhereArgsForIds(long[] ids) {
        if (ids == null || ids.length <= 0) {
            return null;
        }
        String[] whereArgs = new String[ids.length];
        for (int i = 0; i < ids.length; i++) {
            whereArgs[i] = Long.toString(ids[i]);
        }
        return whereArgs;
    }

    public static void passRecommendedSizeLimit(Context context, long[] ids) {
        if (context == null || ids == null || ids.length <= 0) {
            return;
        }
        Statistics.logD("passRecommendedSizeLimit", ids);
        ContentValues values = new ContentValues();
        addRunningStatusAndControlRun(values);
        values.put(COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT, true);
        values.put(COLUMN_ALLOWED_NETWORK_TYPES, -1);
        values.put(COLUMN_ALLOW_METERED, true);
        values.put(COLUMN_ALLOW_ROAMING, true);
        context.getContentResolver().update(mAllDownloadsUri, values, getWhereClauseForIds(ids),
                getWhereArgsForIds(ids));
    }

    public static void addRunningStatusAndControlRun(ContentValues values) {
        if (values != null) {
            values.put(COLUMN_STATUS, STATUS_PENDING);
            values.put(COLUMN_CONTROL, CONTROL_RUN);
        }
    }

    /**
     * 暂停下载操作
     *
     * @param filteredValues
     * @return
     */
    public static boolean isPauseOper(ContentValues filteredValues) {

        if (!filteredValues.containsKey(COLUMN_CONTROL) || !filteredValues.containsKey(COLUMN_STATUS)) {
            return false;
        }
        return filteredValues.getAsInteger(COLUMN_STATUS) == STATUS_PAUSE;

    }

    /**
     * 恢复下载操作
     * @param filteredValues
     * @return
     */
    public static boolean isResumeOper(ContentValues filteredValues) {

        if (!filteredValues.containsKey(COLUMN_CONTROL) || !filteredValues.containsKey(COLUMN_STATUS)) {
            return false;
        }
        return filteredValues.getAsInteger(COLUMN_STATUS) == STATUS_PENDING;

    }

    /**
     * 重新开始下载操作
     * @param filteredValues
     * @return
     */
    public static boolean isReStartOper(ContentValues filteredValues) {

        if (!filteredValues.containsKey(COLUMN_STATUS) || !filteredValues.containsKey(COLUMN_FAILED_CONNECTIONS)) {
            return false;
        }
        return filteredValues.getAsInteger(COLUMN_STATUS) == STATUS_PENDING;

    }

    public static void delete(Context context, long[] ids) {
        if (context == null || ids == null || ids.length <= 0) {
            return;
        }

        context.getContentResolver().delete(mBaseUri, getWhereClauseForIds(ids),
                getWhereArgsForIds(ids));
    }


    public static void updateTrySpeedUp(long mInfoID,int mStatus,int mRemaintime,int duration,int mVipMode) {
        ContentValues values = new ContentValues();
        values.put(XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS, mStatus);
        values.put(XLDownloadCfg.COLUMN_TRYSPEEDUP_MODE, mVipMode);
        values.put(XLDownloadCfg.COLUMN_TRYSPEEDUP_TIME, mRemaintime < 0 ? duration+":"+0+":0" : duration+":"+mRemaintime+":0");
        DownloadApplication.getGlobalApplication().getContentResolver().update(mAllDownloadsUri, values, COLUMN_ID+" = ?",
                new String[]{mInfoID+""});
    }

    public static void updateTrySpeedUpStatus(long mInfoID,int mStatus) {
        ContentValues values = new ContentValues();
        values.put(XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS, mStatus);
        DownloadApplication.getGlobalApplication().getContentResolver().update(mAllDownloadsUri, values, COLUMN_ID+" = ?",
                new String[]{mInfoID+""});
    }

    public static void updateTrySpeedUpSpeed(long mInfoID,long Speed) {
        ContentValues values = new ContentValues();
        values.put(XLDownloadCfg.COLUMN_XL_ACCELERATE_SPEED, Speed);
        DownloadApplication.getGlobalApplication().getContentResolver().update(mAllDownloadsUri, values, COLUMN_ID+" = ?",
                new String[]{mInfoID+""});
    }


    public static void fillSpeedUpTimeValues(ContentValues mValues,int mhasUsedTime,int duration,long mPercent) {
        mValues.put(DownloadProvider.COLUMN_TRYSPEEDUP_TIME, (duration - mhasUsedTime) < 0 ? duration + ":" + 0 +":"+mPercent : duration + ":" + (duration - mhasUsedTime)+":"+mPercent);
    }

    public static void fillSpeedUpStautsValues(ContentValues mValues,int mStatus) {
        mValues.put(DownloadProvider.COLUMN_TRYSPEEDUP_STATUS, mStatus);
    }

    public static int getSpeedUpStatus(long mInfoId){
        Cursor cursor = null;
        int mStatus = 0;
        try {
            cursor = DownloadApplication.getGlobalApplication().getContentResolver().query(mBaseUri, new String[]{XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS}, COLUMN_ID+" = ?",
                    new String[]{mInfoId+""}, null);
            if (cursor != null && cursor.moveToFirst()) {
                mStatus = cursor.getInt(0);
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return mStatus;
    }


    public static void recoveryTrySpeedUpStatus() {
        //0 加速检测 1 不能加速 2.可以加速 3.等待加速被确认 4.加速确认 5.正在加速 6.加速完成等待确认 7 加速完成确定 8 加速完成
        ContentValues values = new ContentValues();
        values.put(XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS, XLSpeedUpManager.TASK_SPEEDUP_SUCESS);
        if(XLSpeedUpManager.getSpeedUpManager().isInVipMode()){
            DownloadApplication.getGlobalApplication().getContentResolver().update(mAllDownloadsUri, values,XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS+">=?",
                    new String[]{XLSpeedUpManager.TASK_SPEEDUP_RUNNING+""});
        }else {
            values.clear();
            values.put(XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS, XLSpeedUpManager.TASK_SUCESS_WAIT_CONFIRM);
            DownloadApplication.getGlobalApplication().getContentResolver().update(mAllDownloadsUri, values,XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS+"=?",
                    new String[]{XLSpeedUpManager.TASK_SPEEDUP_RUNNING+""});
        }
        values.clear();
        values.put(XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS, -1);
        DownloadApplication.getGlobalApplication().getContentResolver().update(mAllDownloadsUri, values, XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS+" < ? AND "+XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS+" > ?",
                new String[]{XLSpeedUpManager.TASK_SPEEDUP_RUNNING+"",-1+""});
    }

    public static void changeGSSYStatus(int originStauts,int newStatus){
        ContentValues values = new ContentValues();
        values.put(XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS, newStatus);
        DownloadApplication.getGlobalApplication().getContentResolver().update(mAllDownloadsUri, values,XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS+"=?",
                new String[]{originStauts+""});
    }


    public static int getSpeedUpId(int status){
        Cursor cursor = null;
        int mStatus = 0;
        try {
            cursor = DownloadApplication.getGlobalApplication().getContentResolver().query(mBaseUri, new String[]{COLUMN_ID}, XLDownloadCfg.COLUMN_TRYSPEEDUP_STATUS+" = ?",
                    new String[]{status+""}, null);
            if (cursor != null && cursor.moveToFirst()) {
                mStatus = cursor.getInt(0);
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return mStatus;
    }

    public static void collectNeedHandIds(SQLiteDatabase db,String where, String[] whereArgs,HashSet<Long> limitIds,HashSet<Long> btIds) {
        if (db == null) {
            return;
        }

        Cursor cursor = null;
        try {
            cursor = db.query(DownloadProvider.DB_TABLE, new String[]{COLUMN_ID, COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT, CONTROL_URL},
                    where, whereArgs, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                int indexId = cursor.getColumnIndexOrThrow(COLUMN_ID);
                int indexLimit = cursor.getColumnIndexOrThrow(COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT);
                int indexUrl = cursor.getColumnIndexOrThrow(CONTROL_URL);
                long id = 0;
                int limit = -1;
                String url = null;
                for (int i = 0; !cursor.isAfterLast(); i++, cursor.moveToNext()) {
                    id = cursor.getLong(indexId);
                    limit = cursor.getInt(indexLimit);
                    if (limit == 0) {
                        limitIds.add(id);
                    }
                    if (btIds != null) {
                        url = cursor.getString(indexUrl);
                        if (XLDownloadHelper.isBtTask(url)) {
                            btIds.add(id);
                        }
                    }

                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    public static long[] setToArray(HashSet<Long> set) {
        if (set == null || set.size() <= 0) {
            return null;
        }
        long[] idsArray = new long[set.size()];
        int i = 0;
        for (Long id : set) {
            idsArray[i] = id;
            i++;
        }
        return idsArray;
    }

    public static boolean allowControlDownload(Context context) {
        String callingPkgName = ProcessUtil.getCallingPkgName(context);
        //将游戏中心，加入白名单
        if ("com.xiaomi.gamecenter".equals(callingPkgName) ||
            "com.blackshark.bsamagent".equals(callingPkgName) ||
            "com.android.providers.downloads.ui".equals(callingPkgName)) {
            return true;
        }
        boolean systemApp = ProcessUtil.isSystemApp(context, callingPkgName);
        if (!systemApp) {
            XLConfig.LOGD(XLDownloadCfg.TAG,"allowControlDownload callingPkgName:"+callingPkgName +" systemApp="+systemApp);
        }
        return systemApp;
    }

    public static boolean fromUi(ContentValues contentValues) {
        boolean fromUi = false;
        try {
            Object from_ui = contentValues.get(FROM_UI);
            if (from_ui != null) {
                contentValues.remove(FROM_UI);
                fromUi = (boolean) from_ui;
            }
        } catch (Exception e){}
        return fromUi;
    }

}
