package com.android.providers.downloads.statistics;

import static com.android.providers.downloads.statistics.Statistics.EVENT_ATTR_HTTPS_AUTHTYPE_UNKNOWN;
import static com.android.providers.downloads.statistics.Statistics.EVENT_ATTR_HTTPS_GETCERTIFICATEFACTORYFAIL;
import static com.android.providers.downloads.statistics.Statistics.EVENT_ATTR_HTTPS_HOSTNAME_FAIL;
import static com.android.providers.downloads.statistics.Statistics.EVENT_ATTR_HTTPS_OPENSSLX509CERTIFICATEFAIL;
import static com.android.providers.downloads.statistics.Statistics.EVENT_ATTR_HTTPS_RESPONSE_FAIL;
import static com.android.providers.downloads.statistics.Statistics.EVENT_ATTR_HTTPS_VERITY_FAIL;

import android.app.ActivityManager;
import android.content.Context;
import android.text.TextUtils;

import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.model.State;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.statistics.platform.OneTrackPlatform;
import com.android.providers.downloads.statistics.platform.TrackPlatform;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.XLDownloadHelper;
import com.android.providers.downloads.util.XLUtil;
import com.android.providers.downloads.utils.LogUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by lxy on 2017/10/12.
 */

public class TraceReport implements XLDownloadCfg {
    private final static String EVENT_DOWNLOAD_START = "download_start";
    private final static String EVENT_DOWNLOAD_END = "download_end";
    private final static String EVENT_DOWNLOAD_END_FAIL = "download_end_fail";
    private final static String EVENT_DOWNLOAD_CONNECTIONS = "download_connections";
    private final static String EVENT_DOWNLOAD_SPEEDUP = "download_speedup";

    private final static String EVENT_KCG_DOWNLOAD_START = "kcg_download_start";
    private final static String EVENT_KCG_DOWNLOAD_END = "kcg_download_end";
    private final static String EVENT_KCG_DOWNLOAD_END_FAIL = "kcg_download_end_fail";
    private final static String COMMON_MIUI_BIG_VERSION = "miui_big_version";
    public final static String EVENT_USE_SETEXTRA2_USEENGINE = "use_setExtra2_useEngine";
    private static boolean isInit = false;
    private static TrackPlatform mTrack;
    private static String mDeviceID;

    public static void init(Context context) {
        try {
            mTrack = new OneTrackPlatform();
            mTrack.init(context);
            isInit = true;
        } catch (Exception e){
            isInit = false;
            LogUtil.d("init exc", e);
        }
    }

    public static void reportServiceStart(Context context) {
        if (!isInit) {
            return;
        }
        //MiStat.trackPageStart("DownloadService");
    }

    public static void reportServiceEnd(Context context) {
        if (!isInit) {
            return;
        }
        //MiStat.trackPageEnd("DownloadService");
    }

    public static void reportDownloadStart(Context context, DownloadItem item, long currentSize) {
        if (!isInit) {
            return;
        }
        Map<String, String> trackData = buildCommonData(context, item);
        trackData.put("current_size", String.valueOf(currentSize));
        reportCountEvent(EVENT_DOWNLOAD_START, trackData);
    }

    public static void reportKcgDownloadStart(Context context, DownloadItem item, long currentSize) {
        if (!isInit) {
            return;
        }
        Map<String, String> trackData = buildCommonData(context, item);
        trackData.put("current_size", String.valueOf(currentSize));
        reportCountEvent(EVENT_KCG_DOWNLOAD_START, trackData);
    }

    public static void reportKcgDownloadEnd(Context context, DownloadItem item, int finalStatus, int soRet, String errorMsg, long downloadSize, long downloadTime) {
        if(!isInit){
            return;
        }
        Map<String, String> trackData = buildCommonData(context, item);
        boolean isFail = false;
        String errorcode = String.valueOf(finalStatus);
        switch (finalStatus) {
            case STATUS_SUCCESS:
            case STATUS_PAUSED_BY_APP:
            case STATUS_CANCELED:
            case STATUS_QUEUED_FOR_WIFI:
            case STATUS_WAITING_FOR_NETWORK:
                isFail = false;
                break;
            case XL_STATUS__FAIL:
                isFail = true;
                errorcode = XL_STATUS__FAIL + "_" + soRet;
                break;
            default:
                LogUtil.d(TAG, "finalStatus=" + finalStatus + "errorMsg=" + errorMsg);
                isFail = true;
                break;
        }

        trackData.put("error_code", getNotEmptyString(errorcode));
        trackData.put("error_msg", getNotEmptyString(errorMsg));
        trackData.put("download_duration",
                String.valueOf(downloadTime > 0 ? downloadTime : 0));
        trackData.put("download_size", String.valueOf(downloadSize > 0 ? downloadSize : 0));
        if (isFail) {
            reportKCGDownloadFail(item, errorcode, errorMsg);
        }
        reportCountEvent(EVENT_KCG_DOWNLOAD_END, trackData);
    }

    public static void reportKCGDownloadFail(DownloadItem item,String errorCode, String errorMsg) {
        if(!isInit){
            return;
        }
        Map<String, String> trackData = new HashMap<String, String>();
        int taskId = generateUniqueId(item.mId, item.mRequestUri);
        trackData.put("task_id", String.valueOf(taskId));
        trackData.put("url", getNotEmptyString(item.mRequestUri));
        trackData.put("from_package", getNotEmptyString(item.mPackage));
        trackData.put("use_kcg", getBooleanString(item.mIsUseKcg));
        trackData.put("error_code", getNotEmptyString(errorCode));
        trackData.put("error_msg", getNotEmptyString(errorMsg));
        trackData.put("lib_version", BuildUtils.getSoVersion());
        reportCountEvent(EVENT_KCG_DOWNLOAD_END_FAIL, trackData);
    }

    public static void reportDownloadEnd(Context context, DownloadItem item, int finalStatus, int soRet, String errorMsg, long downloadSize, long downloadTime) {
        if(!isInit){
            return;
        }
        Map<String, String> trackData = buildCommonData(context, item);
        boolean isFail = false;
        String errorcode = String.valueOf(finalStatus);
        switch (finalStatus) {
            case STATUS_SUCCESS:
            case STATUS_PAUSED_BY_APP:
            case STATUS_CANCELED:
            case STATUS_QUEUED_FOR_WIFI:
            case STATUS_WAITING_FOR_NETWORK:
                isFail = false;
                break;
            case XL_STATUS__FAIL:
                isFail = true;
                errorcode = XL_STATUS__FAIL + "_" + soRet;
                break;
            default:
                LogUtil.d(TAG, "finalStatus=" + finalStatus + "errorMsg=" + errorMsg);
                isFail = true;
                break;
        }

        trackData.put("error_code", getNotEmptyString(errorcode));
        trackData.put("error_msg", getNotEmptyString(errorMsg));
        trackData.put("download_duration",
                String.valueOf(downloadTime > 0 ? downloadTime : 0));
        trackData.put("download_size", String.valueOf(downloadSize > 0 ? downloadSize : 0));
        if (isFail) {
            reportDownloadFail(item, errorcode, errorMsg);
        }
        reportCountEvent(EVENT_DOWNLOAD_END, trackData);
    }

    public static void reportDownloadFail(DownloadItem item,String errorCode, String errorMsg) {
        if(!isInit){
           return;
        }
        Map<String, String> trackData = new HashMap<String, String>();
        int taskId = generateUniqueId(item.mId, item.mRequestUri);
        trackData.put("task_id", String.valueOf(taskId));
        trackData.put("url", getNotEmptyString(item.mRequestUri));
        trackData.put("from_package", getNotEmptyString(item.mPackage));
        trackData.put("use_xunlei", getBooleanString(item.mIsUseXunlei));
        trackData.put("error_code", getNotEmptyString(errorCode));
        trackData.put("error_msg", getNotEmptyString(errorMsg));
        trackData.put("lib_version", BuildUtils.getSoVersion());
        trackData.put("file_optimize", String.valueOf(item.mFileOptimize));
        reportCountEvent(EVENT_DOWNLOAD_END_FAIL, trackData);
    }

    public static void reportHttpsHostNameVerityFail(Context context, DownloadItem item, String hostName, String subjectX500Principal, String certificateString) {
        if (!isInit) {
            return;
        }
        Map<String, String> trackData = buildCommonData(context, item);
        trackData.put("hostName", hostName == null ? "" : hostName);
        trackData.put("principal",subjectX500Principal);
        trackData.put("cert",certificateString);
        reportCountEvent(EVENT_ATTR_HTTPS_HOSTNAME_FAIL, trackData);

    }

    public static void reportHttpsAuthTypeUnknown(Context context,DownloadItem item,String hostName) {
        if (!isInit) {
            return;
        }
        Map<String, String> trackData = buildCommonData(context, item);
        trackData.put("hostName", hostName == null ? "" : hostName);
        trackData.put("authType", "UNKNOWN");
        reportCountEvent(EVENT_ATTR_HTTPS_AUTHTYPE_UNKNOWN, trackData);
    }
    public static void reportHttpsResponseFail(Context context, DownloadItem item, String resourceLine) {
        if (!isInit) {
            return;
        }
        Map<String, String> trackData = buildCommonData(context, item);
        trackData.put("resourceLine", resourceLine == null ? "" : resourceLine);
        reportCountEvent(EVENT_ATTR_HTTPS_RESPONSE_FAIL, trackData);
    }

    public static void reportGetCertificateFactoryFail(HashMap<String, String> trackData) {
        if (!isInit) {
            return;
        }
        reportCountEvent(EVENT_ATTR_HTTPS_GETCERTIFICATEFACTORYFAIL, trackData);
    }
    public static void reportOpenSSLX509CertificateFail(HashMap<String, String> trackData) {
        if (!isInit) {
            return;
        }
        reportCountEvent(EVENT_ATTR_HTTPS_OPENSSLX509CERTIFICATEFAIL, trackData);
    }

    public static void reportHttpsVerityFail(Context context, String authType, String hostName, DownloadItem item, String failReason, String subjectX500Principal, String certificateString) {
        if (!isInit) {
            return;
        }
        Map<String, String> trackData = buildCommonData(context, item);
        trackData.put("hostName", hostName == null ? "" : hostName);
        trackData.put("authType", authType == null ? "" : authType);
        trackData.put("failReason",failReason == null ? "" : failReason);
        trackData.put("principal",subjectX500Principal);
        trackData.put("cert",certificateString);
        reportCountEvent(EVENT_ATTR_HTTPS_VERITY_FAIL, trackData);
    }
    public static void reportUseSetExtra2(long id,String url,String packageName, String useEngine, String endEngine) {
        if(!isInit){
            return;
        }
        Map<String, String> trackData = new HashMap<String, String>();
        int taskId = generateUniqueId(id,url);
        trackData.put("task_id", String.valueOf(taskId));
        trackData.put("package_name", packageName);
        trackData.put("endEngine", endEngine);
        trackData.put("useEngine", useEngine);
        reportCountEvent(EVENT_USE_SETEXTRA2_USEENGINE, trackData);
    }

    public static void trackDownloadConn(Context context, State item, HashMap<String, String> userData) {
        if (userData == null) {
            return;
        }
        HashMap<String, String> trackData = userData;
        int uniqueId = generateUniqueId(item.mId, item.mRequestUri);
        trackData.put("seq_id", String.valueOf(uniqueId));
        reportCountEvent(EVENT_DOWNLOAD_CONNECTIONS, userData);
    }

    public static void reportDcdnOpen(String url) {
        if (!isInit) {
            return;
        }
//        Map<String, String> trackData = new HashMap<String, String>();
//        trackData.put("url", getNotEmptyString(url));
//        String token = DownloadSettings.XLShareConfigSettings.getXLVipToken("");//只有非会员加速才从dcdn通道
//        trackData.put("dcdn_open", getBooleanString(TextUtils.isEmpty(token)));
//        reportCountEvent(EVENT_DOWNLOAD_SPEEDUP, trackData);
    }

    private static Map<String, String> buildCommonData(Context context, DownloadItem item) {
        Map<String, String> trackData = new HashMap<String, String>();
        int networkType = Statistics.getNetworkType(context);
        int taskId = generateUniqueId(item.mId, item.mRequestUri);
        trackData.put("task_id", String.valueOf(taskId));
        trackData.put("url", getNotEmptyString(item.mRequestUri));
        trackData.put("file_name", getNotEmptyString(item.mFileName));
        trackData.put("file_size", String.valueOf(item.mTotalBytes));
        trackData.put("from_package", getNotEmptyString(item.mPackage));
        trackData.put("use_xunlei", getBooleanString(item.mIsUseXunlei));
        trackData.put("is_vip", isVip() ? "1" : "0");
        trackData.put("network_type", String.valueOf(networkType));
        if (item.mProtocol == DownloadType.M3U8.ordinal()) {
            trackData.put("percent", String.valueOf(item.mPercent));
        }
        trackData.put("protocol", XLDownloadHelper.getProtocol(item.mProtocol));
        String vCode = XLUtil.getAppVersionCode(item.mPackage);
        String vName = XLUtil.getAppVersionName(item.mPackage);
        trackData.put("origin_version_code", vCode);
        trackData.put("origin_version_name", vName);
        trackData.put("wakelock_time", String.valueOf(item.mWakelockTime));
        trackData.put("file_optimize", String.valueOf(item.mFileOptimize));
        return trackData;
    }

    private static StringBuilder buildCommonDataStr(Context context, DownloadItem item) {
        Map<String, String> trackData = new HashMap<String, String>();
        int networkType = Statistics.getNetworkType(context);
        int taskId = generateUniqueId(item.mId, item.mRequestUri);

        StringBuilder builder = new StringBuilder();

        builder.append("task_id:"+String.valueOf(taskId));
        builder.append("_");
        builder.append("url:" + getNotEmptyString(item.mRequestUri));
        builder.append("_");
        builder.append("file_name:"+ getNotEmptyString(item.mFileName));
        builder.append("_");
        builder.append("file_size:"+ String.valueOf(item.mTotalBytes));
        builder.append("_");
        builder.append("net:"+ String.valueOf(networkType));
        return builder;
    }

    public static int generateUniqueId(long id, String url) {
        String str = id + url;
        int code = str.hashCode();
        return code;
    }

    private static boolean isVip(){
        String vipToken = DownloadSettings.XLShareConfigSettings.getXLVipToken("");
        return !TextUtils.isEmpty(vipToken);
    }

    public static String getBooleanString(boolean yes) {
        return yes ? "1" : "0";
    }

    public static String getNotEmptyString(String str) {
        return TextUtils.isEmpty(str) ? "" : str;
    }

    public static void reportCountEvent(String eventName, Map<String, String> trackData) {
        if (isMonkeyRunning()) {
            return;
        }
        if (!isInit) {
            return;
        }
        if (!CloudConfigPreference.getInstance().traceOneTrackOpen()) {
            return;
        }

        if (trackData == null) {
            trackData = new HashMap<String, String>();
        }
        addCommonData(trackData);
        printReportEvent(eventName, trackData);

        if (mTrack != null) {
            mTrack.track(eventName, trackData);
        }
    }

    private static void addCommonData(Map<String, String> trackData) {
        trackData.put(COMMON_MIUI_BIG_VERSION, BuildUtils.getBigMiuiVersion());
    }

    private static void printReportEvent(String eventName, Map<String, String> trackData) {
        if (!isDebug()) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("|>>>>>>>>>>>>>\n");
        sb.append("|>reportEvent " + eventName + "\n");
        if (trackData != null && trackData.size() > 0) {
            sb.append("|>trackData:\n");
            for (Map.Entry<String, String> entry : trackData.entrySet()) {
                sb.append(entry.getKey() + "=" + entry.getValue() + "\n");
            }
        }
        sb.append("|><<<<<<<<<<<<");
        print(sb.toString());
    }

    public static void print(String msg) {
        LogUtil.logTag(LogUtil.TAG_MI_STAT, msg);
    }

    public static boolean isDebug() {
        return LogUtil.showMiStat();
    }

    private static boolean isMonkeyRunning() {
        return ActivityManager.isUserAMonkey();
    }

    public static String getDeviceID() {
        return loadDeviceID();
    }

    public static String loadDeviceID() {
        if (TextUtils.isEmpty(mDeviceID)) {
            mDeviceID = mTrack.getDeviceID();
        }
        return mDeviceID;
    }

    public static String getDeviceIDFromTrack() {
        return mDeviceID;
    }

    public static void setNetworkAccessEnabled(boolean provisioned) {
        if (!isInit) {
            return;
        }
        if (mTrack != null) {
            mTrack.setNetworkAccessEnabled(provisioned);
        }
    }

    public static void reportExpoFromHub(String clientId,String behaviorId, Map<String, String> trackData) {
        if (trackData != null) {
            trackData.put("client_id",clientId);
        }
        reportCountEvent(behaviorId, trackData);
    }
}
