
package com.android.providers.downloads.statistics;

import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Binder;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.RealSystemFacadeInjector;
import com.android.providers.downloads.SystemFacade;
import com.android.providers.downloads.config.ConstInfo;
import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.model.State;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.DeviceIdGenerator;
import com.android.providers.downloads.util.DownloadExtra2;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.NetworkUtils;
import com.android.providers.downloads.util.ProcessUtil;
import com.android.providers.downloads.util.Util;
import com.android.providers.downloads.util.XLDownloadHelper;
import com.android.providers.downloads.util.XLUtil;
import com.android.providers.downloads.xunlei.notifyrecommend.NotifyRecommendManager;
import com.michael.corelib.coreutils.Environment;

import miui.os.Build;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.text.TextUtils;

import org.json.JSONException;
import org.json.JSONObject;

import static com.android.providers.downloads.statistics.TraceReport.EVENT_USE_SETEXTRA2_USEENGINE;

public class Statistics implements XLDownloadCfg {

    public static final String TAG = "statistics";
    private static final String EVENT_CLIENT = "download_xunlei_event_v3";
    private static final String EVENT_NAME_DOWNLOAD_START = "download_start";
    private static final String EVENT_NAME_DOWNLOAD_STOP = "download_stop";
    private static final String EVENT_NAME_DOWNLOAD_CONNECTIONS = "download_connections";
    private static final String EVENT_ID_DOWNLOAD = "download_event";
    private static final String EVENT_AD_DATA = "ad_data";
    private static final String EVENT_ID_INTERFACE_EVENT = "interface_event";
    private static final String EVENT_ATTR_API_INTERFACE_PERFORMANCE_REPORT= "api_interface_performance_report";

    private static final String EVENT_ID_DOWNLOAD_OTHER_EVENT = "download_other";
    private static final String EVENT_ATTR_PROCESS_START = "download_service_process_start";
    private static final String EVENT_ATTR_SERVICE_START = "download_service_start";
    private static final String EVENT_ATTR_METHOD_CALL = "download_method_call";
    private static final String EVENT_ATTR_LIMIT_SPEED_START = "download_speed_limit_start";
    private static final String EVENT_ATTR_LIMIT_SPEED_STOP = "download_speed_limit_stop";
    public static final String EVENT_ATTR_HTTPS_HOSTNAME_FAIL = "event_attr_https_hostname_fail";
    public static final String EVENT_ATTR_HTTPS_AUTHTYPE_UNKNOWN = "event_attr_https_authtype_unknown";
    public static final String EVENT_ATTR_HTTPS_VERITY_FAIL = "event_attr_https_verity_fail";
    public static final String EVENT_ATTR_HTTPS_RESPONSE_FAIL = "event_attr_https_response_fail";
    private static final String EVENT_ID_AD_MARTKET_DOWNLOAD_BEGIN = "ad_event_id_martket_download_begin";
    private static final String EVENT_ID_AD_DIRECT_DOWNLAOD = "ad_event_id_direct_download";
    private static final String EVENT_ID_AD_DETAIL_DOWNLAOD = "ad_event_id_detail_download";

    private static final String EVENT_ID_SPEEDUP_VIP_CLICK= "accredit_trial_click";
    private static final String EVENT_ID_SPEEDUP_TIPS_SHOW = "accredit_trial_expo";


    private static final String EVENT_ID_NOTIFY_RECOMMEND_SHOW= "notification_bar_show";
    private static final String EVENT_ID_NOTIFY_RECOMMEND_CLICK = "notification_bar_click";

    public static final String EVENT_ID_CLICK_EVENT = "click_event";
    public static final String EVENT_ATTR_CLICK_POP_MOBILE_DATA_OVERFLOW = "click_pop_mobile_data_overflow";
    public static final String EVENT_ATTR_CLICK_POP_MOBILE_DATA_OVERFLOW_CHANGE_SETTING = "click_pop_mobile_data_overflow_change_setting";
    public static final String EVENT_ATTR_CLICK_POP_MOBILE_DATA_OVERFLOW_SHOW = "click_pop_mobile_data_overflow_show";

    public static final String EVENT_ATTR_SPEEDUP = "download_speedup";
    public static final String EVENT_ATTR_API_MONITOR = "api_monitor";

    /**
     * 流量限制弹框统计点
     */
    private static final String EVENT_ID_DATA_LIMIT_DIALOG_SHOW = "download_prompt_window_expo";
    private static final String EVENT_ID_DATA_LIMIT_DIALOG_CLICK = "download_prompt_window_click";

    private static final String EVENT_ID_AD_OPEN_MARKET = "ad_open_market";
    public static final String PRODUCT_NAME = XLConfig.PRODUCT_NAME;
    public static final String EVENT_ATTR_HTTPS_GETCERTIFICATEFACTORYFAIL = "event_attr_https_getcertificatefactoryfail";
    public static final String EVENT_ATTR_HTTPS_OPENSSLX509CERTIFICATEFAIL = "event_attr_https_opensslx509certificatefail";

    public static int generateUniqueId(Context context, State state) {
        return Statistics.generateUniqueId(context, state.mId, state.mFileCreateTime,
                state.mRequestUri);
    }

    /**
     * 采用时间戳+任务ID+url计算签名的方式，保证TaskID在统一用户下的唯一性
     */
    public static int generateUniqueId(Context context, long id, long fileCreateTime, String url) {
        return generateUniqueId(id, url);
    }

    private static int generateUniqueId(long id, String url) {
        String str = id + url;
        int code = str.hashCode();
        return code;
    }

    public static void reportDetailDownload(Context context, String appId, long taskId) {
        if (TextUtils.isEmpty(appId)) {
            return;
        }

        Map<String, String> trackData = getCommonReports(context, EVENT_ID_AD_DETAIL_DOWNLAOD);
        trackData.put("app_id", appId);
        trackData.put("download_seq_id", String.valueOf(taskId));
        trackData.put("opr_time", String.valueOf(System.currentTimeMillis() / 1000));
        traceReport(context, EVENT_AD_DATA, trackData);
    }

    public static void reportDirectDownload(Context context, String appId, long taskId) {
        if (TextUtils.isEmpty(appId)) {
            return;
        }

        Map<String, String> trackData = getCommonReports(context, EVENT_ID_AD_DIRECT_DOWNLAOD);
        trackData.put("app_id", appId);
        trackData.put("download_seq_id", String.valueOf(taskId));
        trackData.put("opr_time", String.valueOf(System.currentTimeMillis() / 1000));
        traceReport(context, EVENT_AD_DATA, trackData);
    }

    public static Map<String, String> getCommonReports(Context context, String event_id) {
        Map<String, String> trackData = new HashMap<String, String>();
        trackData.put("miui_version", Build.VERSION.INCREMENTAL);
        trackData.put("product_id", "18");
        trackData.put("ad_version", "2");
        trackData.put("channel", "xiaomi");
        trackData.put("phone_type", Build.MODEL);
        trackData.put("event_id", String.valueOf(event_id));
        trackData.put("network_type", String.valueOf(getNetworkType(context)));
        trackData.put("mac", ConstInfo.getValue(context, ConstInfo.ConstKey.PHONE_MAC));
        trackData.put("os_version", ConstInfo.getValue(context, ConstInfo.ConstKey.SDK_VERSION));
        trackData.put(
                "device_id",
                getMd5Digest(ConstInfo.getValue(context, ConstInfo.ConstKey.PHONE_IMEI)
                        + ConstInfo.getValue(context, ConstInfo.ConstKey.PHONE_MAC)));
        trackData.put("client_ip", ConstInfo.getValue(context, ConstInfo.ConstKey.PHONE_IP));
        trackData.put("operator", ConstInfo.getValue(context, ConstInfo.ConstKey.OPERATORS));
        trackData.put("app_version", ConstInfo.getValue(context, ConstInfo.ConstKey.APP_VERSION));

        return trackData;
    }

    public static String getMd5Digest(String pInput) {
        try {
            MessageDigest lDigest = MessageDigest.getInstance("MD5");
            lDigest.update(getBytes(pInput));
            BigInteger lHashInt = new BigInteger(1, lDigest.digest());
            return String.format("%1$032x", lHashInt);
        } catch (NoSuchAlgorithmException lException) {
            throw new RuntimeException(lException);
        }
    }

    private static byte[] getBytes(String s) {
        try {
            return s.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            return s.getBytes();
        }
    }

    private static HashMap<String, String> getCommon(Context context) {
        HashMap<String, String> trackData = new HashMap<String, String>();
        try {
            int networkType = getNetworkType(context);
            String time = Long.toString(System.currentTimeMillis() / 1000);
            trackData.put("product_name", PRODUCT_NAME);
            trackData.put("product_version", XLConfig.getVersionName(context));
            trackData.put("miui_version", Build.VERSION.INCREMENTAL);
            trackData.put("phone_type", Build.MODEL);
            trackData.put("system_version", android.os.Build.VERSION.RELEASE);
            trackData.put("network_type", Integer.toString(networkType));
            trackData.put("time", time);
        } catch (Exception exc) {
        }
        return trackData;
    }

    private static void reportHub(Context context, String eventId, String eventName, HashMap<String, String> trackData) {
        if (context == null || TextUtils.isEmpty(eventId) || trackData == null) {
            return;
        }
        HubStatistics.report(eventId,eventName,trackData);
    }
    private static void traceReport(Context context, String eventId, Map<String, String> trackData) {
        if (context == null || TextUtils.isEmpty(eventId) || trackData == null) {
            return;
        }
        //删除旧的小米统计
    }

    private static void traceLog(String tag, String eventId, Map<String, String> trackData) {
        if (TextUtils.isEmpty(eventId) || trackData == null) {
            return;
        }

        StringBuffer buffer = new StringBuffer();
        int i = 0;
        for (Map.Entry<String, String> entry : trackData.entrySet()) {
            i++;
            buffer.append(entry.getKey());
            buffer.append(": ");
            buffer.append(entry.getValue());
            if (i < trackData.size()) {
                buffer.append("\n");
            }
        }
        XLConfig.LOGD(tag, "<<<<<<" + eventId + ">>>>>>\n" + buffer.toString());
    }

    // IMIE最后一位为1的上报
    private static boolean isTrackOpen(Context context) {
        if (context == null || BuildUtils.isTablet()) {
            return false;
        }
        boolean isTrack = false;
        String deviceId = Util.getImei(context);
        if (TextUtils.isEmpty(deviceId)) {
            isTrack = false;
        } else {
            isTrack = deviceId.endsWith("1");
        }
        //isTrack = true;
        return isTrack;
    }

    /**
     * @param context
     * @param isContinuing 101开始 102续传
     * @param uniqueId
     * @param xunleiEngineOn
     * @param xunleiSpeedOn
     * @param fileSize
     * @param fileName
     * @param pkgName
     */
    public static void trackDownloadStart(Context context, State state, boolean isUseXunlei, String fileName,
                                          long currentSize, int wakelockTime) {
        DownloadItem item = DownloadItem.toDownloadItem(state, isUseXunlei, state.mFilename == null ? "" : state.mFilename, wakelockTime);
        TraceReport.reportDownloadStart(context,item,currentSize);

        HashMap<String, String> trackData =  buildDownloadData(context, item);
        trackData.put("status", currentSize>0?"2":"1");//下载状态，1-开始，2-续传
        reportHub(context, EVENT_ID_DOWNLOAD, EVENT_NAME_DOWNLOAD_START, trackData);
    }

    public static void reportUseSetExtra2(Context context,long id,String url,String pkgName, String extra2, boolean endEngine) {
        //use app cfg
        int useEnsgine = DownloadExtra2.getUseEngine(extra2);
        if (useEnsgine == 1 || useEnsgine == 0) {
            String endEngineString = endEngine ? "1" : "0";
            TraceReport.reportUseSetExtra2(id, url, pkgName, String.valueOf(useEnsgine),endEngineString);

            HashMap<String, String> trackData = new HashMap<String, String>();
            int taskId = generateUniqueId(id, url);
            trackData.put("task_id", String.valueOf(taskId));
            trackData.put("package_name", pkgName);
            trackData.put("endEngine", endEngineString);
            trackData.put("useEngine", String.valueOf(useEnsgine));
            reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_USE_SETEXTRA2_USEENGINE, trackData);
        }
    }

    public static void trackKCGDownloadStart(Context context, State state, boolean isUseKcg, String fileName,
                                          long currentSize, int wakelockTime) {
        DownloadItem item = DownloadItem.toKcgDownloadItem(state, isUseKcg, fileName, wakelockTime);
        TraceReport.reportKcgDownloadStart(context,item,currentSize);
    }

    public static void trackKCGDownloadStop(Context context, State state, boolean isUseXunlei, String fileName,
                                         long downloadTime,
                                         long downloadSize, long startSize, boolean isDelete,
                                         int finalStatus,int soRet, String errorMsg, int wakelockTime) {
        DownloadItem item = DownloadItem.toDownloadItem(state, isUseXunlei, fileName, wakelockTime);
        TraceReport.reportKcgDownloadEnd(context, item, finalStatus, soRet, errorMsg, downloadSize, downloadTime);
    }

    /**
     * @param context
     * @param downloadTime 下载时长
     * @param startSize
     * @param downloadedSize 下载区间大小
     * @param reason 主动暂停0，wifi变3g1 主动删除20，成功30,失败40
     */
    public static void trackDownloadStop(Context context, State state, boolean isUseXunlei, String fileName,
                                         long downloadTime,
                                         long downloadSize, long startSize, boolean isDelete,
                                         int finalStatus,int soRet, String errorMsg, int wakelockTime) {
        DownloadItem item = DownloadItem.toDownloadItem(state, isUseXunlei, fileName, wakelockTime);
        TraceReport.reportDownloadEnd(context, item, finalStatus, soRet, errorMsg, downloadSize, downloadTime);
        ReportStatu reportStatu = new ReportStatu(finalStatus, errorMsg);

        HashMap<String, String> trackData = buildDownloadData(context, item);
        trackData.put("start_status", startSize > 0 ? "2" : "1");//下载状态，1-开始，2-续传
        trackData.put("status", String.valueOf(reportStatu.getReportCode()));
        trackData.put("download_duration",
                String.valueOf(downloadTime > 0 ? downloadTime / 1000 : 0));
        trackData.put("download_size", String.valueOf(downloadSize > 0 ? downloadSize : 0));
        trackData.put("reason", String.valueOf(reportStatu.getReportReason()));
        trackData.put("errorcode", String.valueOf(finalStatus));
        trackData.put("errormsg", String.valueOf(errorMsg));
        reportHub(context, EVENT_ID_DOWNLOAD, EVENT_NAME_DOWNLOAD_STOP, trackData);
    }

    public static void trackDownloadConn(Context context, State item, HashMap<String, String> userData) {
        if (userData == null) {
            return;
        }
        HashMap<String, String> trackData = userData;
        int uniqueId = generateUniqueId(context, item.mId, item.mTotalBytes, item.mRequestUri);
        trackData.put("seq_id", String.valueOf(uniqueId));
        if (CloudConfigPreference.getInstance().trackConnsForHub()) {
            reportHub(context, EVENT_ID_DOWNLOAD, EVENT_NAME_DOWNLOAD_CONNECTIONS, trackData);
        }
        if (CloudConfigPreference.getInstance().trackConnsForOneTack()) {
            TraceReport.trackDownloadConn(context, item, userData);
        }
    }

    /**
     * 接口统计
     * @param context
     * @param interface_name
     * @param code
     * @param msg
     * @param duration
     * @param reason
     * @param info
     * @param md5Name
     */
    public static void trackApiInterface(Context context, String interface_name, int code, String msg, long duration, String reason, String info, String md5Name) {
        if (context == null) {
            return;
        }
        if(!CloudConfigPreference.getInstance().traceApiOpen()){
            return;
        }
        HashMap<String, String> trackData = getCommon(context);
        trackData.put("interface_name",interface_name);
        trackData.put("code",String.valueOf(code));
        trackData.put("msg",msg);
        trackData.put("duration",String.valueOf(duration));
        trackData.put("reason",reason);
        trackData.put("info",info);
        trackData.put("miUserId",md5Name);
        reportHub(context, EVENT_ID_INTERFACE_EVENT, EVENT_ATTR_API_INTERFACE_PERFORMANCE_REPORT, trackData);
    }

    public static void trackProcessStart(Context context) {
        if (context == null) {
            return;
        }
        if(!CloudConfigPreference.getInstance().traceProcessStartOpen()){
            return;
        }
        HashMap<String, String> trackData = getCommon(context);
        trackData.put("start_ts", String.valueOf(System.currentTimeMillis()));
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_PROCESS_START, trackData);
    }

    public static void trackServiceStart(Context context) {
        if (context == null) {
            return;
        }
        if(!CloudConfigPreference.getInstance().traceServiceStartOpen()){
            return;
        }
        HashMap<String, String> trackData = getCommon(context);
        trackData.put("start_ts", String.valueOf(System.currentTimeMillis()));
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_SERVICE_START, trackData);
    }

    public static void trackDownloadProviderCall(Context context,String methodName) {
        if (context == null) {
            return;
        }

        try {
            if (!CloudConfigPreference.getInstance().traceMethodOpen()) {
                return;
            }
            int callingPid = Binder.getCallingPid();
            if (android.os.Process.myPid() == callingPid) {
                return;
            }
            String callAppName = ProcessUtil.getAppName(context, callingPid);
            HashMap<String, String> trackData = getCommon(context);
            trackData.put("start_ts", String.valueOf(System.currentTimeMillis()));
            trackData.put("class_name", "DownloadProvider");
            trackData.put("method_name", methodName);
            trackData.put("call_process_name", callAppName);
            reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_METHOD_CALL, trackData);
        } catch (Exception exc) {
            XLConfig.LOGD("ex", exc);
        }
    }

    private static class ReportStatu {
        int code = -1;
        String msg;

        ReportStatu(int code, String msg) {
            this.code = code;
            this.msg = msg;
            toReportStatu(code);
        }

        int rStatu = -1;
        int rReason = -1;

        private void toReportStatu(int code) {
            //下载状态，1-暂停，2-删除、3-完成、4-失败 5-非下载中被删除
            //reason：状态码，0-主动暂停，1-wifi变3g、2-主动删除、3-成功、4-失败、5-断网
            ReportStatu reportStatu;
            switch (code) {
                case STATUS_SUCCESS:
                    rStatu = 3;
                    rReason = 3;
                    break;
                case STATUS_PAUSED_BY_APP:
                    rStatu = 1;
                    rReason = 0;
                    break;
                case STATUS_CANCELED:
                    rStatu = 2;
                    rReason = 2;
                    break;
                case STATUS_QUEUED_FOR_WIFI:
                    rStatu = 1;
                    rReason = 1;
                    break;
                case STATUS_WAITING_FOR_NETWORK:
                    rStatu = 1;
                    rReason = 5;
                    break;
                case XL_STATUS__FAIL:
                    rStatu = 4;
                    rReason = 4;
                    break;
                default:
                    logD(TAG, "code=" + code, "errorMsg=" + msg);
                    rStatu = 4;
                    rReason = 4;
                    break;
            }
        }

        public int getReportCode() {
            return rStatu;
        }

        public int getReportReason() {
            return rReason;
        }
    }

    private static HashMap<String, String> buildDownloadData(Context context, DownloadItem item) {
        HashMap<String, String> trackData = getCommon(context);
        int uniqueId = generateUniqueId(context, item.mId, item.mTotalBytes, item.mRequestUri);
        trackData.put("seq_id", String.valueOf(uniqueId));
        trackData.put("xunlei_open", item.mIsUseXunlei ? "0" : "1");
        trackData.put("xunlei_vip_open", isVip() ? "0" : "1");//0-vip、1-非vip
        trackData.put("application_name", getNotEmptyString(item.mPackage));
        trackData.put("url", getNotEmptyString(item.mRequestUri));
        trackData.put("file_name", getNotEmptyString(item.mFileName));
        trackData.put("file_size", String.valueOf(item.mTotalBytes));
        trackData.put("filetype_name", getFileSuffix(item.mFileName));//文件名后缀
        if (item.mProtocol == DownloadType.M3U8.ordinal()) {
            trackData.put("percent", String.valueOf(item.mPercent));
        }
        trackData.put("protocol", XLDownloadHelper.getProtocol(item.mProtocol));
        String vCode = XLUtil.getAppVersionCode(item.mPackage);
        String vName = XLUtil.getAppVersionName(item.mPackage);
        trackData.put("origin_version_code", vCode);
        trackData.put("origin_version_name", vName);
        //trackData.put("userid", DownloadSettings.XLShareConfigSettings.getXunleiUserId("0"));
        trackData.put("wakelock_time", String.valueOf(item.mWakelockTime));
        trackData.put("file_optimize", String.valueOf(item.mFileOptimize));
        return trackData;
    }

    private static boolean isVip(){
        String vipToken = DownloadSettings.XLShareConfigSettings.getXLVipToken("");
        return !TextUtils.isEmpty(vipToken);
    }

    private static String parserDownloadFrom(int from) {
        String fromStr = null;
        switch (from) {
            case EXTRA_VALUE_DOWNLOAD_FROM_NEW:
                fromStr = "link";
                break;
            case EXTRA_KEY_DOWNLOAD_FROM_SNIFFER:
                fromStr = "sniffer";
                break;
            case EXTRA_VALUE_DOWNLOAD_FROM_DEFAULT:
            default:
                fromStr = "other";
                break;
        }
        return fromStr;
    }

    public static void trackDownloadDelete(Context context,ArrayList<DownloadItem> items) {
        if (items == null || items.size() == 0) {
            return;
        }
        for (DownloadItem item : items) {
            HashMap<String, String> trackData = buildDownloadData(context, item);
            trackData.put("status", "5");//非下载中被删除
            trackData.put("download_duration","0");
            trackData.put("download_size", "0");
            trackData.put("reason", "2");
            trackData.put("errorcode", String.valueOf(STATUS_CANCELED));
            trackData.put("errormsg", "delete by user");
            reportHub(context, EVENT_ID_DOWNLOAD, EVENT_NAME_DOWNLOAD_STOP, trackData);
        }
    }

    private static String getAndroidId(Context context) {
        String androidId = android.provider.Settings.System.getString(context.getContentResolver(),
                android.provider.Settings.System.ANDROID_ID);
        return androidId;
    }

    private static String getFileSuffix(String fileName) {
        if (TextUtils.isEmpty(fileName)) {
            return "";
        }

        int index = fileName.lastIndexOf(".");
        if (index == -1) {
            return "";
        }

        return fileName.substring(index + 1);
    }

    public static void reportSpeedUpTipShowEvent(Context mContext,String expo_area,String expo_moment,String trial_type){

        Map<String, String> trackData = getClientComment(mContext);
        trackData.put("expo_area", expo_area);
        trackData.put("trial_type", trial_type);
        trackData.put("expo_num", CloudConfigPreference.getInstance().getAccelerateCount()+"");
        trackData.put("expo_moment", expo_moment);
        trackData.put("behavior_event", EVENT_ID_SPEEDUP_TIPS_SHOW);
        traceReport(mContext, EVENT_CLIENT, trackData);
    }


    public static void reportSpeedUpTipClickEvent(Context context,String expo_area,String expo_moment,String trial_type){

        Map<String, String> trackData = getClientComment(context);
        trackData.put("expo_area", expo_area);
        trackData.put("trial_type", trial_type);
        trackData.put("expo_num", CloudConfigPreference.getInstance().getAccelerateCount()+"");
        trackData.put("expo_moment", expo_moment);
        trackData.put("behavior_event", EVENT_ID_SPEEDUP_VIP_CLICK);
        traceReport(context, EVENT_CLIENT, trackData);
    }


    private static Map<String, String> getClientComment(Context context) {

        Map<String, String> trackData = new HashMap<String, String>();
        try {
            trackData.put("time", Long.toString(System.currentTimeMillis() / 1000));
            trackData.put("session_id", "");
            trackData.put("device_id", DeviceIdGenerator.getDeviceId(context));
            trackData.put("product_version", Environment.getVersionName(context));
            trackData.put("miui_version", Build.VERSION.INCREMENTAL);
            trackData.put("os_version", android.os.Build.VERSION.RELEASE);
            trackData.put("phone_type", Build.MODEL);
            trackData.put("network_type", String.valueOf(getNetworkType(context)));
            trackData.put("event_id", EVENT_CLIENT);
        } catch (Exception exc) {
            XLConfig.LOGD("ex", exc);
        }
        return trackData;
    }


    public static void reportNotifyCommendShowEvent(Context context,String recType,String params,String items) {
        Map<String, String> trackData = getClientComment(context);
        try {
            trackData.put("behavior_event",EVENT_ID_NOTIFY_RECOMMEND_SHOW);
            trackData.put("recType", recType);
            trackData.put("rec_id","6");
            trackData.put("business_params",TextUtils.isEmpty(params)?"":URLEncoder.encode(params.replaceAll("\n", "").replaceAll(" ", "")));
            trackData.put("items", URLEncoder.encode(items.replaceAll("\n", "").replaceAll(" ", "")));
            XLConfig.LOGD(NotifyRecommendManager.TAG,"reportNotifyCommendShowEvent recType="+recType+",items="+items);
            traceReport(context, EVENT_CLIENT, trackData);
        } catch (Exception exc) {
            XLConfig.LOGD("ex", exc);
        }
    }


    public static void reportNotifyCommendClickEvent(Context context,String recType,String params,String items,int clickPos) {

        Map<String, String> trackData = getClientComment(context);
        try {
            trackData.put("behavior_event",EVENT_ID_NOTIFY_RECOMMEND_CLICK);
            trackData.put("recType", recType);
            trackData.put("business_params",TextUtils.isEmpty(params)?"":URLEncoder.encode(params.replaceAll("\n", "").replaceAll(" ", "")));
            trackData.put("click_type", clickPos==0?"1":"2");
            trackData.put("items", URLEncoder.encode(items.replaceAll("\n", "").replaceAll(" ", "")));
            XLConfig.LOGD(NotifyRecommendManager.TAG,"reportNotifyCommendClickEvent clickPos="+clickPos+",items="+items);
            traceReport(context, EVENT_CLIENT, trackData);
        } catch (Exception exc) {
            XLConfig.LOGD("ex", exc);
        }
    }

    /**
     *
     * @param context
     * @param size 返回弹窗提示的应用大小（XXMB）
     */
    public static void reportDataLimitDialogShow(Context context,String dialogId,long size) {

        Map<String, String> trackData = getClientComment(context);
        try {
            trackData.put("behavior_event", EVENT_ID_DATA_LIMIT_DIALOG_SHOW);
            trackData.put("dialog_id", String.valueOf(dialogId));
            trackData.put("size", String.valueOf(size));
            traceReport(context, EVENT_CLIENT, trackData);
        } catch (Exception exc) {
            XLConfig.LOGD("ex", exc);
        }
    }

    /**
     *
     * @param context
     * @param size 返回弹窗提示的应用大小（XXMB）
     * @param clickType:1-等待wifi;2-立即下载;
     */
    public static void reportDataLimitDialogClick(Context context,String dialogId,long size,int clickType) {
        Map<String, String> trackData = getClientComment(context);
        try {
            trackData.put("behavior_event", EVENT_ID_DATA_LIMIT_DIALOG_CLICK);
            trackData.put("dialog_id", String.valueOf(dialogId));
            trackData.put("size", String.valueOf(size));
            trackData.put("clickType", String.valueOf(clickType));
            traceReport(context, EVENT_CLIENT, trackData);
        } catch (Exception exc) {
            XLConfig.LOGD("ex", exc);
        }
    }

    private final static String STATISTICS_ACTION="miui.intent.action.analytics.ANALYTICS_DOWNLOAD";
    private final static String STATISTICS_PKG="com.miui.analytics";

    public static void uploadApkDownload(Context context, String downloadFrom, String apkFile, String extra) {
        if (TextUtils.isEmpty(apkFile) || !apkFile.endsWith(".apk")) {
            return;
        }

        File tempFile = new File(apkFile);
        if (!tempFile.exists()) {
            XLConfig.LOGD_INFO(TAG, String.format("uploadApkDownload apk file not exists(%s)", apkFile));
            return;
        }

        try {
            String jsonStr = "";
            PackageManager packageManager = context.getApplicationContext().getPackageManager();
            PackageInfo pkgInfo = packageManager.getPackageArchiveInfo(apkFile, PackageManager.GET_ACTIVITIES);
            String pkgName = pkgInfo.packageName;
            int versionCode = pkgInfo.versionCode;
            String versionName = pkgInfo.versionName;
            JSONObject json = new JSONObject();
            json.put("sourcePkg", downloadFrom != null ? downloadFrom : "");
            json.put("pkg", pkgName);
            json.put("vc", String.valueOf(versionCode));
            json.put("vn", versionName);
            json.put("ex", extra != null ? extra : "");
            jsonStr = json.toString();

            XLConfig.LOGD(TAG, "uploadApkDownload jsonStr=" + jsonStr);
            Intent serviceIntent = new Intent(STATISTICS_ACTION);
            serviceIntent.setPackage(STATISTICS_PKG);
            serviceIntent.putExtra("appExtra", jsonStr);
            context.startService(serviceIntent);
        } catch (JSONException e) {
            XLConfig.LOGD_INFO("uploadApkDownload ex", e);
        } catch (Exception e) {
            XLConfig.LOGD_INFO("uploadApkDownload get app name fail", e);
        }
    }

    public static void logD(String tag, Object... objs) {
        if (objs == null) {
            return;
        }
        XLConfig.LOGD(tag, toString(objs));
    }

    public static String toString(Object... objs) {
        StringBuilder sb = new StringBuilder();
        for (Object obj : objs) {
            if (obj != null) {
                sb.append(" ");
                if (obj instanceof Object[]) {
                    Object[] js = (Object[]) obj;
                    int size = js.length;
                    for (int i = 0; i < size; i++) {
                        if (js[i] != null) {
                            sb.append(js[i].toString());
                            if (i < size) {
                                sb.append(",");
                            }
                        }
                    }
                } else if (obj instanceof long[]) {
                    sb.append(Arrays.toString((long[]) obj));
                } else {
                    sb.append(obj.toString());
                }
            }
        }
        return sb.toString();
    }

    public static String getStackTraceMsg() {
        String stackMsg = "";
        try {
            StackTraceElement[] stack = Thread.currentThread().getStackTrace();
            for (int i = 0; i < stack.length; i++) {
                stackMsg += stack[i] + "\n";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return stackMsg;
    }


    /**
     * 获取网络信息
     */
    public static NetworkInfo getActiveNetworkInfo(Context context) {
        NetworkInfo networkInfo = null;
        ConnectivityManager connectivityManager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            networkInfo = connectivityManager.getActiveNetworkInfo();
        }
        return networkInfo;
    }

    public static boolean isMobileActive(Context context) {
        if (context == null) {
            return false;
        }
        boolean isActive = false;
        NetworkInfo networkInfo = null;
        ConnectivityManager connectivityManager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            networkInfo = connectivityManager.getActiveNetworkInfo();
        }
        if (networkInfo != null) {
            int type = networkInfo.getType();
            if (type == ConnectivityManager.TYPE_MOBILE) {
                isActive = networkInfo.isConnected();
            }
        }

        //将付费网络当成数据流量网络处理
        if (!isActive) {
            return isMeteredNetwork(context);
        }
        return isActive;
    }

    public static boolean isMobileActive(NetworkInfo info, SystemFacade systemFacade) {
        if (info == null) {
            return false;
        }
        if (info.getType() == ConnectivityManager.TYPE_MOBILE) {
            return true;
        }

        //将付费网络当成数据流量网络处理
        if (systemFacade.isActiveNetworkMetered()) {
            return true;
        }
        return false;
    }

    /**
     * wifi下付费网络为付费网络
     * @param context
     * @return
     */
    public static boolean isMeteredNetwork(Context context) {
        if (context == null) {
            return false;
        }
        boolean isActive;
        ConnectivityManager conn =
                (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (Helpers.isPad()) {
            boolean isSupportedMobile = RealSystemFacadeInjector.isNetworkSupported(conn,ConnectivityManager.TYPE_MOBILE);
            if (!isSupportedMobile) {
                isActive = false;
            } else {
                isActive = conn.isActiveNetworkMetered();
            }
            XLConfig.LOGD(Statistics.TAG,"isMeteredNetwork: " + isActive +", isSupportedMobile:"+isSupportedMobile);
        } else {
            isActive = conn.isActiveNetworkMetered();
            XLConfig.LOGD(Statistics.TAG,"isMeteredNetwork: " + isActive);
        }
        return isActive;
    }


    public static boolean isWifiActive(Context context) {
        if (context == null) {
            return false;
        }
        boolean isActive = false;
        NetworkInfo networkInfo = null;
        ConnectivityManager connectivityManager = (ConnectivityManager) context
                .getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            networkInfo = connectivityManager.getActiveNetworkInfo();
        }
        if (networkInfo != null) {
            int type = networkInfo.getType();
            if (type == ConnectivityManager.TYPE_WIFI) {
                isActive = networkInfo.isConnected();
            }
        }
        return isActive;
    }

    /**
     * 获取网络类型 0:无网络连接 2:2G网络 3:3G网络 4:4G网络 9:Wifi网络
     */
    public static int getNetworkType(Context context) {
       return NetworkUtils.getNetworkType(context);
    }

    public static String getNotEmptyString(String str) {
        return TextUtils.isEmpty(str) ? "" : str;
    }

    public static void printCallTime(String fuc, long start, long end, long id) {
        printCallTime(fuc, start, end, id, null);
    }

    /**
     * 只是为了统计方法调用的时间
     *
     * @param fuc
     * @param start
     * @param end
     * @param id
     * @param extra
     */
    public static void printCallTime(String fuc, long start, long end, long id, String extra) {
        //extra = TextUtils.isEmpty(extra) ? "" : extra;
        //String str = String.format("printCallTime id=%d %s duration=%d start=%d end=%d", id, fuc,
        //        endstart, start, end);
        //XLConfig.LOGD(TAG, str + extra);
    }

    public static void traceStartLimitSpeed(Context context, String callProcessName, String msg, long limitValue) {
        if (context == null) {
            return;
        }
        HashMap<String, String> trackData = getCommon(context);
        trackData.put("start_ts", String.valueOf(System.currentTimeMillis()));
        trackData.put("call_process_name", String.valueOf(callProcessName));
        trackData.put("result", String.valueOf(msg));
        trackData.put("limit_value", String.valueOf(limitValue > 0 ? (limitValue / 1024) : limitValue));
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_LIMIT_SPEED_START, trackData);
    }

    public static void traceStopLimitSpeed(Context context, boolean autoCancel, long limitDuration) {
        if (context == null) {
            return;
        }
        HashMap<String, String> trackData = getCommon(context);
        trackData.put("start_ts", String.valueOf(System.currentTimeMillis()));
        trackData.put("reason", autoCancel ? "auto" : "third");
        trackData.put("limit_duration", String.valueOf(limitDuration));
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_LIMIT_SPEED_STOP, trackData);
    }

    public enum MOBILEPOP_CLICKID {
        wait_wifi,
        download_now,
        change_setting;
    };
    public static void reportClickMobilePop(Context context,MOBILEPOP_CLICKID click_id,String source) {
        HashMap<String, String> trackData = getCommon(context);
        trackData.put("click_id", click_id.name());
        trackData.put("source", source);
        trackData.put("package_name", context.getPackageName());
        reportHub(context,EVENT_ID_CLICK_EVENT, EVENT_ATTR_CLICK_POP_MOBILE_DATA_OVERFLOW,trackData);
        TraceReport.reportExpoFromHub(EVENT_ID_CLICK_EVENT, EVENT_ATTR_CLICK_POP_MOBILE_DATA_OVERFLOW,trackData);
    }

    public enum MOBILEPOP_CHANGESETTING_CLICKID {
        ok,
        cancel;
    };
    public static void reportClickMobilePopChangeSetting(Context context,MOBILEPOP_CHANGESETTING_CLICKID click_id,long limit_value,String source) {
        HashMap<String, String> trackData = getCommon(context);
        trackData.put("click_id", click_id.name());
        trackData.put("limit_value", String.valueOf(limit_value));
        trackData.put("source", source);
        trackData.put("package_name", context.getPackageName());
        reportHub(context,EVENT_ID_CLICK_EVENT, EVENT_ATTR_CLICK_POP_MOBILE_DATA_OVERFLOW_CHANGE_SETTING,trackData);
        TraceReport.reportExpoFromHub(EVENT_ID_CLICK_EVENT, EVENT_ATTR_CLICK_POP_MOBILE_DATA_OVERFLOW_CHANGE_SETTING,trackData);
    }

    public static void reportClickMobilePopShow(Context context,long downloadSize,long limit_value,String source) {
        HashMap<String, String> trackData = getCommon(context);
        trackData.put("download_size", String.valueOf(downloadSize));
        trackData.put("limit_value", String.valueOf(limit_value));
        trackData.put("source", source);
        trackData.put("package_name", context.getPackageName());
        reportHub(context,EVENT_ID_CLICK_EVENT, EVENT_ATTR_CLICK_POP_MOBILE_DATA_OVERFLOW_SHOW,trackData);
        TraceReport.reportExpoFromHub(EVENT_ID_CLICK_EVENT, EVENT_ATTR_CLICK_POP_MOBILE_DATA_OVERFLOW_SHOW,trackData);
    }

    public static void reportSpeedup(Map<String, String> extra) {
        Context context = DownloadApplication.getGlobalApplication();
        if (context == null) {
            return;
        }
        HashMap<String, String> trackData = getCommon(context);
        if (extra != null && extra.size() > 0) {
            for (Map.Entry<String, String> entry : extra.entrySet()) {
                trackData.put(entry.getKey(), entry.getValue());
            }
        }
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_SPEEDUP, trackData);
        TraceReport.reportExpoFromHub(EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_SPEEDUP, trackData);
    }

    public static void reportSpeedupApi(Context context, Map<String, String> extra) {
        if (context == null) {
            return;
        }
        HashMap<String, String> trackData = getCommon(context);
        if (extra != null && extra.size() > 0) {
            for (Map.Entry<String, String> entry : extra.entrySet()) {
                trackData.put(entry.getKey(), entry.getValue());
            }
        }
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_API_MONITOR, trackData);
        TraceReport.reportExpoFromHub(EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_API_MONITOR, trackData);
    }

    public static void reportHttpsHostNameVerityFail(Context context, State state, String hostName, boolean isUseXunlei, String subjectX500Principal, String certificateString) {
        if (context == null) {
            return;
        }
        String fileName = state.mFilename == null ? "" : state.mFilename;
        DownloadItem item = DownloadItem.toDownloadItem(state, isUseXunlei, fileName);
        TraceReport.reportHttpsHostNameVerityFail(context,item,hostName,subjectX500Principal,certificateString);

        HashMap<String, String> trackData =  buildDownloadData(context, item);
        trackData.put("hostName", hostName == null ? "" : hostName);
        trackData.put("principal",subjectX500Principal);
        trackData.put("cert",certificateString);
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_HTTPS_HOSTNAME_FAIL, trackData);
    }
    public static void reportHttpsAuthTypeUnknown(Context context, State state, String hostName,boolean isUseXunlei) {
        if (context == null) {
            return;
        }
        String fileName = state.mFilename == null ? "" : state.mFilename;
        DownloadItem item = DownloadItem.toDownloadItem(state, isUseXunlei, fileName);

        TraceReport.reportHttpsAuthTypeUnknown(context,item,hostName);
        HashMap<String, String> trackData =  buildDownloadData(context, item);
        trackData.put("hostName", hostName == null ? "" : hostName);
        trackData.put("authType", "UNKNOWN");
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_HTTPS_AUTHTYPE_UNKNOWN, trackData);
    }
    public static void reportHttpsResponseFail(Context context, State state, boolean isUseXunlei, String resourceLine) {
        if (context == null) {
            return;
        }
        String fileName = state.mFilename == null ? "" : state.mFilename;
        DownloadItem item = DownloadItem.toDownloadItem(state, isUseXunlei, fileName);

        TraceReport.reportHttpsResponseFail(context,item,resourceLine);
        HashMap<String, String> trackData =  buildDownloadData(context, item);
        trackData.put("resourceLine", resourceLine == null ? "" : resourceLine);
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_HTTPS_RESPONSE_FAIL, trackData);
    }

    public static void reportGetCertificateFactoryFail(Context context, String failureReasons) {
        if (context == null) {
            return;
        }
        HashMap<String, String> trackData = getCommon(context);
        trackData.put("failureReason", failureReasons);
        TraceReport.reportGetCertificateFactoryFail(trackData);
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_HTTPS_GETCERTIFICATEFACTORYFAIL, trackData);
    }
    public static void reportOpenSSLX509CertificateFail(Context context, String failureReason) {
        if (context == null) {
            return;
        }
        HashMap<String, String> trackData = getCommon(context);
        trackData.put("failureReason", failureReason);
        TraceReport.reportOpenSSLX509CertificateFail(trackData);
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_HTTPS_OPENSSLX509CERTIFICATEFAIL, trackData);
    }
    public static void reportHttpsVerityFail(Context context, String authType, String hostName, State state,boolean isUseXunlei,String failReason, String subjectX500Principal, String certificateString) {
        if (context == null) {
            return;
        }
        String fileName = state.mFilename == null ? "" : state.mFilename;
        DownloadItem item = DownloadItem.toDownloadItem(state, isUseXunlei, fileName);
        TraceReport.reportHttpsVerityFail(context,authType,hostName,item,failReason,subjectX500Principal,certificateString);

        HashMap<String, String> trackData =  buildDownloadData(context, item);
        trackData.put("hostName", hostName == null ? "" : hostName);
        trackData.put("authType", authType == null ? "" : authType);
        trackData.put("failReason",failReason == null ? "" : failReason);
        trackData.put("principal",subjectX500Principal);
        trackData.put("cert",certificateString);
        reportHub(context, EVENT_ID_DOWNLOAD_OTHER_EVENT, EVENT_ATTR_HTTPS_VERITY_FAIL, trackData);
    }
}
