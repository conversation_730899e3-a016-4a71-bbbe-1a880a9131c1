package com.android.providers.downloads.statistics;

import android.database.Cursor;
import android.text.TextUtils;
import android.util.Log;

import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.kcg.KCGDownloadCfg;
import com.android.providers.downloads.model.State;
import com.android.providers.downloads.util.DownloadExtra;

import java.io.File;

/**
 * Created by lxy on 16/3/22.
 */
public class DownloadItem implements XLDownloadCfg, KCGDownloadCfg {


    public static class Reader {
        private Cursor mCursor;

        public Reader(Cursor cursor) {
            mCursor = cursor;
        }

        public DownloadItem newDownloadItem() {
            DownloadItem item = new DownloadItem();
            item.mId = getLong(COLUMN_ID);
            item.mRequestUri = getString(COLUMN_URI);
            String path = getString(COLUMN_DATA);
            String fileName = "";
            if (!TextUtils.isEmpty(path)) {
                try {
                    fileName = new File(path).getName();
                } catch (Exception e) {
                    Log.w("DownloadItem", "get filename fail", e);
                }
            }

            item.mFileName = fileName;
            item.mTotalBytes = getLong(COLUMN_TOTAL_BYTES);
            item.mPackage = getString(COLUMN_NOTIFICATION_PACKAGE);
            item.errorCode = getInt(COLUMN_STATUS);
            item.errorMsg = getString(COLUMN_ERROR_MSG);
            String extras = getString(COLUMN_EXTRA);
            item.mDownloadFrom = DownloadExtra.getDownloadFrom(extras);
            item.mFileCreateTime = getLong(COLUMN_FILE_CREATE_TIME);
            item.mIsUseXunlei = getInt(COLUMN_XL_TASK_OPEN_MARK) == 1 ? true : false;
            // MIUI ADD FOR KCG:
            item.mIsUseKcg = getInt(COLUMN_KCG_TASK_OPEN_MARK) == 1 ? true : false;
            item.mCurrentBytes = getLong(COLUMN_CURRENT_BYTES);
            return item;
        }

        private String getString(String column) {
            int index = mCursor.getColumnIndexOrThrow(column);
            String s = mCursor.getString(index);
            return (TextUtils.isEmpty(s)) ? null : s;
        }

        private Integer getInt(String column) {
            return mCursor.getInt(mCursor.getColumnIndexOrThrow(column));
        }

        private Long getLong(String column) {
            return mCursor.getLong(mCursor.getColumnIndexOrThrow(column));
        }
    }

    public long mId;
    public boolean mIsUseXunlei;
    // MIUI ADD FOR KCG:
    public boolean mIsUseKcg;
    public String mRequestUri;
    public String mFileName;
    public long mTotalBytes;
    public String mPackage;
    public int errorCode;
    public String errorMsg;
    public int mDownloadFrom;
    public long mFileCreateTime;
    public long mCurrentBytes;
    public int mProtocol;
    public int mPercent;
    public int mWakelockTime;
    public boolean mFileOptimize;

    public static DownloadItem toDownloadItem(State state, boolean isUseXunlei, String fileName, int wakelockTime) {
        DownloadItem item = new DownloadItem();
        item.mId = state.mId;
        item.mRequestUri = state.mRequestUri;
        item.mFileName = fileName;
        item.mTotalBytes = state.mTotalBytes;
        item.mPackage = state.mPackage;
        item.mDownloadFrom = state.mDownloadFrom;
        item.mFileCreateTime = state.mFileCreateTime;
        item.mIsUseXunlei = isUseXunlei;
        item.mProtocol = state.mProtocol;
        item.mPercent = state.mPercent;
        item.mWakelockTime = wakelockTime;
        item.mFileOptimize = state.mFileOptimize;
        return item;
    }

    public static DownloadItem toDownloadItem(State state, boolean isUseXunlei, String fileName) {
        return toDownloadItem(state, isUseXunlei, fileName, -1);
    }

    // MIUI ADD FOR KCG:
    public static DownloadItem toKcgDownloadItem(State state, boolean isUseKCG, String fileName, int wakelockTime) {
        DownloadItem item = new DownloadItem();
        item.mId = state.mId;
        item.mRequestUri = state.mRequestUri;
        item.mFileName = fileName;
        item.mTotalBytes = state.mTotalBytes;
        item.mPackage = state.mPackage;
        item.mDownloadFrom = state.mDownloadFrom;
        item.mFileCreateTime = state.mFileCreateTime;
        item.mIsUseKcg = isUseKCG;
        item.mWakelockTime = wakelockTime;
        return item;
    }
    // END
}
