package com.android.providers.downloads.statistics;

import android.content.Context;
import android.text.TextUtils;

import com.android.providers.downloads.model.State;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.utils.InetAddressUtil;
import com.michael.corelib.coreutils.CustomThreadPool;
import com.xunlei.downloadlib.XLDownloadManager;
import com.xunlei.downloadlib.parameter.P2spTaskOriginResConnectionStat;
import com.xunlei.downloadlib.parameter.P2spTaskOriginResStat;
import com.xunlei.downloadlib.parameter.XLConstant;

import java.util.HashMap;
import java.util.Map;

public class XLStatistics extends Statistics {

    public static void trackDownloadConn(Context context, XLDownloadManager manager, long taskId, State state, int finalStatus, int soRet) {
        if (context == null) {
            return;
        }
        if (!CloudConfigPreference.getInstance().trackConnsForOneTack()
                && !CloudConfigPreference.getInstance().trackConnsForHub()) {
            return;
        }
        P2spTaskOriginResStat stat = new P2spTaskOriginResStat();
        int ret = manager.getP2spTaskOriginResStat(taskId, stat);
        if (XLConstant.XLErrorCode.NO_ERROR != ret) {
            logD(TAG, "getP2spTaskOriginResStat ret = " + ret);
            return;
        }
        Context appCtx = context.getApplicationContext();
        CustomThreadPool.asyncWork(new Runnable() {
            @Override
            public void run() {
                _trackDownloadConn(appCtx, manager, stat, state, finalStatus, soRet);
            }
        });
    }

    public static void _trackDownloadConn(Context context, XLDownloadManager manager, P2spTaskOriginResStat stat, State state, int finalStatus, int soRet) {
        if (context == null || manager == null) {
            return;
        }

        try {
            String dnsIps = InetAddressUtil.getDnsIps(context);
            HashMap<String, String> data = new HashMap<String, String>();
            P2spTaskOriginResConnectionStat[] conns = stat.mConnectionStat;
            for (int i = 0; i < conns.length; i++) {
                data.clear();
                P2spTaskOriginResConnectionStat conn = conns[i];
                trackDownloadConn(context, stat.mOriginURL, dnsIps, state, finalStatus, soRet, conn, data);
            }
        } catch (Exception e) {
            logD(TAG, "trackDownloadConn exc", e);
        }
    }

    public static void trackDownloadConn(Context context, String originURL, String dnsIps, State state,
                                         int finalStatus, int soRet, P2spTaskOriginResConnectionStat conn, HashMap<String, String> data) {
        data.put("origin_url", String.valueOf(originURL));
        data.put("scheme", String.valueOf(conn.mScheme));
        data.put("host", String.valueOf(conn.mHost));
        data.put("port", String.valueOf(conn.mPort));
        data.put("path", String.valueOf(conn.mPath));
        data.put("conn_ip", String.valueOf(conn.mIP));
        data.put("exception", String.valueOf(conn.mException));
        data.put("result", String.valueOf(conn.mResult));
        data.put("network_type", String.valueOf(conn.mNetworkType));
        data.put("req_time", String.valueOf(conn.mReqTime));
        data.put("dns_duration", String.valueOf(conn.mDnsDuration));
        data.put("connect_duration", String.valueOf(conn.mConnectDuration));
        data.put("handshake_duration", String.valueOf(conn.mHandshakeDuration));
        data.put("download_duration", String.valueOf(conn.mDownloadDuration));
        addNotEmptyItem(data, "xm_cdn_prov", String.valueOf(conn.mXMCDNProvider));
        addNotEmptyItem(data, "xm_cache_status", String.valueOf(conn.mXMCacheStatus));
        addNotEmptyItem(data, "xm_remote_address", String.valueOf(conn.mXMRemoteAddress));
        data.put("dns_ip", String.valueOf(dnsIps));
        data.put("error_code", String.valueOf(finalStatus));
        data.put("so_code", String.valueOf(soRet));
        data.put("time", String.valueOf(System.currentTimeMillis()));
        data.put("application_name", getNotEmptyString(state.mPackage));
        trackDownloadConn(context, state, data);
    }

    private static void addNotEmptyItem(Map<String, String> data, String key, String value) {
        if (data != null && !TextUtils.isEmpty(value)) {
            data.put(key, value);
        }
    }

    private static boolean isFail(int finalStatus) {
        boolean isFail = false;
        switch (finalStatus) {
            case STATUS_SUCCESS:
            case STATUS_PAUSED_BY_APP:
            case STATUS_CANCELED:
            case STATUS_QUEUED_FOR_WIFI:
            case STATUS_WAITING_FOR_NETWORK:
                isFail = false;
                break;
            case XL_STATUS__FAIL:
                isFail = true;
                break;
            default:
                isFail = true;
                break;
        }
        return isFail;
    }

    private static boolean isFailConn(P2spTaskOriginResConnectionStat conn) {
        return conn != null &&
                conn.mResult != 200 &&
                conn.mResult != 206 &&
                !(conn.mResult == 0 && TextUtils.isEmpty(conn.mException));
    }
}
