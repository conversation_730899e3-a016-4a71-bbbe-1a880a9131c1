package com.android.providers.downloads.xunlei;

import android.content.Context;
import android.content.pm.PackageManager;
import android.security.NetworkSecurityPolicy;
import android.security.net.config.ApplicationConfig;
import android.security.net.config.RootTrustManager;
import android.text.TextUtils;
import android.util.Log;

import com.android.providers.downloads.helper.FeatureSwitch;
import com.android.providers.downloads.model.State;
import com.android.providers.downloads.service.DownloadInfo;
import com.android.providers.downloads.statistics.Statistics;
import com.xunlei.downloadlib.XLDownloadManager;
import com.xunlei.downloadlib.parameter.XLVerifyPeerInterface;

import java.io.ByteArrayInputStream;
import java.lang.reflect.Method;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

import okhttp3.internal.tls.OkHostnameVerifier;

public class XlDownloadThreadDelegate {
    private final XLDownloadThread mXlDownloadThread;

    public XlDownloadThreadDelegate(XLDownloadThread xlDownloadThread) {
        mXlDownloadThread = xlDownloadThread;
    }

    public boolean checkServerTrusted(Context context, byte[][] chains, String authType, String hostName, DownloadInfo info, State state) {
        String packageName = info.mPackage;
        if (!FeatureSwitch.enableHttpsVerity(packageName)) {
            return true;
        }
        if (!FeatureSwitch.enableCheckHttpsAuthtype(packageName) && (authType == null || "UNKNOWN".equals(authType))) {
            Statistics.reportHttpsAuthTypeUnknown(context, state, hostName, true);
            Log.i(XLDownloadThread.TAG, String.format("[%d] checkServerTrusted authType: %s,hostName: %s", info.mId,authType, hostName));
            return true;
        }

        boolean trusted = true;
        X509Certificate certificate = null;
        try {
            X509Certificate[] certificates = decodeX509CertificateChain(packageName,chains);
            if (certificates != null && certificates.length > 0) {
                // host校验
                certificate = certificates[0];
                if (FeatureSwitch.enableCheckHttpsHost(packageName)) {
                    boolean verify = OkHostnameVerifier.INSTANCE.verify(hostName, certificate);
                    if (!verify) {
                        trusted = false;
                        String subjectX500Principal = certificate.getSubjectX500Principal().toString();
                        String certificateString = FeatureSwitch.enableRepportCertificate(packageName) ? certificate.toString() : "";
                        Statistics.reportHttpsHostNameVerityFail(context, state, hostName, true, subjectX500Principal,certificateString);
                        Log.i(XLDownloadThread.TAG, String.format("[%d] OkHostnameVerifier fail: %s %s %s",info.mId, hostName, subjectX500Principal,certificateString));
                        return trusted;
                    }
                }

                // 证书校验
                ApplicationConfig appConfig = null;
                try {
                    appConfig = NetworkSecurityPolicy.getApplicationConfigForPackage(context, packageName);
                } catch (PackageManager.NameNotFoundException e) {
                    appConfig = NetworkSecurityPolicy.getApplicationConfigForPackage(context, "com.android.providers.downloads");
                    Log.i(XLDownloadThread.TAG, String.format("[%d] getApplicationConfigForPackage not find: %s", info.mId,packageName));
                }
                RootTrustManager x509TrustManager = (RootTrustManager) appConfig.getTrustManager();
                x509TrustManager.checkServerTrusted(certificates, authType, hostName);
            }
        } catch (Exception e) {
            //证书校验失败会抛异常
            trusted = false;
            String failReason = e.toString();
            String subjectX500Principal = "";
            String certificateString = "";
            if (certificate != null) {
                subjectX500Principal = certificate.getSubjectX500Principal().toString();
                certificateString = FeatureSwitch.enableRepportCertificate(packageName) ? certificate.toString() : "";
            }
            Statistics.reportHttpsVerityFail(context, authType, hostName, state, true,failReason, subjectX500Principal,certificateString);
            Log.i(XLDownloadThread.TAG, String.format("[%d] checkServerTrusted fail: %s %s %s", info.mId, failReason, subjectX500Principal,certificateString));
        }
        return trusted;
    }

    public X509Certificate[] decodeX509CertificateChain(String packageName, byte[][] certChain)
            throws java.security.cert.CertificateException {
        CertificateFactory certificateFactory = getCertificateFactory();
        int numCerts = certChain.length;
        X509Certificate[] decodedCerts = new X509Certificate[numCerts];
        for (int i = 0; i < numCerts; i++) {
            decodedCerts[i] = decodeX509Certificate(packageName,certificateFactory, certChain[i]);
        }
        return decodedCerts;
    }

    private CertificateFactory getCertificateFactory() {
        try {
            return CertificateFactory.getInstance("X.509");
        } catch (java.security.cert.CertificateException e) {
            Log.i(XLDownloadThread.TAG, String.format("getCertificateFactory: %s", e.toString()));
            Statistics.reportGetCertificateFactoryFail(mXlDownloadThread.getContext(),e.toString());
            return null;
        }
    }

    private X509Certificate decodeX509Certificate(String packageName, CertificateFactory certificateFactory,
                                                  byte[] bytes) throws java.security.cert.CertificateException {
        X509Certificate certificate = null;
        if (FeatureSwitch.enableConscrypt(packageName)) {
            Class<?> OpenSSLX509Certificate = null;
            try {
                OpenSSLX509Certificate = Class.forName("com.android.org.conscrypt.OpenSSLX509Certificate");
                Method fromX509Der = OpenSSLX509Certificate.getDeclaredMethod("fromX509Der", byte[].class);
                certificate = (X509Certificate) fromX509Der.invoke(null, bytes);
            } catch (Exception e) {
                Log.i(XLDownloadThread.TAG, String.format("get OpenSSLX509Certificate failed: %s", e.toString()));
                Statistics.reportOpenSSLX509CertificateFail(mXlDownloadThread.getContext(),e.toString());
            }
        } else {
            if (certificateFactory != null) {
                certificate = (X509Certificate) certificateFactory.generateCertificate(
                        new ByteArrayInputStream(bytes));
            }
        }
        return certificate;
    }

    public void setTaskOriginResNeedVerifyPeer(String packageName, long taskId, XLVerifyPeerInterface verifyPeerInterface) {
        if (!FeatureSwitch.enableHttpsVerity(packageName)) {
            return;
        }
        XLDownloadManager xlDownloadManager = mXlDownloadThread.mXlDownloadManager;
        if (xlDownloadManager == null) {
            return;
        }
        xlDownloadManager.setTaskOriginResNeedVerifyPeer(taskId, verifyPeerInterface);
    }

    public boolean checkHttpsResponse(Context context,String packageName, State state) {
        if (TextUtils.isEmpty(state.mResourceLine)) {
            return false;
        }
        boolean failed = false;
        try {
            String[] status = state.mResourceLine.trim().split(" ");
            if (status.length > 1 && Integer.parseInt(status[1]) == 156) {
                failed = true;
                Log.i(XLDownloadThread.TAG,String.format("[%d] checkHttpsResponse %s",state.mId,state.mResourceLine));
                Statistics.reportHttpsResponseFail(context,state,true,state.mResourceLine);
            }
        } catch (Exception e) {
            Log.i(XLDownloadThread.TAG,String.format("[%d] checkHttpsResponse %s %s",state.mId,state.mResourceLine,e.toString()));
        }
        if (!FeatureSwitch.enableThrowHttpsFail(packageName)) {
            failed = false;
        }
        return failed;
    }
}
