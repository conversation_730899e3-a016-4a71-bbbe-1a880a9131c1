
package com.android.providers.downloads.xunlei;

import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.provider.Downloads;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;

import com.android.providers.downloads.DownloadThread;
import com.android.providers.downloads.StorageManager;
import com.android.providers.downloads.SystemFacade;
import com.android.providers.downloads.alarm.LimitSpeed;
import com.android.providers.downloads.config.Constants;
import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.exception.StopRequestException;
import com.android.providers.downloads.model.State;
import com.android.providers.downloads.scdn.ScdnHelper;
import com.android.providers.downloads.service.DownloadInfo;
import com.android.providers.downloads.service.DownloadNotifier;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.speedup.SpeedupManager2;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.statistics.XLStatistics;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.ProcessUtil;
import com.android.providers.downloads.util.TokenHelper;
import com.android.providers.downloads.util.XLDownloadHelper;
import com.android.providers.downloads.util.XLUtil;
import com.android.providers.downloads.xunlei.speedup.XLSpeedUpManager;
import com.xunlei.downloadlib.XLDownloadManager;
import com.xunlei.downloadlib.parameter.GetDownloadHead;
import com.xunlei.downloadlib.parameter.GetTaskId;
import com.xunlei.downloadlib.parameter.P2spTaskParam;
import com.xunlei.downloadlib.parameter.ServerResourceParam;
import com.xunlei.downloadlib.parameter.XLConstant;
import com.xunlei.downloadlib.parameter.XLConstant.XLDownloadHeaderState;
import com.xunlei.downloadlib.parameter.XLConstant.XLErrorCode;
import com.xunlei.downloadlib.parameter.XLTaskInfo;
import com.xunlei.downloadlib.parameter.XLVerifyPeerInterface;
import com.xunlei.vipchannel.XLVipChannelManager;
import com.xunlei.vipchannel.parameter.VipGetTaskId;
import com.xunlei.vipchannel.parameter.VipSubTaskInfo;
import com.xunlei.vipchannel.parameter.VipSubTaskParam;
import com.xunlei.vipchannel.parameter.VipTaskInfo;
import com.xunlei.vipchannel.parameter.VipTaskParam;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.GeneralSecurityException;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLPeerUnverifiedException;

import com.libcore.io.IoUtils;

import static android.text.format.DateUtils.SECOND_IN_MILLIS;
import static java.net.HttpURLConnection.HTTP_INTERNAL_ERROR;
import static java.net.HttpURLConnection.HTTP_MOVED_PERM;
import static java.net.HttpURLConnection.HTTP_MOVED_TEMP;
import static java.net.HttpURLConnection.HTTP_OK;
import static java.net.HttpURLConnection.HTTP_PARTIAL;
import static java.net.HttpURLConnection.HTTP_SEE_OTHER;
import static java.net.HttpURLConnection.HTTP_UNAVAILABLE;

public class XLDownloadThread extends DownloadThread implements XLDownloadCfg, XLVerifyPeerInterface {

    public static final String TAG = "XLDownloadThread";
    private static final String VIP_TAG = "vip";

    protected final long MAX_GET_DOWNLOAD_HEADER_DELAY = 60 * 1000; // 60 second
    private static final int DEFAULT_TIMEOUT = (int) (20 * SECOND_IN_MILLIS);
    private final XlDownloadThreadDelegate mXlDownloadThreadDeletegation;

    protected Context mContext;

    protected final XLDownloadManager mXlDownloadManager;
    protected final XLVipChannelManager mXLVipChannelManager;
    protected long mTaskId = -1;
    protected long mVipTaskId = -1;

    private boolean hasAddedXlCdnQueryTask = false;
    private boolean hasGetCdn = false;
    private boolean hasTryAcceleration = false;
    private long loopStart = 0;// 统计方法调用时间用,记录轮询开始时间
    private boolean isReport = false;// 统计方法调用时间用,记录是否记录过
    protected File mDownloadDestFile;
    private int samplingInterval = 10;//用来控制进度日志采样输出
    private int count = 0;
    private boolean needShowProgressLog = false;

    public XLDownloadThread(Context context, SystemFacade systemFacade, DownloadInfo info,
                            StorageManager storageManager, DownloadNotifier notifier, XLDownloadManager dm,
                            XLVipChannelManager vm) {
        super(context, systemFacade, info, storageManager, notifier);
        mContext = context;
        mXlDownloadManager = dm;
        mXLVipChannelManager = vm;
        mXlDownloadThreadDeletegation = new XlDownloadThreadDelegate(this);
    }

    protected Context getContext() {
        return mContext;
    }

    protected String getCookie() {
        String cookie = "";
        for (Pair<String, String> header : mInfo.getHeaders()) {
            if (!TextUtils.isEmpty(header.first)
                    && header.first.equalsIgnoreCase("Cookie")) {
                cookie = header.second;
            }
        }
        return cookie;
    }

    protected String getRefUrl() {
        String refurl = "";
        for (Pair<String, String> header : mInfo.getHeaders()) {
            if (!TextUtils.isEmpty(header.first)
                    && header.first.equalsIgnoreCase("Referer")) {
                refurl = header.second;
            }
        }
        return refurl;
    }

    protected XLDownloadManager getManager() {
        return mXlDownloadManager;
    }

    protected XLVipChannelManager getVipManager() {
        return mXLVipChannelManager;
    }

    protected long getDownloadId() {
        return mInfo.mId;
    }

    protected long getTaskId() {
        return mTaskId;
    }

    protected long getVipTaskId() {
        return mVipTaskId;
    }

    @Override
    protected void preDownload() {
        super.preDownload();
        try {
            XLConfig.setSoDebug(XLConfig.isDebug(), XLConfig.getLogSoPath());
        } catch (Exception e) {
            XLConfig.LOGD("setSoDebug ex", e);
        }
    }

    @Override
    protected void executeDownload(State state) throws StopRequestException {
        long start = System.currentTimeMillis();
        Statistics.printCallTime("executeDownload", start, start, mInfo.mId);
        logD("executeDownload_xl ---> fun called");
        if (state.mTotalBytes != 0 && state.mCurrentBytes == state.mTotalBytes) {
            logD("DownloadThread executeDownload_xl Skipping initiating request for download "
                    + mInfo.mId + "; already completed");
            return;
        }

        LimitSpeed.getInstance().checkAndSetLimitSpeed();
        state.mContinuingDownload=state.mCurrentBytes > 0;
        NetworkInfo info = mSystemFacade.getActiveNetworkInfo();
        // only do this proc in mobile network and new task
        if (info != null && info.getType() == ConnectivityManager.TYPE_MOBILE
                && !state.mContinuingDownload && mInfo.mBypassRecommendedSizeLimit == 0) {
            Long recommendedMaxBytesOverMobile = mSystemFacade.getRecommendedMaxBytesOverMobile();
            if (!((recommendedMaxBytesOverMobile != null && recommendedMaxBytesOverMobile >= DownloadInfo.MAX_BYTES_OVER_MOBILE))) {
                Log.e(Constants.TAG,
                        "DownloadThread executeDownload_xl ---> checkFileSizeinMobile fun called!");
                mIfMobileFileSizeChecked = true;
                checkFileSizeinMobile(state);
            }
        }

        checkConnectivity(true);

        startTask(state);

        int queryMode = canAcceleration() ? 1 : 0;
        loopProgress(state, queryMode);
    }

    private int mSpeedUpResultCode;
    private com.android.providers.downloads.xunlei.XLSpeedUpRules XLSpeedUpRules =new XLSpeedUpRules();
    private XLGSSYUpdateRules updateRules = new XLGSSYUpdateRules();

    protected void checkFileSizeinMobile(State state) throws StopRequestException {

        SSLContext appContext;
        try {
            appContext = getSSLContextForPackage(mContext, mInfo.mPackage);
        } catch (GeneralSecurityException e) {
            // This should never happen.
            throw new StopRequestException(STATUS_UNKNOWN_ERROR, "Unable to create SSLContext.");
        }

        long fileSize = -1;
        String headerTransferEncoding = null;

        int index = 0;

        while (index++ < Constants.MAX_REDIRECTS) {
            HttpURLConnection conn = null;
            try {
                conn = (HttpURLConnection) state.mUrl.openConnection();
                conn.setInstanceFollowRedirects(false);
                conn.setConnectTimeout(DEFAULT_TIMEOUT);
                conn.setReadTimeout(DEFAULT_TIMEOUT);
                // If this is going over HTTPS configure the trust to be the same as the calling
                // package.
                if (conn instanceof HttpsURLConnection) {
                    ((HttpsURLConnection) conn).setSSLSocketFactory(appContext.getSocketFactory());
                }

                addRequestHeaders(state, conn);
                Map<String, List<String>> requestProperties = conn.getRequestProperties();

                XLConfig.LOGD(TAG, " executeDownload addRequestHeaders \nheader=" + requestProperties);
                final int code = conn.getResponseCode();
                XLConfig.LOGD(TAG, " checkFileSizeinMobile responseCode=" + code);
                if (code == HTTP_MOVED_PERM
                        || code == HTTP_MOVED_TEMP
                        || code == HTTP_SEE_OTHER
                        || code == HTTP_TEMP_REDIRECT) {
                    final String location = conn.getHeaderField("Location");
                    state.mUrl = new URL(state.mUrl, location);
                    if (code == HTTP_MOVED_PERM) {
                        // Push updated URL back to database
                        state.mRequestUri = state.mUrl.toString();
                    }
                    continue;
                }
                readResponseHeaders(state, conn);
                state.mFilename = Helpers.generateSaveFile(
                        mContext,
                        mInfo.mUri,
                        mInfo.mHint,
                        state.mContentDisposition,
                        state.mContentLocation,
                        state.mMimeType,
                        mInfo.mDestination,
                        state.mContentLength,
                        mStorageManager,
                        false,
                        needFixExtension(mInfo.mPackage));
//                state.mDownloadingFileName = state.mFilename + Helpers.sDownloadingExtension;
                final String transferEncoding = conn.getHeaderField("Transfer-Encoding");
                if (transferEncoding == null) {
                    // get file size form http content
                    state.mContentLength = getHeaderFieldLong(conn, "Content-Length", -1);
                    fileSize = state.mContentLength;
                    state.mTotalBytes = state.mContentLength;
                    mInfo.mTotalBytes = state.mContentLength;
                    // now we get filename, and check space.
                    mStorageManager.verifySpace(mInfo.mDestination, state.mFilename,
                            state.mTotalBytes);
                }
                // correct mimetype
                correctMimeType(state);
                // update header values into database
                updateDatabaseFromHeaders(state);
                if (headerTransferEncoding == null) {
                    break;
                } else {
                    checkPausedOrCanceled(state);
                    if (Helpers.isCmTestBuilder()) {
                        break;
                    }
                    String errorMsg = mInfo.generateReason(STATUS_NET_UNUSABLE_DUE_TO_SIZE, "No Content-Length");
                    throw new StopRequestException(Downloads.Impl.STATUS_QUEUED_FOR_WIFI, errorMsg);
                }
            } catch (IOException ex) {
                if (ex instanceof SSLPeerUnverifiedException) {
                    throw new StopRequestException(
                            STATUS_BAD_REQUEST,
                            "while trying to execute request: " + ex.toString(), ex);
                } else {
                    throw new StopRequestException(
                            Downloads.Impl.STATUS_HTTP_DATA_ERROR,
                            "while trying to execute request: " + ex.toString(), ex);
                }
            } finally {
                if (conn != null) {
                    InputStream in = null;
                    try {
                        in = conn.getInputStream();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    IoUtils.closeQuietly(in);
                    conn.disconnect();
                }
            }
        } /* End of While */

        int status = Downloads.Impl.STATUS_WAITING_FOR_NETWORK;

        if (index >= Constants.MAX_REDIRECTS) {
            throw new StopRequestException(STATUS_TOO_MANY_REDIRECTS, "Too many redirects");
        }

        if (Helpers.isCmTestBuilder()) {
            return;
        }

        Long maxBytesOverMobile = mSystemFacade.getMaxBytesOverMobile();
        if (maxBytesOverMobile != null && fileSize > maxBytesOverMobile) {
            status = Downloads.Impl.STATUS_QUEUED_FOR_WIFI;
            XLConfig.LOGD("DownloadThread  checkFileSizeinMobile ---> notifyPauseDueToSize");
            // mInfo.notifyPauseDueToSize(true);
            String errorMsg = mInfo.generateReason(STATUS_NET_UNUSABLE_DUE_TO_SIZE,
                    "download size exceeds limit for mobile network");
            throw new StopRequestException(status, errorMsg);
        }

        if (mInfo.mBypassRecommendedSizeLimit == 0) {
            Long recommendedMaxBytesOverMobile = mSystemFacade.getRecommendedMaxBytesOverMobile();
            if (recommendedMaxBytesOverMobile != null
                    && fileSize > recommendedMaxBytesOverMobile) {
                status = Downloads.Impl.STATUS_QUEUED_FOR_WIFI;
                // mInfo.notifyPauseDueToSize(false);
                String errorMsg = mInfo.generateReason(STATUS_NET_UNUSABLE_DUE_TO_SIZE,
                        "download size exceeds limit for mobile network");
                throw new StopRequestException(status, errorMsg);
            }
        }
    }
    public int  isAlreadyQuerySpeedTaskMode = 0;
    private String mGcid;
    private void loopProgress(State state, int queryMode) throws StopRequestException {
        XLTaskInfo taskInfo = new XLTaskInfo();
        VipTaskInfo vipTaskInfo = new VipTaskInfo();
        loopStart = System.currentTimeMillis();
        for (;;) {
            checkPausedOrCanceled(state);
            checkFileExists(mDownloadDestFile, state);
            try {
                Thread.sleep(1000);
            } catch (Exception e) {
            }
            // querymode 1表示获取高速资源信息，0表示不获取高速资源信息
            //int ret = mXlDownloadManager.getTaskInfo(getTaskId(), queryMode, taskInfo);
            //int ret = mXlDownloadManager.getTaskInfo(getTaskId(),1 XLSpeedUpManager.getSpeedUpManager().isInVipMode()? 1:0, taskInfo);

            int ret = mXlDownloadManager.getTaskInfo(getTaskId(),getSpeedUpQueryMode(), taskInfo);
            logD("transferData " + getDownloadInfo() + " getTaskInfo queryMode="
                    + queryMode + " ret = "
                    + ret);
            if (XLErrorCode.NO_ERROR != ret) {
                throwException("loopProgress", ret);
            }


            reportTask(state, taskInfo, queryMode);
            if(XLSpeedUpRules.isCanUseSpeedUp(mSpeedUpResultCode,mInfo,taskInfo)){
                mSpeedUpResultCode= XLSpeedUpManager.getSpeedUpManager().doQueryAndSpeedUp(XLSpeedUpRules.getSpeedUpMode(),
                        mInfo.mId,state.mRequestUri,getRefUrl(),getCookie(),state.mFilename,"",null,XLSpeedUpManager.getDownloadType(state.mRequestUri).ordinal(),taskInfo);
            }

            mGcid = taskInfo.mGcid;
            SpeedupManager2.getInstance().trySpeedup(mInfo.mId, mInfo.mPackage, taskInfo.mGcid, mState.mTotalBytes);
            reportProgress(state);
           /* if (!hasTryAcceleration) {
                queryMode=canAcceleration()?1:0;
                getAccelerationInfo(state, queryMode, taskInfo, vipTaskInfo);
            }*/

            //checkPausedOrCanceled(state);
            checkFileSize(taskInfo.mFileSize);
            if (taskInfo.mTaskStatus == TASK_STATU_SUCCESS) {
                break;
            }
            checkTaskStatus(taskInfo);
            checkSpace(state,taskInfo);
        }
    }

    private boolean isCheck = false;
    private void checkFileSize(long fileSize) throws StopRequestException {
        if (!BuildUtils.isSdkOverR()) {
            return;
        }
        if (!CloudConfigPreference.getInstance().enableCheckFileSize()) {
            return;
        }
        long sFileSize = mInfo.mFileSize;
        if (sFileSize > 0 && fileSize > 0 && !isCheck) {
            if (sFileSize != fileSize) {
                throw new StopRequestException(STATUS_CHECK_FILE_SIZE_FAIL, "check file size fail");
            }
            isCheck = true;
        }
    }

    protected int getSpeedUpQueryMode(){
        if(isAlreadyQuerySpeedTaskMode==0){
            isAlreadyQuerySpeedTaskMode= XLSpeedUpManager.getSpeedUpManager().isCanQuerySpeedUpResource(mInfo)?1:0;
        }
        return isAlreadyQuerySpeedTaskMode;
    }

    protected void checkSpace(State state, XLTaskInfo taskInfo) throws StopRequestException {
        long needSpace = 0;
        if (state.mTotalBytes < 0) {
            needSpace = taskInfo.mOriginRecvBytes + taskInfo.mP2PRecvBytes + taskInfo.mP2SRecvBytes +
                    taskInfo.mAdditionalResPeerBytes + taskInfo.mAdditionalResVipRecvBytes;
        } else {
            needSpace = state.mTotalBytes - state.mCurrentBytes;
        }
        if (needSpace > 0) {
            mStorageManager.verifySpace(mInfo.mDestination, state.mFilename,
                    needSpace);
        }
    }

    private void startTask(State state) throws StopRequestException {
        logD("enter transferData function!");

        if (state.mRequestUri == null) {
            throwErrorException("xunlei - Download url is null.", TASK_URL_IS_NULL);
        }
        String fileName = "";
        String path = "";
        int taskmode = !state.mContinuingDownload ? XLConstant.XLCreateTaskMode.NEW_TASK.ordinal()
                : XLConstant.XLCreateTaskMode.CONTINUE_TASK.ordinal();
        if (mIfMobileFileSizeChecked || state.mContinuingDownload) {
            File downloadFile = new File(state.mFilename);
            fileName = downloadFile.getName();
            path = downloadFile.getParent();
        }

        P2spTaskParam para = new P2spTaskParam(fileName, path,
                state.mRequestUri.toString(), getCookie(), getRefUrl(), "", "",
                taskmode, downloadSequenceId);
        logD("transferData init P2spTaskParam id=" + state.mId + " taskmode="
                + taskmode + " " + path
                + "/" + fileName);

        GetTaskId task = new GetTaskId();
        int ret = mXlDownloadManager.createP2spTask(para, task);
        mTaskId = task.getTaskId();
        logD("transferData createP2spTask " + ret);
        if (ret != XLErrorCode.NO_ERROR) {
            throwException("createP2spTask", ret);
        }

        if (!Helpers.isTablet() && !Helpers.isEnglishEnv(mContext)
                && !Helpers.isInternationalBuilder()) {
            Intent intent = new Intent("com.downloads.notification.action.init");
            intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
            mContext.sendBroadcast(intent);
        }

        if (getTaskId() == -1) {
            throwErrorException("xunlei - XLCreateP2SPTask return mTaskId -1", TASK_NO_CREATE);
        }

        setTaskOrigin(state.mPackage);
        mXlDownloadManager.setOriginUserAgent(getTaskId(), userAgent());

        // 设置Http Header
        addRequestHeadersToXlEngine(mXlDownloadManager, getTaskId());

        setTaskUid(mInfo.mUid);
        mXlDownloadThreadDeletegation.setTaskOriginResNeedVerifyPeer(mInfo.mPackage,getTaskId(),this);
        int startTask = mXlDownloadManager.startTask(getTaskId());
        logInfo("transferData startTask ret=" + startTask);
        XLUtil.statExternalInfo(mXlDownloadManager, getTaskId(), -1);
        XLUtil.addStatInfo(mXlDownloadManager, getTaskId(), state.mPackage);
        if (!state.mContinuingDownload && !mIfMobileFileSizeChecked) {
            getDownloadHeader(getTaskId(), state);
            ret = mXlDownloadManager.setFileName(getTaskId(), state.mFilename);
            logD("transferData setFileName ret = " + ret);
            if (XLErrorCode.NO_ERROR != ret) {
                throwException("setFileName", ret);
            }
        }

        if(TextUtils.isEmpty(state.mFilename)){
            throwErrorException("download name is null", TASK_DOWNLOAD_FILENAME_INVALID);
        }
        ScdnHelper.getInstance().addScdnResource(
                mXlDownloadManager,getTaskId(),
                state.mPackage,mInfo.mDownloadExtras2,
                getTask());
        mDownloadDestFile = new File(state.mFilename);
    }

    protected void setTaskOrigin(String pkg) {
        int ret = getManager().setDownloadTaskOrigin(getTaskId(), pkg);
        logD("setTaskOrigin  ret=" + ret + " pkg=" + (TextUtils.isEmpty(pkg) ? "" : pkg));
    }

    protected void setTaskUid(int uid) {
        if (!Helpers.isBindUid()) {
            return;
        }
        //用pid来区分1000uid的流量,其它的用uid来区分
        int pid = ProcessUtil.getPid(mContext, mInfo.mPackage);
        logD("setTaskUid  " + mInfo.mPackage + " ==>" + pid);
        if(uid != 1000) {
            pid = -1;
        }
        int ret = getManager().setTaskUidWithPid(getTaskId(), uid, pid);
        logD("setTaskUid  ret=" + ret + " mUid=" + uid + " pid=" + pid);
    }

    private void reportTask(State state, XLTaskInfo taskInfo, int queryMode) {
        state.mCurrentBytes = taskInfo.mDownloadSize;
        state.mTotalBytes = taskInfo.mFileSize;
        state.mDownloadingCurrentSpeed = taskInfo.mDownloadSpeed;
        state.mDownloadSurplustime = taskInfo.mP2SSpeed;
        if (!isReport && state.mCurrentBytes > 0) {
            long end = System.currentTimeMillis();
            Statistics.printCallTime("reportTask", loopStart, end, mInfo.mId, "mTaskId=" + mTaskId);
            isReport = true;
        }
        if(!XLConfig.isDebug()) {
            needShowProgressLog = false;
            if (count == samplingInterval) {
                needShowProgressLog = true;
                count = 0;
            }
            count++;
        }else {
            needShowProgressLog = true;
        }
        if(needShowProgressLog) {
            StringBuilder logStr = new StringBuilder();
            logStr.append("loopProgress "
                    + " downloadId:" + getDownloadId()
                    + " taskid:" + taskInfo.mTaskId
                    + ",filesize:" + taskInfo.mFileSize
                    + ",taskstatus:" + taskInfo.mTaskStatus
                    + ",errorcode:" + taskInfo.mErrorCode
                    + ",Query_index_status:" + taskInfo.mQueryIndexStatus);
            logStr.append("\n");
            logStr.append(",mDownloadSpeed:" + taskInfo.mDownloadSpeed
                    + ",mOriginSpeed:" + taskInfo.mOriginSpeed
                    + ",mP2PSpeed:" + taskInfo.mP2PSpeed
                    + ",mP2SSpeed:" + taskInfo.mP2SSpeed
                    + ",mScdnSpeed:" + taskInfo.mScdnSpeed
                    + ",mAdditionalResPeerSpeed:" + taskInfo.mAdditionalResPeerSpeed
                    + ",mAdditionalResVipSpeed:" + taskInfo.mAdditionalResVipSpeed);
            logStr.append("\n");
            logStr.append(",mDownloadSize:" + taskInfo.mDownloadSize
                    + ",mOriginRecvBytes:" + taskInfo.mOriginRecvBytes
                    + ",mP2PRecvBytes:" + taskInfo.mP2PRecvBytes
                    + ",mP2SRecvBytes:" + taskInfo.mP2SRecvBytes
                    + ",mScdnRecvBytes:" + taskInfo.mScdnRecvBytes
                    + ",mAdditionalResPeerBytes:" + taskInfo.mAdditionalResPeerBytes
                    + ",mAdditionalResVipRecvBytes:" + taskInfo.mAdditionalResVipRecvBytes);
            logInfo(logStr.toString());
        }

        if(!XLSpeedUpManager.getSpeedUpManager().isDcdnMode()) {
            state.mXlAccelerateSpeed = taskInfo.mAdditionalResVipSpeed + taskInfo.mAdditionalResPeerSpeed;
        }
        updateRules.saveSpeedUpValues(state,mInfo,taskInfo);
    }

    /**
     * 0 表示idle； 1表示running；2表示success；3表示failed；4表示stopped
     * 
     * @throws StopRequestException
     */
    private void checkTaskStatus(XLTaskInfo taskInfo) throws StopRequestException {
        int taskStatus = taskInfo.mTaskStatus;
        if (taskStatus == TASK_STATU_IDLE || taskStatus == TASK_STATU_RUNNING) {

        } else if (taskStatus == TASK_STATU_STOPPED || taskStatus == TASK_STATU_FAILED) {
            checkMountedState(taskInfo.mErrorCode);
            throwErrorException("download lib task statu failed or stopped "
                    + getErrorMsg(taskInfo.mErrorCode), taskInfo.mErrorCode);
        } else {
            throwErrorException("download lib statu is unkown taskStatus="
                    + taskStatus + " " + getErrorMsg(taskInfo.mErrorCode), taskInfo.mErrorCode);
        }

    }

    protected void checkMountedState(int retCode) throws StopRequestException {
        if (retCode != XLErrorCode.OPEN_FILE_ERR) {
            return;
        }
        if (mDownloadFile != null && !mStorageManager.isExternalSdcardMounted(mContext, mDownloadFile.getAbsolutePath())) {
            throw new StopRequestException(Downloads.Impl.STATUS_DEVICE_NOT_FOUND_ERROR,
                    "external media not mounted " + mDownloadFile.getAbsolutePath());
        }
    }

    protected void getAccelerationInfo(State state, int queryMode, XLTaskInfo taskInfo,
            VipTaskInfo vipTaskInfo) {
        String url = state.mRequestUri.toString();
        DownloadType type = XLDownloadHelper.getDownloadType(url);
        VipTaskParam vipTaskParam =
                new VipTaskParam(url, getRefUrl(),
                        getCookie(), "", "", type.ordinal(), 1);
        XLVipChannelManager vipManager = getVipManager();
        // add cdn query task mQueryIndexStatus==2表示查询到索引信息
        int ret = -1;
        logVipD("getAccelerationInfo " + getDownloadInfo()
                + " mQueryIndexStatus=" + taskInfo.mQueryIndexStatus);
        if (queryMode == 1 && taskInfo.mQueryIndexStatus == 3) {
            vipTaskParam.mSubTaskList[0] = new VipSubTaskParam("", taskInfo.mGcid,
                    taskInfo.mCid, taskInfo.mFileSize, 0);
            ret = vipManager.vipCommitCollectTask(vipTaskParam);
            logVipD("getAccelerationInfo " + getDownloadInfo()
                    + " vipCommitCollectTask ret=" + ret);
            queryMode = 0;
            accelerationChange(queryMode, "QueryIndexStatus == 3");
            hasTryAcceleration = true;
        } else if (queryMode == 1 && taskInfo.mQueryIndexStatus == 2 && !hasAddedXlCdnQueryTask) {
            vipTaskParam.mSubTaskList[0] = new VipSubTaskParam("", taskInfo.mGcid,
                    taskInfo.mCid, taskInfo.mFileSize, 0);
            VipGetTaskId vipTask = new VipGetTaskId();
            ret = vipManager.vipCreateEnterHighSpeedTask(vipTaskParam, vipTask);
            mVipTaskId = vipTask.getTaskId();
            logVipD("getAccelerationInfo " + getDownloadInfo()
                    + " vipCreateEnterHighSpeedTask ret=" + ret);
            if (ret != 0) {
                queryMode = 0;
                accelerationChange(queryMode, "vipCreateEnterHighSpeedTask failed ret=" + ret);
            }
            hasAddedXlCdnQueryTask = true;
        }
        if (getVipTaskId() == -1) {
            return;
        }

        // query cdn
        if (queryMode == 1 && hasAddedXlCdnQueryTask && !hasGetCdn) {
            ret = mXLVipChannelManager.vipGetHighSpeedTaskInfo(getVipTaskId(), vipTaskInfo);
            logVipD("getAccelerationInfo " + getDownloadInfo()
                    + " vipGetHighSpeedTaskInfo "
                    + ",mResult=" + vipTaskInfo.mResult
                    + ",mTokenError=" + vipTaskInfo.mTokenError
                    + ",ret =" + ret);
            if (0 == ret) {
                hasGetCdn = true;
                VipSubTaskInfo subTaskInfo = vipTaskInfo.mSubTaskInfo[0];
                if (subTaskInfo.mCdnUrl != null && subTaskInfo.mCdnCookie != null) {
                    ServerResourceParam respara = new ServerResourceParam(subTaskInfo.mCdnUrl,
                            "", subTaskInfo.mCdnCookie, 20, 0);
                    ret = getManager().addServerResource(getTaskId(), respara);
                    logVipD("getAccelerationInfo " + getDownloadInfo()
                            + " addServerResource ret=" + ret);
                } else {
                    queryMode = 0;
                    accelerationChange(queryMode, "CdnUrl or dnCookie is null");
                }
                int vipDestoryHighSpeedTask = mXLVipChannelManager
                        .vipDestoryHighSpeedTask(getVipTaskId());
                logVipD("getAccelerationInfo " + getDownloadInfo()
                        + " vipDestoryHighSpeedTask ret=" + vipDestoryHighSpeedTask);
                hasTryAcceleration = true;
            } else {
                if (ret == 400
                        && (vipTaskInfo.mTokenError == 707 || vipTaskInfo.mTokenError == 706)) {
                    TokenHelper.getInstance().requestToken();
                    queryMode = 0; // token过期时 当前任务不可再加速
                    accelerationChange(queryMode, "vipGetHighSpeedTaskInfo failed ret=" + ret
                            + " token error(" + vipTaskInfo.mTokenError + ")");
                    hasTryAcceleration = true;
                }
            }
        }
    }

    protected void accelerationChange(int queryMode, String msg) {
        logVipD("accelerationChange queryMode=" + queryMode + " msg=" + msg);
    }

    /**
     * used by xunlei engine return filename
     *
     * @param taskid
     * @return
     * @throws StopRequestException
     */
    protected void getDownloadHeader(long taskid, State state) throws StopRequestException {
        int ret;
        GetDownloadHead header = new GetDownloadHead();
        int time = 0;
        long start = System.currentTimeMillis();
        long duration = System.currentTimeMillis() - start;
        while (duration < MAX_GET_DOWNLOAD_HEADER_DELAY) {
            try {
                Thread.sleep(200);
            } catch (Exception e) {
                // TODO: handle exception
            }
            duration = System.currentTimeMillis() - start;
            ret = mXlDownloadManager.getDownloadHeader(taskid, header);
            if (XLErrorCode.NO_ERROR != ret) {
                int errorCode = ret;
                if (XLErrorCode.TASK_NOT_RUNNING == ret) {
                    XLTaskInfo taskInfo = new XLTaskInfo();
                    mXlDownloadManager.getTaskInfo(taskid, 0, taskInfo);
                    errorCode = taskInfo.mErrorCode;
                }
                throwException("XLGetDownloadHeader", errorCode);
            }
            ++time;
            logD("getDownloadHeader " + getDownloadInfo()
                    + " xunlei return state=" + header.mHttpState
                    + ", times=" + time + " duration=" + duration);
            if (header.mHttpState == XLDownloadHeaderState.GDHS_SUCCESS.ordinal()) {
                final int responseCode = processDownloadHeader(state, header.mHttpResponse);
                switch (responseCode) {
                    case HTTP_OK:
                    case HTTP_PARTIAL:
                    case HTTP_REQUESTED_RANGE_NOT_SATISFIABLE:
                        processResponseHeaders(state, null);
                        return;
                    case HTTP_UNAVAILABLE:
                        parseRetryAfterHeaders(state, null);
                        throw new StopRequestException(
                                HTTP_UNAVAILABLE,
                                "xunlei - http return 503 when getting download header.");
                    case HTTP_INTERNAL_ERROR:
                        throw new StopRequestException(
                                HTTP_INTERNAL_ERROR,
                                "xunlei - http return 500 when getting download header.");
                    default:
                        StopRequestException.throwUnhandledHttpError(
                                responseCode, "xunlei - http return " + responseCode
                                        + " when getting download header.");
                }

                break;
            } else if (header.mHttpState == XLDownloadHeaderState.GDHS_ERROR.ordinal()) {
                Map<String, String> headers = new TreeMap<String, String>(String.CASE_INSENSITIVE_ORDER);
                processDownloadHeaderField(headers, header.mHttpResponse);
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    XLConfig.LOGD("key= " + entry.getKey() + " and value= " + entry.getValue());
                }
                int code = TASK_HEADER_STATE_ERROR;
                int statuCode = getStatuCode(headers);
                if (statuCode != -1) {
                    code = statuCode;
                }
                throwErrorException("XLGetDownloadHeader http error headers=" + headers, code);
            }

            checkPausedOrCanceled(state);
        }

        throw new StopRequestException(STATUS_TOO_MANY_REDIRECTS,
                "xunlei - Too many redirects to get header.");
    }


    public int getStatuCode(Map<String, String> headers) {
        int rCode = -1;
        String code = headers.get("ResponseCode");
        try {
            rCode = Integer.parseInt(code);
        } catch (Exception e) {
        }
        return rCode;
    }

    /**
     * @param state
     * @param response
     * @return
     * @throws StopRequestException
     */
    protected int processDownloadHeader(State state, final String response)
            throws StopRequestException {
        if(TextUtils.isEmpty(response)){
            return -1;
        }
        logD("processDownloadHeader---> response\n"+ response);
        int responseCode = -1;
        if (response.startsWith("HTTP")) {
            Map<String, String> headers = new TreeMap<String, String>(String.CASE_INSENSITIVE_ORDER);
            processDownloadHeaderField(headers, response);

            String code = headers.get("ResponseCode");
            try {
                responseCode = Integer.parseInt(code);
            } catch (Exception e) {
                XLConfig.LOGD(TAG,
                        "(processDownloadHeader) ---> NumberFormatException happen in parse response code = "
                                + code);
            }

            XLConfig.LOGD(TAG, "(processDownloadHeader) ---> int responseCode="
                    + responseCode);

            if (responseCode == HTTP_OK ||
                    responseCode == HTTP_PARTIAL ||
                    responseCode == HTTP_REQUESTED_RANGE_NOT_SATISFIABLE) {
                state.mResourceLine = headers.get("ResourceLine");
                state.mContentDisposition = headers.get("Content-Disposition");
                state.mContentLocation = headers.get("Content-Location");
                if (state.mMimeType == null) {
                    String mimeType = headers.get("Content-Type");
                    if (!TextUtils.isEmpty(mimeType)) {
                        state.mMimeType = mimeType;
                        state.mMimeTypeFromHead = true;
                    }
                }
                state.mHeaderETag = headers.get("ETag");
                state.mHeaderIfRangeId = headers.get("Last-Modified");
                state.mHeaderAcceptRanges = headers.get("Accept-Ranges");

                try {
                    state.mRetryAfter = Integer.parseInt(headers.get("Retry-After"));
                } catch (Exception e) {
                    state.mRetryAfter = -1;
                }

                String transferEncoding = headers.get("Transfer-Encoding");
                if (TextUtils.isEmpty(transferEncoding)) {
                    String lengthField = null;
                    if (responseCode == HTTP_PARTIAL) {
                        lengthField = headers.get("Content-Range");
                        if (!TextUtils.isEmpty(lengthField)) {
                            int index = lengthField.indexOf("/");
                            if (index != -1) {
                                lengthField = lengthField.substring(index + 1);
                            }
                        }
                    } else {
                        lengthField = headers.get("Content-Length");
                    }

                    try {
                        if (lengthField == null) {
                            lengthField = headers.get("Accept-Length");
                        }
                        state.mContentLength = Long.parseLong(lengthField);
                    } catch (Exception e) {
                        state.mContentLength = -1;
                    }
                } else {
                    state.mContentLength = -1;
                }

                state.mTotalBytes = state.mContentLength;
                mInfo.mTotalBytes = state.mContentLength;

                XLConfig.LOGD(TAG, " processDownloadHeader ---> filesize="
                        + state.mTotalBytes);

                final boolean hasLength = state.mContentLength != -1;
                final boolean isConnectionClose = "close".equalsIgnoreCase(headers.get("Connection"));
                final boolean isEncodingChunked = "chunked".equalsIgnoreCase(headers.get("Transfer-Encoding"));
                final boolean finishKnown = hasLength || isConnectionClose || isEncodingChunked;
                if (!finishKnown) {
                    throw new StopRequestException(STATUS_CANNOT_RESUME,
                            "can't know size of download, giving up");
                }
            }
        }

        return responseCode;
    }

    /**
     * Decode http headers which return by download lib.
     */
    protected void processDownloadHeaderField(Map<String, String> headers, String response) throws StopRequestException {
        if (TextUtils.isEmpty(response) || headers == null) {
            return;
        }

        try {
            String[] lines = response.split("\r\n");
            if (lines.length > 0) {
                // decode status line
                String[] status = lines[0].split(" ");
                if (status.length > 1) {
                    headers.put("ResponseCode", status[1]);
                }
                if (status.length > 2) {
                    headers.put("xl_response",lines[0]);
                }

                // decode header field
                for (int i = 1; i < lines.length; i++) {
                    String line = lines[i];
                    if (!TextUtils.isEmpty(line)) {
                        int pos = line.indexOf(':');
                        if (pos == -1) {
                            throwErrorException("processDownloadHeaderField fail line=" + line, TASK_HEADER_PARSER_FAIL);
                        }
                        String name = line.substring(0, pos);
                        if (TextUtils.isEmpty(name)) {
                            throwErrorException("processDownloadHeaderField fail line=" + line, TASK_HEADER_PARSER_FAIL);
                        } else {
                            int valuePos = name.length() + 1;
                            if (line.length() > name.length() + 1) {
                                headers.put(name, line.substring(valuePos).trim());
                            }
                        }
                    }
                }
            }
            logD("processDownloadHeaderField header\n" + headers.toString());
        } catch (Exception e) {
            String msg = (e != null && e.getMessage() != null) ? e.getMessage() : "";
            throwException("processDownloadHeaderField fail msg=" + msg, e);
        }
    }

    protected void processResponseHeaders(State state, HttpURLConnection conn)
            throws StopRequestException {
        // TODO: fallocate the entire file if header gave us specific length

        if (conn != null) {
            readResponseHeaders(state, conn);
        }
        //对于下载库而言不需要生成下载中(*.download)的文件
        state.mFilename = Helpers.generateSaveFile(
                mContext,
                mInfo.mUri,
                mInfo.mHint,
                state.mContentDisposition,
                state.mContentLocation,
                state.mMimeType,
                mInfo.mDestination,
                state.mContentLength,
                mStorageManager,
                false,
                needFixExtension(mInfo.mPackage));
        // correct mimetype
        correctMimeType(state);
        // update header values into database
        updateDatabaseFromHeaders(state);
        // now we get filename, and check space.
        mStorageManager.verifySpace(mInfo.mDestination, state.mFilename, state.mTotalBytes);
        // check connectivity again now that we know the total size
        checkConnectivity(true);
        //https 证书检查
        if (mXlDownloadThreadDeletegation.checkHttpsResponse(mContext,mInfo.mPackage, state)) {
            throwErrorException("xunlei - https return 156 when getting download header:"+state.mResourceLine,156);
        }
    }

    protected boolean canAcceleration() {
        boolean canAcceleration = false;

        if (Helpers.isTablet()) {
            return canAcceleration;
        }
        if (!DownloadSettings.XLShareConfigSettings.isVipEnable()) {
            XLConfig.LOGW(TAG,
                    "canAcceleration IsVipEnabled is false");
            return canAcceleration;
        }

        String token = DownloadSettings.XLShareConfigSettings.getXLVipToken("");
        if (TextUtils.isEmpty(token)) {
            XLConfig.LOGW(TAG,
                    "canAcceleration token is empty");
            return canAcceleration;
        }
        canAcceleration = true;
        int vipSetUserToken = mXLVipChannelManager.vipSetUserToken(token);
        logD("canAcceleration vipSetUserToken token=" + token + " ret="
                + vipSetUserToken);
        return canAcceleration;
    }

    @Override
    protected void stopTask(int finalStatus, int ret) throws StopRequestException {
        long taskId = getTaskId();
        XLDownloadManager manager = getManager();
        if (manager == null || taskId == -1) {
            return;
        }

        //以下载库的错误码为主
        int finalRet = -1;
        if (finalStatus == XL_STATUS__FAIL) {
            finalRet = ret;
        } else {
            finalRet = finalStatus;
        }
        if (finalRet == 0) {//一些异常情况下没有已知的错误码的情况下，用原生的状态码上报
            finalRet = finalStatus;
        }
        int stopTask = manager.stopTaskWithReason(taskId, finalRet);
        logD("stopTask ret =" + stopTask + " finalStatus=" + finalStatus + " ret=" + ret + " finalRet=" + finalRet);
        XLStatistics.trackDownloadConn(mContext, mXlDownloadManager, taskId, mState, finalStatus, ret);
        int releaseTask = manager.releaseTask(taskId);
        XLSpeedUpManager.getSpeedUpManager().removeSpeedUpTaskInfo(mInfo.mId);
        logD("releaseTask ret ="+ releaseTask);
        ScdnHelper.getInstance().stopScdnTask(mInfo.mUri);
        SpeedupManager2.getInstance().releaseTask(mInfo.mId, mGcid, finalRet);
    }

    protected String getErrorDetail(int errorCode) {
        return String.format("%s(%d)", getErrorMsg(errorCode), errorCode);
    }

    protected String getErrorMsg(int mErrorCode) {
        return getManager().getErrorCodeMsg(mErrorCode);
    }

    protected String generateUniqueFilename(String path) throws StopRequestException {
        return Helpers.getUniqueFilename(path);
    }

    protected void throwException(String msg, Throwable t) throws StopRequestException {
        t.printStackTrace();
        throw new StopRequestException(XL_STATUS__FAIL, msg,t);
    }

    //为了方便统计，一定要有错误码,这个是自定义的错误码
    protected void throwErrorException(String msg, int errorCode) throws StopRequestException {
        throw new StopRequestException(XL_STATUS__FAIL, errorCode, String.format(
                "%d:" + msg,
                errorCode));
    }

    //这个是当下载库调用失败调用
    protected void throwException(String method, int ret) throws StopRequestException {
        if (ret != XLErrorCode.NO_ERROR) {
            final int status = ret == ERROR_CODE_INSUFFICIENT_DISK_SPACE ?
                    STATUS_INSUFFICIENT_SPACE_ERROR : XL_STATUS__FAIL;
            throw new StopRequestException(status, ret, String.format(
                    "%d:call %s fail %s",
                    ret, method, getErrorMsg(ret)));
        }
    }

    protected void logVipD(String msg) {
        logD(VIP_TAG + " " + msg);
    }

    protected void logD(String msg) {
        XLConfig.LOGD(this.getClass().getSimpleName(), getTask() + " " + msg);
    }

    private void logInfo(String msg) {
        XLConfig.LOGD_INFO(this.getClass().getSimpleName(), getTask() + " " + msg);
    }

    private String getTask() {
        return "mId=" + getDownloadId() + " mTaskId=" + getTaskId();
    }

    private State mState;
    protected void trackTaskStart(State state) {
        mState = state;
        mDownloadFile = state.mFilename != null ? new File(state.mFilename) : null;
        long currentBytes = (mDownloadFile != null && mDownloadFile.exists()) ? mDownloadFile
                .length()
                : state.mCurrentBytes;

        String fileName = mDownloadFile != null ? mDownloadFile.getName() : "";
        mStartSize = currentBytes > 0 ? currentBytes : 0;
        mStartTime = System.currentTimeMillis();
        // XLConfig.LOGD(TAG,"Statistics", " mCurrentBytes=" +
        // state.mCurrentBytes +
        // " fileSize=" + mStartSize);
        Statistics.trackDownloadStart(mContext, state, true, fileName, mStartSize, wakelockTime);
    }

    protected void trackTaskStop(State state, int finalStatus, int soRet, String errorMsg) {
        mDownloadFile = state.mFilename != null ? new File(state.mFilename) : null;
        long fileSize = (mDownloadFile != null && mDownloadFile.exists()) ? mDownloadFile
                .length() : state.mCurrentBytes;
        String fileName = mDownloadFile != null ? mDownloadFile.getName() : "";
        long currentBytes = fileSize > state.mCurrentBytes ? fileSize : state.mCurrentBytes;
        long downloadSize = currentBytes - mStartSize;
        long downloadTime = System.currentTimeMillis() - mStartTime;
        // XLConfig.LOGD(TAG,
        // "Statistics",
        // " mCurrentBytes=" + currentBytes + " fileSize=" + fileSize +
        // " finalStatus="
        // + Downloads.Impl.statusToString(finalStatus) + "(" + finalStatus +
        // ")" + " errorMsg="
        // + (TextUtils.isEmpty(errorMsg) ? "null" : errorMsg));
        Statistics.trackDownloadStop(mContext, state, true, fileName,
                downloadTime, downloadSize, mStartSize, mInfo.mDeleted, finalStatus, soRet, errorMsg, wakelockTime);
    }

    @Override
    public boolean checkServerTrusted(byte[][] chains, String authType, String hostName) {
        return mXlDownloadThreadDeletegation.checkServerTrusted(mContext,chains,authType,hostName,mInfo,mState);
    }
}
