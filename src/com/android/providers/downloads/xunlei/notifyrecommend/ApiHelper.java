package com.android.providers.downloads.xunlei.notifyrecommend;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import com.android.providers.downloads.api.notifyrecommend.AppInitParam;
import com.android.providers.downloads.api.notifyrecommend.AppInitRequest;
import com.android.providers.downloads.api.notifyrecommend.NotifyRecommendRequest;
import com.android.providers.downloads.api.notifyrecommend.NotifyRecommendResponse;
import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.utils.InternetUtil;
import com.michael.corelib.internet.core.NetWorkException;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * Created by lvxia on 16/1/6.
 */
public final class ApiHelper {
    /**
     * mcp app.init
     */
    public static void initAppApiData(final Context context) {
        //先确认是否需要调用初始化接口
        if (!ApiHelper.needInitApp(context)) {
            return;
        }
        final AppInitParam params = AppInitParam.getInstance();
        final AppInitRequest request = new AppInitRequest(params);

        try {
            String response = InternetUtil.syncRequest(context, request);
            JSONObject jsonResponse = new JSONObject(response);
            if (jsonResponse != null) {
                int result = jsonResponse.getInt("result");
                if (result == 0) {
                    ApiHelper.saveInitApp(context);
                }
            }
        } catch (NetWorkException e) {
            e.printStackTrace();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /***
     * get notify recommend
     * @param context
     * @param keyword
     * @param recType
     * @return
     */
    public static NotifyRecommendResponse getNotifyRecommend(final Context context, String mainName, final String recType,String packageName,String mUrl,String infoHash) {
        if (mainName == null || mainName.isEmpty()) {
            return null;
        }

        mainName = encodeString(mainName);

        final NotifyRecommendRequest request = new NotifyRecommendRequest(
                AppInitParam.getInstance().getDeviceId(),
                mainName,
                recType,mUrl,infoHash,packageName);
        XLConfig.LOGD(NotifyRecommendManager.TAG,request.toString());
        NotifyRecommendResponse response = null;
        try {
            response = InternetUtil.syncRequestNeedInit(context, request);
        } catch (NetWorkException e) {
            e.printStackTrace();
        }

        return response;
    }


    /***
     * 判断是否需要调用init接口
     * @param context
     * @return true:调用|false:不调用
     */
    public static boolean needInitApp(Context context){
        //根据init参数md5是否变化判断是否需要调用init接口
        return !DownloadSettings.XLSecureConfigSettings.getAppInitApiParams("").equals(AppInitParam.getInstance().getParamsMd5());
    }

    /***
     * app.init 接口调用成功，保存当前api版本号
     * @param context
     */
    public static void saveInitApp(Context context){
        //根据init参数md5是否变化判断是否需要调用init接口
        DownloadSettings.XLSecureConfigSettings.setAppInitApiParams(AppInitParam.getInstance().getParamsMd5());
    }


    /***
     * url encode
     * @param url
     * @return
     */
    public static String encodeString(String url) {
        if (TextUtils.isEmpty(url)) {
            return url;
        }
        try {
            return URLEncoder.encode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return url;
    }
}
