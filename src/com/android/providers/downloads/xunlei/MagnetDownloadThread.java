
package com.android.providers.downloads.xunlei;

import static com.android.providers.downloads.config.XLDownloadCfg.TAG;

import java.io.File;

import android.content.Context;
import android.net.Uri;
import android.util.Log;
import android.widget.Toast;

import com.android.providers.downloads.StorageManager;
import com.android.providers.downloads.SystemFacade;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.exception.StopRequestException;
import com.android.providers.downloads.model.State;
import com.android.providers.downloads.service.DownloadInfo;
import com.android.providers.downloads.service.DownloadNotifier;
import com.android.providers.downloads.util.Helpers;
import com.xunlei.downloadlib.XLDownloadManager;
import com.xunlei.downloadlib.parameter.GetTaskId;
import com.xunlei.downloadlib.parameter.MagnetTaskParam;
import com.xunlei.downloadlib.parameter.XLConstant.XLErrorCode;
import com.xunlei.downloadlib.parameter.XLTaskInfo;
import com.xunlei.vipchannel.XLVipChannelManager;

public class MagnetDownloadThread extends XLDownloadThread implements XLDownloadCfg {

    private long mLastRecvDataByte = 0;
    private long mLastRecvTime;

    public MagnetDownloadThread(Context context, SystemFacade systemFacade, DownloadInfo info,
            StorageManager storageManager, DownloadNotifier notifier, XLDownloadManager dm,
            XLVipChannelManager vm) {
        super(context, systemFacade, info, storageManager, notifier, dm, vm);
    }

    protected void executeDownload(State state) throws StopRequestException {
        // public synchronized void startMagnetTask(final String url, final
        // String filename) {
        XLDownloadManager xlDownload = getManager();
        if (xlDownload == null) {
            throwErrorException("xlDownload is null", TASK_MANAGER_IS_NULL);
        }
        GetTaskId task = new GetTaskId();
        if (state.mCurrentBytes <= 0) {
            state.mFilename = generateUniqueFilename(Uri.parse(mInfo.mHint).getPath());
            updateDatabaseFromHeaders(state);
        }
        File file = new File(state.mFilename);
        // step1：初始化任务参数
        MagnetTaskParam para = new MagnetTaskParam(file.getName(),
                file.getParent(), state.mRequestUri);

        // step2：创建任务
        int btMagnetTask = xlDownload.createBtMagnetTask(para, task);
        mTaskId = task.getTaskId();
        if (btMagnetTask != XLErrorCode.NO_ERROR) {
            Log.i(XLDownloadCfg.TAG, "createBtMagnetTask failed, ret=" + btMagnetTask);
            xlDownload.releaseTask(getTaskId());
            throwException("createBtMagnetTask", btMagnetTask);
        }

        setTaskOrigin(state.mPackage);
        setTaskUid(mInfo.mUid);
        // step3:开始任务（createTask和startTask必须成对使用）
        int startTask = xlDownload.startTask(getTaskId());
        if (startTask != XLErrorCode.NO_ERROR) {
            Log.i(XLDownloadCfg.TAG, "startTask failed, ret=" + startTask);
            throwException("startTask", startTask);
        }
        mDownloadDestFile = file;
        // step4：获取任务信息
        loopProgress(state);
    }

    private void loopProgress(State state) throws StopRequestException {
        XLTaskInfo taskInfo = new XLTaskInfo();
        while (true) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            checkPausedOrCanceled(state);
            checkFileExists(mDownloadDestFile, state);
            long duringTime = mLastRecvTime - System.currentTimeMillis() / 1000;
            int ret = getManager().getTaskInfo(getTaskId(), 0, taskInfo);
            if (ret != XLErrorCode.NO_ERROR) {
                continue;
            }
            long recvDataByte = taskInfo.mDownloadSize - mLastRecvDataByte;
            if (recvDataByte > 0 || taskInfo.mTaskStatus != TASK_STATU_RUNNING
                    || taskInfo.mDownloadSpeed != mPreDownloadSpeed
                    || state.mTotalBytes != taskInfo.mFileSize) {
                //因为文件很小所以只要有变化就更新
                mLastRecvTime = System.currentTimeMillis() / 1000;
                mLastRecvDataByte = taskInfo.mDownloadSize;
                state.mCurrentBytes = taskInfo.mDownloadSize;
                state.mTotalBytes = taskInfo.mFileSize;
                state.mDownloadingCurrentSpeed = taskInfo.mDownloadSpeed;
                state.mDownloadSurplustime = taskInfo.mP2SSpeed;
                logD("loopProgress"
                        + ", taskid:" + getTaskId()
                        + ", curDownloadSize:" + taskInfo.mDownloadSize
                        + ", filesize:" + taskInfo.mFileSize
                        + ", curSpeed:" + taskInfo.mDownloadSpeed
                        + ", p2sSpeed:" + taskInfo.mP2SSpeed
                        + ", taskStatus:" + taskInfo.mTaskStatus);
                reportProgress(state);
            }

            // mTaskStatus： 0 表示idle； 1表示running；2表示success；3表示failed；4表示stopped
            int taskStatus = taskInfo.mTaskStatus;
            if (taskStatus == TASK_STATU_IDLE || taskStatus == TASK_STATU_RUNNING) {
                if (duringTime >= 300) {
                    throwErrorException("download lib task time out", TASK_STATU_TIME_OUT);
                }
            } else if (taskStatus == TASK_STATU_STOPPED || taskStatus == TASK_STATU_FAILED) {
                checkMountedState(taskInfo.mErrorCode);
                throwErrorException("download lib task statu failed or stopped",
                        taskInfo.mErrorCode);
            } else if (taskStatus == TASK_STATU_SUCCESS) {
                break;
            } else {
                throwErrorException("download lib statu is unkown taskStatus=" + taskStatus,
                        TASK_STATU_UNKOWN);
            }
            checkSpace(state,taskInfo);
        }
    }

}
