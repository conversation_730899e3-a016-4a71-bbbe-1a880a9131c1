package com.android.providers.downloads.speedup;

import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.statistics.TraceReport;
import com.android.providers.downloads.utils.LogUtil;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.michael.corelib.coreutils.CustomThreadPool;
import com.michael.corelib.coreutils.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.FutureTask;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import xunlei.os.SystemProperties;
import xunlei.util.Timer;

public class SpeedupCore {
    private static final String TAG = "SpeedupCore";
    public static long KEEP_ALIVE_INTERVAL = 15 * 1000;

    private final static String APPID = "xiaomi_01";
    private final static String APPKEY = "UdotVFXJMZAwvFjB";
    private final static String PEER_ID = "98A7F8B4157C005A";
    private final static String CLIENT_PORT = "12345";
    private final static String CLIENT_TYPE = "android-swjsq-*******";
    private final static String SESSION_ID = "ws001.EAE884748B9B29A602BBA5187D4F8A2E";

    private final static String URL_SPEEDUP_HOST = "http://api.upportal.swjsq.vip.xunlei.com:12180";
    private final static String URL_PREFIX_BANDWIDTH = URL_SPEEDUP_HOST + "/v2/bandwidth";
    private final static String URL_PREFIX_UPGRADE = URL_SPEEDUP_HOST + "/v2/upgrade";
    private final static String URL_PREFIX_RECOVER = URL_SPEEDUP_HOST + "/v2/recover";
    private final static String URL_PREFIX_KEEPALIVE = URL_SPEEDUP_HOST + "/v2/keepalive";

    private final static String SPEEDUP_CFG_IP = "debug.speedup.cfg_ip";

    private String mDialAccount;
    private String mUid;
    private String mClientIP;
    private long mKeepAliveInterval;
    private SpeedupListener mSpeedupListener;
    private ExecutorService executorService = new ThreadPoolExecutor(1, 1, 0L,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>());

    public SpeedupCore() {
        mUid = getUniqueId();
        mClientIP = SystemProperties.get(SPEEDUP_CFG_IP, "");
        mKeepAliveInterval = KEEP_ALIVE_INTERVAL;
    }

    private String getUniqueId() {
        String uid = TraceReport.getDeviceID();
        uid = TextUtils.isEmpty(uid) ? "*********" : "999" + uid;
        return uid;
    }

    private String buildCanSpeedupUrl(String userId, String clientIp) {
        String urlParam = buildParam(userId, clientIp);
        return String.format(URL_PREFIX_BANDWIDTH + "?%s", urlParam);
    }

    private String buildSpeedupUrl(String userId, String clientIp, String dial_account) {
        String urlParam = buildAcountParam(userId, clientIp, dial_account);
        return String.format(URL_PREFIX_UPGRADE + "?%s", urlParam);
    }


    private String buildRemoveSpeedUpUrl(String userId, String clientIp, String dial_account) {
        String urlParam = buildAcountParam(userId, clientIp, dial_account);
        return String.format(URL_PREFIX_RECOVER + "?%s", urlParam);
    }

    private String buildKeepAliveUrl(String userId, String clientIp, String dial_account) {
        String urlParam = buildAcountParam(userId, clientIp, dial_account);
        return String.format(URL_PREFIX_KEEPALIVE + "?%s", urlParam);
    }

    public NetResponse getNetResponse(String url) {
        try {
            return getNetResponseThrowExc(url);
        } catch (IOException e) {
            e.printStackTrace();
            return new NetResponse(499, e.getMessage());
        }
    }

    public NetResponse getNetResponseThrowExc(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .build();
        logD(String.format("request==>\n%s", url));
        String msg;
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(6, TimeUnit.SECONDS)
                .readTimeout(6, TimeUnit.SECONDS)
                .writeTimeout(6, TimeUnit.SECONDS)
                .build();
        Response response = client.newCall(request).execute();
        response.code();
        msg = response.body().string();
        logD(String.format("response==>\n%s", msg));
        return new NetResponse(response.code(), msg);
    }

    public class NetResponse {
        int code;
        String rawResponse;

        NetResponse(int code, String response) {
            this.code = code;
            this.rawResponse = response;
        }

        public boolean isSuccessful() {
            return this.code >= 200 && this.code < 300;
        }
    }

    public String buildAcountParam(String userId, String clientIp, String dial_account) {
        String params = buildParam(userId, clientIp);
        String fmtMsg = String.format(params + "&dial_account=%s", dial_account);
        return fmtMsg;
    }

    public String buildParam(String userId, String clientIp) {
        long time = System.currentTimeMillis();
        String scretId = buildScretId(userId, time, clientIp);
        if (TextUtils.isEmpty(mClientIP)) {
            return String.format("peerid=%s&app_id=%s&userid=%s&client_type=%s&clientport=%s&timestamp=%s&secret_id=%s&sessionid=%s",
                    PEER_ID, APPID, userId, CLIENT_TYPE, CLIENT_PORT, String.valueOf(time), scretId, SESSION_ID);
        }
        return String.format("peerid=%s&app_id=%s&userid=%s&client_type=%s&clientip=%s&clientport=%s&timestamp=%s&secret_id=%s&sessionid=%s",
                PEER_ID, APPID, userId, CLIENT_TYPE, clientIp, CLIENT_PORT, String.valueOf(time), scretId, SESSION_ID);
    }

    public String buildScretId(String userId, long timeStamp, String clientIp) {
        String fmtMsg = String.format("%s+%s+%s+%s+%s", APPID, userId, String.valueOf(timeStamp), clientIp, APPKEY);
        String secretId = StringUtils.generateMD5String(fmtMsg);
        return secretId;
    }

    public void speeup() {
        CustomThreadPool.asyncWork(new Runnable() {
            @Override
            public void run() {
                trySpeeup();
            }
        });
    }

    public void addSpeedupListener(SpeedupListener callback) {
        mSpeedupListener = callback;
    }

    public void speedupStop() {
        if (mKeepAlive != null && !mKeepAlive.isStoped()) {
            mKeepAlive.quit();
            CustomThreadPool.asyncWork(new Runnable() {
                @Override
                public void run() {
                    String url = buildRemoveSpeedUpUrl(mUid, mClientIP, mDialAccount);
                    getNetResponse(url);
                    if (mSpeedupListener != null) {
                        mSpeedupListener.onSpeedupRemove();
                    }
                }
            });
        }
    }

    public String getUid() {
        return mUid;
    }

    public interface SpeedupListener {
        void onSpeedupSuccess(Bandwidth bandwidth);

        void onSpeedupFail(int ret, String msg);

        void onSpeedupRemove();
    }

    public void trySpeeup() {
        try {
            boolean canUpgrade = false;
            boolean upgradeOk = false;
            String erroMsg = "speed up fail";
            BandwidthResponse res = null;
            NetResponse response = getFutureTaskResponse(buildCanSpeedupUrl(mUid, mClientIP));
            String responseStr = response.rawResponse;
            if (response.isSuccessful() && !TextUtils.isEmpty(responseStr)) {
                res = fromJson(responseStr, BandwidthResponse.class);
                canUpgrade = res.canUpgrade();
                mDialAccount = res.dialAccount;
                reportBandwidthApi(res.errno, res.message);
            } else {
                erroMsg = responseStr;
            }
            if (canUpgrade) {
                NetResponse vResponse = getFutureTaskResponse(buildSpeedupUrl(mUid, mClientIP, mDialAccount));
                String vResponseStr = vResponse.rawResponse;
                if (vResponse.isSuccessful() && !TextUtils.isEmpty(vResponseStr)) {
                    UpgradeResponse resUpgrade = fromJson(vResponseStr, UpgradeResponse.class);
                    upgradeOk = resUpgrade.errno == 0;
                    reportUpgradeApi(resUpgrade.errno, resUpgrade.message);
                } else {
                    erroMsg = vResponseStr;
                }
            }
            if (upgradeOk && res != null) {
                if (mSpeedupListener != null) {
                    mSpeedupListener.onSpeedupSuccess(Bandwidth.fillBandwidth(res));
                }
            } else {
                if (mSpeedupListener != null) {
                    mSpeedupListener.onSpeedupFail(1000, erroMsg);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (mSpeedupListener != null) {
                mSpeedupListener.onSpeedupFail(1000, e.getMessage());
            }
        }
    }

    public <T> T fromJson(String json, Class<T> classOfT) throws JsonSyntaxException {
        return new Gson().fromJson(json, classOfT);
    }

    private Timer mKeepAlive;

    public void keepAlive() {
        synchronized (this) {
            if (mKeepAlive == null) {
                mKeepAlive = new Timer("SpeedupKeepAlive");
            }
        }
        mKeepAlive.schedule(new Runnable() {
            @Override
            public void run() {
                String url = buildKeepAliveUrl(mUid, mClientIP, mDialAccount);
                getNetResponse(url);
            }
        }, 0, mKeepAliveInterval);
    }

    private NetResponse getFutureTaskResponse(String url) throws InterruptedException, ExecutionException, TimeoutException {
        FutureTask<NetResponse> future = new FutureTask<NetResponse>(
                new Callable<NetResponse>() {
                    public NetResponse call() throws Exception {
                        return getNetResponse(url);
                    }
                });
        executorService.execute(future);
        return future.get(10, TimeUnit.SECONDS);
    }

    private void reportBandwidthApi(int errorCode, String msg) {
        reportSpeedupApi("speedup_bandwidth", errorCode, msg);
    }

    private void reportUpgradeApi(int errorCode, String msg) {
        reportSpeedupApi("speedup_upgrade", errorCode, msg);
    }

    private void reportSpeedupApi(String api, int errorCode, String msg) {
        Map<String, String> data = new HashMap<String, String>();
        data.put("api_name", api);
        data.put("speedup_uid", getUid());
        data.put("error_code", String.valueOf(errorCode));
        data.put("error_msg", msg);
        Context ctx = DownloadApplication.getGlobalApplication();
        Statistics.reportSpeedupApi(ctx, data);
    }

    private void logD(String msg) {
        if (LogUtil.debugSpeedup()) {
            LogUtil.log(TAG, msg);
        }
    }
}
