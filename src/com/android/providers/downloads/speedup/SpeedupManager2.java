package com.android.providers.downloads.speedup;

import android.net.TrafficStats;
import android.util.Log;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.SharePreferenceHelper;
import com.android.providers.downloads.utils.LogUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import xunlei.util.Timer;

public class SpeedupManager2 implements XLDownloadCfg {
    private static final String TAG = "SpeedupManager2";
    private static SpeedupManager2 instance;
    private SpeedupCore mSpeedupCore;
    private final static int STATU_SPEEDUP_DEF = -1;
    private final static int STATU_SPEEDUP_TRY = 101;
    private final static int STATU_SPEEDUP_KEEPALIVE = 102;
    private final static int STATU_SPEEDUP_REMOVE = 103;
    private final static int STATU_SPEEDUP_SPEED_ONLY = 110;
    private final static int STATU_SPEEDUP_STOP = 200;
    private final static int STATU_SPEEDUP_FAIL = 201;
    private volatile static int mSpeedupStatu = STATU_SPEEDUP_DEF;

    private final static long UNIT_MINUTE = 60 * 1000;
    private final static long ONE_DAY = 24 * 60 * UNIT_MINUTE;

    private static long MAX_KEEP_ALIVE_TIME = 5 * 60 * 1000;
    private Timer mKeepAliveTask = new Timer();
    private long mTaskId;

    private static long SPEED_SAMPLING_INTERVAL = 2 * 1000;
    private Timer mSpeedTimer = new Timer();
    private int count = 0;
    private long lastTimeStamp;
    private long lastRxBytes = 0;
    private boolean isAddFirst = true;
    private boolean isFirst = true;

    //统计相关
    private long mAdd_max_speed;
    private long mAdd_min_speed;
    private long mMax_speed;
    private long mMin_speed;
    private ArrayList<Long> mAddSpeed;
    private Bandwidth mBandwidth;
    private HashMap<Long, TaskInfo> mTaskMap = new HashMap<>();

    public synchronized static SpeedupManager2 getInstance() {
        if (instance == null) {
            instance = new SpeedupManager2();
            synchronized (SpeedupManager2.class) {
                if (instance == null) {
                    instance = new SpeedupManager2();
                }
            }
        }
        return instance;
    }

    public SpeedupManager2() {
        mSpeedupCore = new SpeedupCore();
        mAddSpeed = new ArrayList<>();
        mSpeedupCore.addSpeedupListener(new SpeedupCore.SpeedupListener() {
            @Override
            public void onSpeedupSuccess(Bandwidth bandwidth) {
                mBandwidth = bandwidth;
                switchStatu(STATU_SPEEDUP_KEEPALIVE);
                usedSpeedup();
                mSpeedupCore.keepAlive();
                mKeepAliveTask.schedule(new Runnable() {
                    @Override
                    public void run() {
                        mSpeedupCore.speedupStop();
                    }
                }, MAX_KEEP_ALIVE_TIME);
            }

            @Override
            public void onSpeedupFail(int ret, String msg) {
                switchStatu(STATU_SPEEDUP_FAIL);
                usedSpeedup();
                logD(String.format("preSpeed ret=%d,msg=%s", ret, msg));
            }

            @Override
            public void onSpeedupRemove() {
                if (mSpeedupStatu == STATU_SPEEDUP_KEEPALIVE) {
                    switchStatu(STATU_SPEEDUP_REMOVE);
                }
            }
        });
    }

    public void trySpeedup(long taskId, String source, String gcid, long taskSize) {
        if (!CloudConfigPreference.getInstance().enableSpeedup()) {
            return;
        }
        if (isSpeeduping()) {
            addTask(taskId, "", source, taskSize);
            return;
        }

        if (!Helpers.isNetworkWifi(DownloadApplication.getGlobalApplication())) {
            return;
        }

        logD("trySpeedup test ");
        doSpeedup(taskId);

        addTask(taskId, gcid, source, taskSize);
    }

    private synchronized void doSpeedup(long taskId) {
        if (mSpeedupStatu < 0 || mSpeedupStatu >= 200) {
            mTaskId = taskId;
            mAddSpeed.clear();
            if (!outOfSpeedupCount()) {
                switchStatu(STATU_SPEEDUP_TRY);
                mSpeedupCore.trySpeeup();
                speedSampling();
            } else {
                switchStatu(STATU_SPEEDUP_SPEED_ONLY);
                speedSampling();
            }
        } else {
            logD("trySpeedup ... " + toStatus(mSpeedupStatu));
        }
    }

    public void addTask(long taskId, String gcid, String source, long taskSize) {
        if (!mTaskMap.containsKey(taskId)) {
            mTaskMap.put(taskId, new TaskInfo(taskId, gcid, source, taskSize));
        }
    }

    public void releaseTask(long taskId, String gcid, int statu) {
        TaskInfo taskInfo = mTaskMap.get(taskId);
        if (taskInfo == null) {
            return;
        }
        logD("releaseTask " + taskInfo);
        taskInfo.taskGcid = gcid;
        long taskSize = taskInfo.taskSize;
        logD("releaseTask mTaskMap size=" + mTaskMap.size());
        synchronized (mTaskMap) {
            reportSpeedup(mBandwidth, mTaskMap, taskSize, statu);
            mTaskMap.remove(taskId);
        }
        logD("releaseTask mTaskMap size=" + mTaskMap.size());
        if (mTaskMap.size() == 0) {
            mSpeedupCore.speedupStop();
            mKeepAliveTask.quit();
            speedSamplingStop();
            switchStatu(STATU_SPEEDUP_STOP);
        }
    }

    private boolean isSpeeduping() {
        return mSpeedupStatu > 0 && mSpeedupStatu < 200;
    }

    private boolean isSpeedup() {
        return mSpeedupStatu == STATU_SPEEDUP_KEEPALIVE;
    }

    private void switchStatu(int speedupStatu) {
        if (mSpeedupStatu != speedupStatu) {
            mSpeedupStatu = speedupStatu;
            logD("switchStatu=" + toStatus(mSpeedupStatu));
        }
    }

    private String toStatus(int speedStatu) {
        String statuStr;
        switch (speedStatu) {
            case STATU_SPEEDUP_TRY:
                statuStr = "STATU_SPEEDUP_TRY";
                break;
            case STATU_SPEEDUP_KEEPALIVE:
                statuStr = "STATU_SPEEDUP_KEEPALIVE";
                break;
            case STATU_SPEEDUP_REMOVE:
                statuStr = "STATU_SPEEDUP_REMOVE";
                break;
            case STATU_SPEEDUP_SPEED_ONLY:
                statuStr = "STATU_SPEEDUP_SPEED_ONLY";
                break;
            case STATU_SPEEDUP_FAIL:
                statuStr = "STATU_SPEEDUP_FAIL";
                break;
            case STATU_SPEEDUP_STOP:
                statuStr = "STATU_SPEEDUP_STOP";
                break;
            case STATU_SPEEDUP_DEF:
                statuStr = "STATU_SPEEDUP_DEF";
                break;
            default:
                statuStr = "unkown(" + speedStatu + ")";
                break;
        }
        return statuStr;
    }

    public boolean outOfSpeedupCount() {
        long useTime = SharePreferenceHelper.instance().getUseSpeedupTime();
        long duration = System.currentTimeMillis() - useTime;
        return duration < ONE_DAY;
    }

    public void usedSpeedup() {
        SharePreferenceHelper.instance().usedSpeedup();
    }

    private void reportSpeedup(Bandwidth bandwidth, HashMap<Long, TaskInfo> tasks, long taskSize, int status) {
        if (tasks == null) {
            return;
        }
        int taskCount = tasks.size();

        Map<String, String> data = new HashMap<String, String>();
        boolean speedup = isSpeedup() || (mAddSpeed != null && mAddSpeed.size() > 0);
        if (taskCount == 1) {
            Optional<TaskInfo> optValue = tasks.values().stream().findFirst();
            TaskInfo taskInfo = optValue.get();
            if (taskInfo != null) {
                data.put("task_gcid", taskInfo.taskGcid);
                data.put("task_source", taskInfo.taskSource);

            }
        }
        data.put("task_num", String.valueOf(taskCount));
        data.put("task_size", String.valueOf(taskSize));
        data.put("speedup_uid", mSpeedupCore.getUid());
        data.put("max_speed", String.valueOf(mMax_speed));
        data.put("min_speed", String.valueOf(mMin_speed));
        data.put("use_speedup", String.valueOf(speedup ? 1 : 0));
        data.put("completion_status", toDownloadStatus(status));
        data.put("errorcode", String.valueOf(status));
        if (speedup) {
            data.put("add_max_speed", String.valueOf(mAdd_max_speed));
            data.put("add_min_speed", String.valueOf(mAdd_min_speed));
            data.put("add_speeds", mAddSpeed != null ? mAddSpeed.toString() : "");
            if (bandwidth != null) {
                data.put("max_down_stream", String.valueOf(bandwidth.maxDownStream));
                data.put("max_up_stream", String.valueOf(bandwidth.maxUpStream));
                data.put("down_stream", String.valueOf(bandwidth.downStream));
                data.put("up_stream", String.valueOf(bandwidth.upStream));
            }
        }
        logD("reportSpeedup");
        if (data != null && data.size() > 0) {
            Set<String> keys = data.keySet();
            for (String key : keys) {
                logD("param " + key + "=" + data.get(key));
            }
        }
        logD("\n");
        Statistics.reportSpeedup(data);
    }

    private String toDownloadStatus(int status) {
        String statuStr = "others";
        switch (status) {
            case STATUS_SUCCESS:
                statuStr = "success";
                break;
            case STATUS_PAUSED_BY_APP:
                statuStr = "pause";
                break;
            case XL_STATUS__FAIL:
                statuStr = "fail";
                break;
        }
        return statuStr;
    }


    public class TaskInfo {
        long taskId;
        String taskGcid;
        String taskSource;
        long taskSize;

        public TaskInfo(long taskId, String gcid, String source, long taskSize) {
            this.taskId = taskId;
            this.taskGcid = gcid;
            this.taskSource = source;
            this.taskSize = taskSize;
        }

        @Override
        public String toString() {
            return "TaskInfo{" +
                    "taskId=" + taskId +
                    ", taskGcid='" + taskGcid + '\'' +
                    ", taskSource='" + taskSource + '\'' +
                    ", taskSize=" + taskSize +
                    '}';
        }
    }

    public void speedSampling() {
        if (!mSpeedTimer.isStoped()) {
            return;
        }
        count = 0;
        mSpeedTimer.schedule(new Runnable() {
            @Override
            public void run() {
                boolean speedup = false;
                long speed = 0;
                if (count == 0) {
                    reset();
                } else {
                    speed = getNetSpeed();
                    speedup = isSpeedup();
                    refreshSpeed(speedup, speed, count);
                }
                logD("getNetSpeed " + " count=" + count + " speedup=" + speedup + " speed=" + speed);
                count++;
            }
        }, 0, SPEED_SAMPLING_INTERVAL);
    }

    public void speedSamplingStop() {
        mSpeedTimer.quit();
    }

    private void refreshSpeed(boolean isSpeedup, long speed, int count) {
        if (isSpeedup) {
            if (count > 0 && isAddFirst) {
                mAdd_max_speed = mAdd_min_speed = speed;
                isAddFirst = false;
            }
            mAdd_max_speed = Math.max(speed, mAdd_max_speed);
            mAdd_min_speed = Math.min(speed, mAdd_min_speed);
            if (mAddSpeed.size() < 10) {
                mAddSpeed.add(speed);
            }
        } else {
            if (count > 0 && isFirst) {
                mMax_speed = mMin_speed = speed;
                isFirst = false;
            }
            mMax_speed = Math.max(speed, mMax_speed);
            mMin_speed = Math.min(speed, mMin_speed);
        }
    }

    public void reset() {
        lastTimeStamp = System.currentTimeMillis();
        lastRxBytes = getUidRxBytes();
    }

    private long getNetSpeed() {
        long uidRxBytes = getUidRxBytes();
        long nowTimeStamp = System.currentTimeMillis();
        long duration = nowTimeStamp - lastTimeStamp;
        long nowRxBytes = uidRxBytes;
        long speed = (uidRxBytes - lastRxBytes) * 1000 / duration;
        lastTimeStamp = nowTimeStamp;
        lastRxBytes = nowRxBytes;
        return speed;
    }

    private static long getUidRxBytes() {
        return TrafficStats.getTotalRxBytes();
    }

    private void logD(String msg) {
        if (LogUtil.debugSpeedup()) {
            LogUtil.log(TAG, msg);
        }
    }
}
