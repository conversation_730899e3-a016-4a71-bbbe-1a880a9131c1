
package com.android.providers.downloads.config;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;

import android.content.ContentResolver;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.net.Uri;
import android.os.Environment;
import android.provider.Settings;
import android.provider.Settings.SettingNotFoundException;
import android.text.TextUtils;
import android.util.Log;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.scdn.ScdnHelper;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.FileUtil;
import com.michael.corelib.config.CoreConfig;
import com.xunlei.downloadlib.XLDownloadManager;

public class XLConfig implements XLDownloadCfg {

    public static final String TAG = "DownloadManager";
    public static final Uri MobileLimitUri = Uri.parse("content://downloads/xl_download_bytes_limit_over_mobile");

    public static final String LOG_DIR = Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)
            ? Environment.getExternalStorageDirectory().getAbsolutePath() + "/.dlprovider"
            : null;
    private static boolean DEBUG = false;
    public static final String PRODUCT_NAME = "miui_download_manager";
    public static final String PRODUCT_ID = "10031";
    private static String app_version = null;
    public static final String APP_KEY = "xzNjAwMQ^^yb==0^852^083dbcff^cee25055f125a2fde";
    public static final String ACTION_NOTIFICATION_WITHOUT_ENGINE = "com.downloads.notification.action.noengine.init";
    private static String logDir = sdcardPath + "/.xlDownload/" + log_default_filename;
    public static String logSoDir = sdcardPath + "/.xlDownload/" + log_so_default_filename;
    public static String getVersionName(Context context) {
        if (context == null) {
            return null;
        }
        if (!TextUtils.isEmpty(app_version)) {
            return app_version;
        }
        try {
            PackageManager manager = context.getPackageManager();
            PackageInfo info = manager.getPackageInfo(context.getPackageName(), 0);
            app_version = info.versionName;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return app_version;
    }

    public static void LOGD(String message) {
        if (isDebug()) {
            LOGD(TAG, message);
        }
    }

    public static void LOGD(String tag, String message) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGD(tag, message);
            }
        }
    }

    public static void LOGD(String message, Throwable t) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGD(message, t);
            }
        }
    }

    public static void LOGI(String message) {
        if (!isDebug()) {
            return;
        }
        android.util.Log.i(TAG, message);
    }

    public static void LOGE(String message, Throwable exc) {
        android.util.Log.e(TAG, message, exc);
    }

    public static void LOGW(String message) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGW("", message);
            }
        }
    }

    public static void LOGW(String tag, String message) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGW(tag, message);
            }
        }
    }

    public static void LOGW(String message, Throwable t) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGW(message, t);
            }
        }
    }

    public static void LOGW(String tag, String message, Throwable t) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGW(tag, message, t);
            }
        }
    }

    public static void LOGD_INFO(String message) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGD(message);
            }
        } else {
            Log.d(TAG, message);
        }
    }

    /**
     * 总是有debug log
     *
     * @param tag
     * @param message
     */
    public static void LOGD_INFO(String tag, String message) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGD(tag, message);
            }
        } else {
            Log.d(tag, message);
        }
    }

    public static void LOGD_INFO(String tag, String message, Throwable t) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGD(tag, message, t);
            }
        } else {
            Log.d(tag, message, t);
        }
    }

    public static void LOGD_INFO(String message, Throwable t) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGD(message, t);
            }
        } else {
            Log.d(TAG, message, t);
        }
    }

    public static void LOGD(String tag, String message, Throwable t) {
        if (isDebug()) {
            if (!TextUtils.isEmpty(message)) {
                CoreConfig.LOGD(tag, message, t);
            }
        }
    }

    private static final String DEBUG_DATE_FORMAT = "MM-dd HH:mm:ss:SSS";

    public static String debugFormatTime(long time) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(DEBUG_DATE_FORMAT);
        return dateFormat.format(time);
    }

    public static boolean isDebug() {
        return DEBUG;
    }

    public static String getLogPath() {
        return logDir;
    }

    public static String getLogSoPath() {
        return logSoDir;
    }

    public static void initLog(Context context) {
        try {
            String path = DownloadSettings.XLSecureConfigSettings.getDpDebugLogPath(logDir);//Settings.System.getString(resolver, DEBUG_DP_PATH);
            setDebug(context, DownloadSettings.XLShareConfigSettings.getDebugLogSwitch(false), path);
        } catch (Exception e) {
            Log.w(TAG, "initLog fail ", e);
        }
    }

    public static void setDebug(Context context, boolean isDebug, String path) {
        try {
            String soLogPath = null;
            if (context == null) {
                throw new IllegalAccessException("context is null");
            }
            String logPath = null;
            String logFileName = null;
            if (TextUtils.isEmpty(path)) {
                path = context.getCacheDir().getAbsolutePath() +"/"+ log_default_filename;
            }
            File file = new File(path);
            logPath = file.getParent();
            logFileName = file.getName();
            File tempFile = generationSoLogFile(path);
            File soLogFile = FileUtil.createFile(tempFile.getPath());
            if (soLogFile == null) {
                throw new Exception("so log file is null");
            }
            soLogPath = soLogFile.getPath();
            CoreConfig.init(context, isDebug,
                    logPath, logFileName);
            DownloadSettings.XLSecureConfigSettings.saveDpDebugLogPath(path);
            DownloadSettings.XLShareConfigSettings.setDebugLogSwitch(isDebug);
            DEBUG = isDebug;
            Log.d(TAG,
                    String.format("setDebug isDebug=%b path=%s logPath=%s", DEBUG, path,
                            CoreConfig.getLogPath()));
            logDir = CoreConfig.getLogPath();
            logSoDir = soLogPath;
            ScdnHelper.getInstance().setLogEnable(isDebug);
        } catch (Exception e) {
            Log.w(TAG, "saveLogMark fail ", e);
            DEBUG = false;
        }
    }

    private static File generationSoLogFile(String path) throws IOException {
        if (TextUtils.isEmpty(path)) {
            throw new IOException("path is null");
        }
        File file = new File(path);
        String name = file.getName();
        if (TextUtils.isEmpty(name)) {
            throw new IOException("get name fail path=" + file.getPath());
        }
        int dot = name.lastIndexOf('.');
        String preName = dot > 0 ? name.substring(0, dot) : name;
        String suffixName = dot > 0 ? name.substring(dot) : "";
        String soPath = file.getParent() + "/" + preName + "_so" + suffixName;
        return new File(soPath);
    }

    private static String getErrorDetail(int errorCode) {
        XLDownloadManager instance = XLDownloadManager.getInstance();
        if (instance == null) {
            return String.valueOf(errorCode);
        }
        return String.format("%s(%d)", instance.getErrorCodeMsg(errorCode), errorCode);
    }

    public static void saveLogMark(Context context) {
        setDebug(context, isDebug(), logDir);
    }

    public static void setSoDebug(boolean isDebug, String path) throws Exception {
        XLDownloadManager instance = XLDownloadManager.getInstance();
        if (instance == null) {
            throw new IllegalAccessException("mXlDownloadManager is null");
        }
        int ret = instance.setReleaseLog(isDebug, isDebug ? path : "");
        LOGD_INFO(String.format("setReleaseLog trunOn=%b ret=%s", isDebug, getErrorDetail(ret)));
    }

    public static boolean needShowInstallGuide() {
        Context context = DownloadApplication.getGlobalApplication();
        if (context == null) {
            return false;
        }
        if (BuildUtils.isInternationalVersion() || BuildUtils.isCTSVersion() || BuildUtils.isTablet()) {
            return false;
        }
        if (BuildUtils.isAlphaVersion() || BuildUtils.isDevVersion()) {
            return false;
        }
        if (BuildUtils.isStableVersion()) {//此版本之后，安装引导通知由桌面负责
            return false;
        }
        return false;
    }

}
