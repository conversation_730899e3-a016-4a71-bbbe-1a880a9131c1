
package com.android.providers.downloads.config;

import android.app.ActivityManager;
import android.app.ActivityManager.RunningAppProcessInfo;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Process;
import android.provider.Settings.Secure;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;

import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.Util;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

public class ConstInfo {
    private static final String LOG_TAG = "ConstInfo";

    private static final HashMap<ConstKey, String> sValueMap = new HashMap<ConstKey, String>();

    public enum ConstKey {
        SDK_VERSION,
        APP_VERSION, APP_VERSION_CODE, APP_NAME, APP_PACKAGE, CHANNEL, PROCESS_NAME,
        SCREEN_DENSITY, SCREEN_SIZE, SCREEN_WIDTH, SCREEN_HEIGHT,
        DEVICE_MODEL, DEVICE_ID, OPERATORS, OPERATORS_NAME, PROCESS_SHORT_NAME,
        PHONE_NUMBER, PHONE_IMEI, PHONE_IMSI, PHONE_MAC, PHONE_IP, PHONE_IP_V4, PHONE_LANGUAGE, PHONE_COUNTRY
    }

    public static synchronized String getValue(Context context, ConstKey key) {
        if (key == null) {
            return null;
        }

        String result = sValueMap.get(key);
        if (!TextUtils.isEmpty(result)) {
            return result;
        }

        result = loadValue(context, key);
        sValueMap.put(key, result);
        return result;
    }

    private static String loadValue(Context context, ConstKey key) {
        String result = null;
        switch (key) {
            case SDK_VERSION:
                result = getSdkVersion(context);
                break;
            case APP_VERSION:
                result = getAppVersion(context);
                break;
            case APP_VERSION_CODE:
                result = getAppVersionCode(context);
                break;
            case APP_NAME:
                result = getAppName(context);
                break;
            case APP_PACKAGE:
                result = getAppPackage(context);
                break;
            case SCREEN_SIZE:
                result = getScreenSize(context);
                break;
            case SCREEN_DENSITY:
                result = getScreenDensity(context);
                break;
            case DEVICE_MODEL:
                result = getDeviceModel(context);
                break;
            case DEVICE_ID:
                result = getDeviceId(context);
                break;
            case OPERATORS:
                result = getOperators(context);
                break;
            case OPERATORS_NAME:
                result = getOperatorsName(context);
                break;
            case CHANNEL:
                result = getChannel(context);
                break;
            case PROCESS_NAME:
                result = getProcessName(context);
                break;
            case PROCESS_SHORT_NAME:
                result = getShortProcessName(context);
                break;
            case PHONE_NUMBER:
                result = getPhoneNumber(context);
                break;
            case PHONE_IMEI:
                result = Util.getImei(context);
                break;
            case PHONE_IMSI:
                result = getPhoneIMSI(context);
                break;
            case PHONE_MAC:
                result = getPhoneMac(context);
                break;
            case PHONE_IP:
                result = getPhoneIP(context);
                break;
            case PHONE_IP_V4:
                result = getPhoneIP(context);
                break;
            case PHONE_LANGUAGE:
                result = getPhoneLaunguage();
                break;
            case PHONE_COUNTRY:
                result = getPhoneCountry();
                break;
            case SCREEN_WIDTH:
                result = getScreenWidht(context);
                break;
            case SCREEN_HEIGHT:
                result = getScreenHeight(context);
                break;
            default:
                result = null;
        }

        return result;
    }

    private static String getScreenHeight(Context context) {
        return String.valueOf(context.getResources().getDisplayMetrics().heightPixels);
    }

    private static String getScreenWidht(Context context) {
        return String.valueOf(context.getResources().getDisplayMetrics().widthPixels);
    }

    private static String getPhoneLaunguage() {
        return Locale.getDefault().getLanguage();
    }

    private static String getPhoneCountry() {
        return Locale.getDefault().getCountry();
    }

    private static String getSdkVersion(Context context) {
        return Build.VERSION.RELEASE;
    }

    private static String getAppVersion(Context context) {
        loadPackageInfo(context);
        return sValueMap.get(ConstKey.APP_VERSION);
    }

    private static String getAppVersionCode(Context context) {
        loadPackageInfo(context);
        return sValueMap.get(ConstKey.APP_VERSION_CODE);
    }

    private static String getAppName(Context context) {
        loadAppInfo(context);
        return sValueMap.get(ConstKey.APP_NAME);
    }

    private static String getPhoneMac(Context context) {
        WifiManager wifi = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        WifiInfo info = wifi.getConnectionInfo();
        return info.getMacAddress();
    }

    private static String getPhoneIP(Context context) {
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface
                    .getNetworkInterfaces(); en.hasMoreElements();) {
                NetworkInterface intf = en.nextElement();
                for (Enumeration<InetAddress> enumIpAddr = intf
                        .getInetAddresses(); enumIpAddr.hasMoreElements();) {
                    InetAddress inetAddress = enumIpAddr.nextElement();
                    if (!inetAddress.isLoopbackAddress()) {
                        return inetAddress.getHostAddress().toString();
                    }
                }
            }
        } catch (SocketException ex) {
            Log.e("WifiPreference IpAddress", ex.toString());
        }
        return null;
    }

    //获取ipV4的ip地址
    private static String getPhoneIPV4(Context context) {
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface
                    .getNetworkInterfaces(); en.hasMoreElements(); ) {
                NetworkInterface intf = en.nextElement();
                for (Enumeration<InetAddress> enumIpAddr = intf
                        .getInetAddresses(); enumIpAddr.hasMoreElements(); ) {
                    InetAddress inetAddress = enumIpAddr.nextElement();
                    if (!inetAddress.isLoopbackAddress() && !inetAddress.isLinkLocalAddress()) {
                        return inetAddress.getHostAddress().toString();
                    }
                }
            }
        } catch (Exception ex) {
            Log.e("WifiPreference IpAddress", ex.toString());
        }
        return null;
    }

    private static String getAppPackage(Context context) {
        return context.getPackageName();
    }

    private static void loadPackageInfo(Context context) {
        String packageName = context.getPackageName();
        sValueMap.put(ConstKey.APP_PACKAGE, packageName);

        PackageManager pm = context.getPackageManager();
        try {
            PackageInfo info = pm.getPackageInfo(packageName, 0);
            String version = info.versionName;
            if (version == null) {
                version = "code(" + info.versionCode + ")";
            }
            sValueMap.put(ConstKey.APP_VERSION, version);
            sValueMap.put(ConstKey.APP_VERSION_CODE,
                    String.valueOf(info.versionCode));
        } catch (NameNotFoundException e) {
            Log.w(LOG_TAG, "Exception when retrieving package:" + packageName);
        }
    }

    private static String getScreenSize(Context context) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        return String.format("%d*%d", metrics.widthPixels, metrics.heightPixels);
    }

    private static String getScreenDensity(Context context) {
        return String.valueOf(context.getResources().getDisplayMetrics().density);
    }

    private static String getDeviceModel(Context context) {
        return Build.MODEL;
    }

    private static String getDeviceId(Context context) {
        TelephonyManager tm = (TelephonyManager) context
                .getSystemService(Context.TELEPHONY_SERVICE);
        String tmDevice = null;
        String tmSerial = null;
        String androidId = null;
        if(!Helpers.isInCTSMode()){
            try {
                tmDevice = Util.getImei(context);
            } catch (Exception e) {
                Log.w(LOG_TAG, e);
            }
            try {
                tmSerial = tm.getSimSerialNumber();
            } catch (Exception e) {
                Log.w(LOG_TAG, e);
            }
        }
        try {
            androidId = Secure.getString(context.getContentResolver(),
                    Secure.ANDROID_ID);
        } catch (Exception e) {
            Log.w(LOG_TAG, e);
        }
        if (tmDevice == null) {
            tmDevice = "";
        }
        if (tmSerial == null) {
            tmSerial = "";
        }
        if (androidId == null) {
            androidId = "";
        }
        UUID deviceUuid = new UUID(androidId.hashCode(),
                ((long) tmDevice.hashCode() << 32) | tmSerial.hashCode());
        return "lsa-kp" + deviceUuid.toString();
    }

    private static String getOperators(Context context) {
        TelephonyManager tel = (TelephonyManager) context
                .getSystemService(Context.TELEPHONY_SERVICE);
        return tel == null ? "" : tel.getSimOperator();
    }

    private static String getOperatorsName(Context context) {
        TelephonyManager tel = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        return tel == null ? "" : tel.getSimOperatorName();
    }

    private static String getChannel(Context context) {
        loadAppInfo(context);
        return sValueMap.get(ConstKey.CHANNEL);
    }

    private static void loadAppInfo(Context context) {
        try {
            PackageManager pmg = context.getPackageManager();
            ApplicationInfo info = pmg.getApplicationInfo(
                    context.getPackageName(), PackageManager.GET_META_DATA);

            CharSequence label = info.loadLabel(pmg);

            sValueMap.put(ConstKey.APP_NAME, label == null ? info.packageName
                    : label.toString());

            Bundle bundle = info.metaData;
            String value = bundle == null ? null : bundle
                    .getString("UMENG_CHANNEL");
            value = value == null ? (bundle == null ? null : bundle
                    .getString("CHANNEL")) : value;
            sValueMap.put(ConstKey.CHANNEL, value == null ? "" : value);
        } catch (Exception e) {
            Log.w(LOG_TAG, e);
        }
    }

    private static String getProcessName(Context context) {
        ActivityManager activityManager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        List<RunningAppProcessInfo> infos = activityManager
                .getRunningAppProcesses();
        int pid = Process.myPid();
        RunningAppProcessInfo myInfo = null;
        for (RunningAppProcessInfo info : infos) {
            if (info.pid == pid) {
                myInfo = info;
                break;
            }
        }

        return myInfo == null ? null : myInfo.processName;
    }

    private static String getShortProcessName(Context context) {
        String name = getProcessName(context);
        String packageName = context.getPackageName();
        if (name == null) {
            return null;
        }

        String result = name;
        if (TextUtils.equals(name, packageName)) {
            result = "";
        } else if (name.startsWith(packageName)) {
            result = name.substring(packageName.length());
        }

        if (result.startsWith(":")) {
            result = result.substring(1);
        }

        return result;
    }

    private static String getPhoneNumber(Context context) {
        if(Helpers.isInCTSMode()){
            return "";
        }
        String result = null;
        try {
            TelephonyManager tm = (TelephonyManager) context
                    .getSystemService(Context.TELEPHONY_SERVICE);
            result = tm.getLine1Number();
        } catch (Exception e) {
            Log.w(LOG_TAG, e);
        }
        return result == null ? "" : result;
    }


    private static String getPhoneIMSI(Context context) {
        if(Helpers.isInCTSMode()){
            return "";
        }
        String result = null;
        try {
            TelephonyManager tm = (TelephonyManager) context
                    .getSystemService(Context.TELEPHONY_SERVICE);
            result = tm.getSubscriberId();
        } catch (Exception e) {
            Log.w(LOG_TAG, e);
        }

        return result == null ? "" : result;
    }
}
