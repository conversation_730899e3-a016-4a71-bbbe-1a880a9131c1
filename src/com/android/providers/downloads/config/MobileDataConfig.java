package com.android.providers.downloads.config;

import android.app.DownloadManager;
import android.content.Context;
import android.provider.Settings;
import android.util.Log;

import com.android.providers.downloads.util.Helpers;

/**
 * Created by lxy on 16/1/7.
 * 移动数据限制配置
 */
public class MobileDataConfig {
    public static final String TAG = "MobileDataConfig";
    public static final long MAX_BYTES_OVER_MOBILE = Long.MAX_VALUE;//移动网络下最大值
    public static final String DOWNLOAD_RECOMMENDED_KEY = Settings.Global.DOWNLOAD_RECOMMENDED_MAX_BYTES_OVER_MOBILE;

    private static long mLimit = -1;


    /**
     * 移动网络设置是否为无限制
     *
     * @return
     */
    public static boolean isUnlimited(Context context) {
        return getLimit(context) >= MAX_BYTES_OVER_MOBILE;
    }

    public static boolean isUnlimited(long limitValue) {
        return limitValue == MAX_BYTES_OVER_MOBILE;
    }

    /**
     * 由于分身模式下DownloadManager.getRecommendedMaxBytesOverMobile
     * 与主模式使用同一份数据，没有分开，因此引入
     * DownloadSettings.XLSecureConfigSettings.getRecommendedMaxBytesOverMobile
     * @param context
     * @return
     */
    public static long getLimit(Context context) {
        if (mLimit != -1) {
            return mLimit;
        }
        boolean intoUISettingPage = DownloadSettings.XLShareConfigSettings.isIntoUISettingPage(false);
        //移动网络下,国际版要为无限制,非国际版为1M)
        long recommendedMaxBytesOverMobile = DownloadSettings.XLSecureConfigSettings.getRecommendedMaxBytesOverMobile();
        Long limit = DownloadManager.getRecommendedMaxBytesOverMobile(context);
        mLimit = limit == null ? -1 : limit.longValue();
        if (recommendedMaxBytesOverMobile != mLimit && intoUISettingPage && recommendedMaxBytesOverMobile != -1) {
            setLimitForGlobal(context,recommendedMaxBytesOverMobile);
        }
        return mLimit;
    }


    public static boolean setLimit(Context context, long limit) {
        DownloadSettings.XLSecureConfigSettings.setRecommendedMaxBytesOverMobile(limit);
        return setLimitForGlobal(context,limit);
    }

    private static boolean setLimitForGlobal(Context context, long limit) {
        mLimit = limit;
        try {
            return DownloadManager.setRecommendedMaxBytesOverMobile(context, limit);
        }catch (NoSuchMethodError e){
            Log.w(TAG, "NoSuchMethodError setRecommendedMaxBytesOverMobile");
        }
        return false;
    }


}
