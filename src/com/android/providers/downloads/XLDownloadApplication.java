package com.android.providers.downloads;

import android.content.Context;
import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.scdn.ScdnHelper;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.DownloadServiceUtils;
import com.android.providers.downloads.util.FileUtil;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.XLUtil;
import com.android.providers.downloads.utils.LogUtil;
import com.android.providers.downloads.xunlei.speedup.XLSpeedUpManager;
import com.xunlei.downloadlib.XLDownloadManager;
import com.xunlei.downloadlib.parameter.GetDownloadLibVersion;
import com.xunlei.downloadlib.parameter.InitParam;
import com.xunlei.vipchannel.XLVipChannelManager;
import com.xunlei.vipchannel.parameter.VipInitParam;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * Created by admin on 15/11/25.
 */
public class XLDownloadApplication {

    private static final String TAG = XLConfig.TAG;
    private XLDownloadManager mXlDownloadManager;
    private XLVipChannelManager mXLVipChannelManager;
    private Context mContext;
    private static boolean mXLEngineInit = false;

    public XLDownloadApplication(Context context) {
        mContext = context;
    }

    /**
     * init Xunlei service
     */
    public void initXunleiEngine() {
        if (BuildUtils.isTablet()|| BuildUtils.isInternationalVersion()) {
            return;
        }
        Context ctx = mContext;
        if (mXlDownloadManager != null) {
            return;
        }
        LogUtil.d(TAG, "initXunleiEngine");
        initSettingFile();
        mXlDownloadManager = XLDownloadManager.getInstance();
        InitParam para = new InitParam();
        para.mAppKey = XLConfig.APP_KEY;
        para.mAppVersion = XLConfig.getVersionName(ctx);
        String cfgPath = generateCacheFileDir();
        para.mStatCfgSavePath = cfgPath;
        para.mStatSavePath = cfgPath;
        para.mlogSavePath = cfgPath;
        para.mPermissionLevel = 1;
        //下载库控制云控，是否初始化拉取
        boolean usedDownload = DownloadSettings.XLSecureConfigSettings.getUsedFirstDownload();
        para.mQueryConfOnInit = usedDownload ? 1 : 0;
        mXlDownloadManager.init(ctx, para, true);
        mXlDownloadManager.setStatReportSwitch(LogUtil.isForceReport());
        GetDownloadLibVersion libVersion = new GetDownloadLibVersion();
        mXlDownloadManager.getDownloadLibVersion(libVersion);
        String miuiVersion = !TextUtils.isEmpty(Build.VERSION.INCREMENTAL) ? Build.VERSION.INCREMENTAL
                : "";
        mXlDownloadManager.setOSVersion(miuiVersion+"_"+BuildUtils.getBigMiuiVersion());
        StringBuffer tmpStr = new StringBuffer();
        tmpStr.append(String.format("osVersion=%s\n",(miuiVersion+"_"+BuildUtils.getBigMiuiVersion())));
        tmpStr.append(String.format("appVersion=%s\n", String.valueOf(XLUtil.getAppVersion(ctx))));
        tmpStr.append(String.format("libVersion=%s\n", String.valueOf(libVersion.mVersion)));
        tmpStr.append(String.format("miuiVersion=%s\n", miuiVersion));
        tmpStr.append(String.format("usedFirstDownload=%d",para.mQueryConfOnInit));
        LogUtil.forceD(TAG, "initXunleiEngine \n" + tmpStr.toString());

        String token = DownloadSettings.XLShareConfigSettings.getXLVipToken("");
        mXLVipChannelManager = XLVipChannelManager.getInstance();
        VipInitParam vipPara = new VipInitParam(XLConfig.getVersionName(ctx), XLConfig.PRODUCT_ID,
                token);
        mXLVipChannelManager.vipChannelInit(ctx, vipPara, Helpers.isInCTSMode() ? false : true);
        XLSpeedUpManager.initSpeedUpManager(mXlDownloadManager, mXLVipChannelManager);
        if (XLConfig.isDebug() && !mXlDownloadManager.isLogTurnOn()) {
            File file = null;
            try {
                FileUtil.createFile(XLConfig.logSoDir);
            } catch (Exception e) {
                XLConfig.LOGD("create file fail", e);
                file = null;
            }
            if (file != null && file.exists()) {
                mXlDownloadManager.setReleaseLog(true, file.getPath());
            }
        }

        ScdnHelper scdnHelper = new ScdnHelper();
        scdnHelper.init(ctx);
        String version = scdnHelper.getVersion();
        if (usedDownload) {//如果下载过，则可以做网络请求
            ScdnHelper.getInstance().setNetworkEnable();
        }
        XLConfig.LOGD(TAG, String.format("scdn libVersion=%s"
                ,version));
        mXLEngineInit = true;
        DownloadServiceUtils.notifyEngineInitComplete(mContext);
        mXlDownloadManager.setStatForceReportSwitch(LogUtil.isForceReport());
    }

    /**
     * 初始化setting.cfg，从so库版本：4.0413.230.29开始
     */
    private void initSettingFile() {
        String cfgPath = generateCacheFileDir();
        String path = cfgPath != null ? cfgPath + File.separator + "setting.cfg" : null;
        if (path == null) {
            return;
        }
        File file = new File(path);
        if (!file.exists()) {
            try {
                File setFile = FileUtil.createFile(path);
                InputStream inputStream = mContext.getAssets().open("setting.cfg");
                FileUtil.writeFile(setFile, inputStream);
            } catch (IOException e) {
                e.printStackTrace();
                LogUtil.d(TAG, e);
            }
        }
    }

    public boolean isInit() {
        return mXLEngineInit;
    }


    /**
     * uninit xunlei service how to call this function - if some tasks
     * running(xunlei), create a new task(android)
     */
    public void uninitXunleiEngine() {
        XLConfig.LOGD(TAG, "(uninitXunleiEngine) ---> uninit xunlei engine service.");
        if (null != mXlDownloadManager) {
            mXlDownloadManager.uninit();
            mXlDownloadManager = null;
        }

        if (null != mXLVipChannelManager) {
            mXLVipChannelManager.vipChannelUninit();
            mXLVipChannelManager = null;
        }
        ScdnHelper.getInstance().releaseScdn();

    }

    private String generateCacheFileDir() {
        File cfgFile = null;
        try {
            cfgFile = new File(mContext.getFilesDir().getPath() + File.separator + "so_cfg");
            if (!cfgFile.exists()) {
                boolean createOk = cfgFile.mkdirs();
                if (!createOk) {
                    LogUtil.forceD(TAG, "create " + cfgFile.getAbsolutePath() + " failed");
                }
            }
        } catch (Exception e) {
            LogUtil.forceD(TAG, "create dir failed", e);
        }
        return cfgFile != null ? cfgFile.getAbsolutePath() : null;
    }

    public XLDownloadManager getManager() {
        return mXLEngineInit ? mXlDownloadManager : null;
    }

    public XLVipChannelManager getVipManager() {
        return mXLEngineInit ? mXLVipChannelManager : null;
    }
}
