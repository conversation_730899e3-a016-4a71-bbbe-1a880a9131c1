/*
 * Copyright (C) 2008 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.providers.downloads.fileoptimization;

import android.app.DownloadManager.ExtraDownloads;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.ContentObserver;
import android.database.Cursor;
import android.drm.DrmManagerClient;
import android.drm.DrmOutputStream;
import android.net.INetworkPolicyListener;
import android.net.NetworkInfo;
import android.net.NetworkPolicyManager;
import android.net.TrafficStats;
import android.net.Uri;
import android.net.wifi.WifiManager;
import android.os.FileUtils;
import android.os.Handler;
import android.os.ParcelFileDescriptor;
import android.os.PowerManager;
import android.os.Process;
import android.os.SystemClock;
import android.os.WorkSource;
import android.provider.Downloads;
import android.security.NetworkSecurityPolicy;
import android.security.net.config.ApplicationConfig;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.webkit.MimeTypeMap;

import com.android.providers.downloads.DownloadApplication;
import com.android.providers.downloads.DownloadThread;
import com.android.providers.downloads.StorageManager;
import com.android.providers.downloads.SystemFacade;
import com.android.providers.downloads.compat.DownloadThreadCompat;
import com.android.providers.downloads.compat.SimpleNetworkPolicyManager;
import com.android.providers.downloads.config.Constants;
import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.config.XLDownloadCfg;
import com.android.providers.downloads.exception.StopRequestException;
import com.android.providers.downloads.helper.CloudControlHelper;
import com.android.providers.downloads.model.State;
import com.android.providers.downloads.provider.DownloadProvider;
import com.android.providers.downloads.service.DownloadInfo;
import com.android.providers.downloads.service.DownloadInfo.NetworkState;
import com.android.providers.downloads.service.DownloadNotifier;
import com.android.providers.downloads.setting.CloudConfigPreference;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.util.BuildUtils;
import com.android.providers.downloads.util.DownloadDrmHelper;
import com.android.providers.downloads.util.DownloadExtra;
import com.android.providers.downloads.util.DownloadExtra2;
import com.android.providers.downloads.util.Helpers;
import com.android.providers.downloads.util.NetworkChangeManager;
import com.android.providers.downloads.util.ProcessUtil;
import com.android.providers.downloads.util.XLDownloadHelper;
import com.android.providers.downloads.xunlei.XLDownloadThread;
import com.xunlei.downloadlib.XLDownloadManager;

import java.io.File;
import java.io.FileDescriptor;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.SyncFailedException;
import java.lang.reflect.Method;
import java.lang.reflect.InvocationTargetException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.security.GeneralSecurityException;
import java.util.List;
import java.util.Map;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLPeerUnverifiedException;
import javax.net.ssl.TrustManager;

import com.libcore.io.IoUtils;

import miui.provider.ExtraNetwork;

import android.net.ConnectivityManager;
import android.system.Os;
import android.system.OsConstants;
import android.system.ErrnoException;
import static android.text.format.DateUtils.SECOND_IN_MILLIS;
import static java.net.HttpURLConnection.HTTP_INTERNAL_ERROR;
import static java.net.HttpURLConnection.HTTP_MOVED_PERM;
import static java.net.HttpURLConnection.HTTP_MOVED_TEMP;
import static java.net.HttpURLConnection.HTTP_OK;
import static java.net.HttpURLConnection.HTTP_PARTIAL;
import static java.net.HttpURLConnection.HTTP_SEE_OTHER;
import static java.net.HttpURLConnection.HTTP_UNAVAILABLE;

/**
 * Task which executes a given {@link DownloadInfo}: making network requests,
 * persisting data to disk, and updating {@link DownloadProvider}.
 */
public class OptDownloadThread extends DownloadThread implements XLDownloadCfg {

    // TODO: bind each download to a specific network interface to avoid state
    // checking races once we have ConnectivityManager API

    protected static final int HTTP_REQUESTED_RANGE_NOT_SATISFIABLE = 416;
    protected static final int HTTP_TEMP_REDIRECT = 307;

    private static final int DEFAULT_TIMEOUT = (int) (20 * SECOND_IN_MILLIS);
    private static /*HttpHandler*/ Object mMiHttpHandler = null;
    private static /*HttpsHandler*/ Object mMiHttpsHandler = null;

    private final Context mContext;
    protected final DownloadInfo mInfo;
    protected final SystemFacade mSystemFacade;
    protected final StorageManager mStorageManager;
    private final DownloadNotifier mNotifier;

    private volatile boolean mPolicyDirty;
    private boolean mMobileLimiteChange = false;
    private boolean mFirewallRuleChange = false;
    private boolean mNetChange = false;
    private MobileLimiteListener mMobileLimiteListener = new MobileLimiteListener();
    private FirewallObserver mFirewallObserver = new FirewallObserver();

    protected boolean mIfMobileFileSizeChecked = false;
    private boolean mHasUpdateFilesizeToDB = false;

    protected int downloadSequenceId;

    private static final String TAG = "DownloadThread";

    protected long mStartSize = 0;
    protected long mStartTime = 0;
    private File mDownloadingFile;
    protected File mDownloadFile;
    private File mDownloadDestFile;//用于记录下载过程中的文件
    protected long mPreDownloadSpeed;// 记录上一次速度
    private long mTotalBytes = 0;
    private boolean isRunning = false;

    protected int wakelockTime = 0;

    public OptDownloadThread(Context context, SystemFacade systemFacade, DownloadInfo info,
                          StorageManager storageManager, DownloadNotifier notifier) {
        super(context, systemFacade, info,storageManager, notifier);
        mContext = context;
        mSystemFacade = systemFacade;
        mInfo = info;
        mStorageManager = storageManager;
        mNotifier = notifier;
        if (DownloadApplication.miProvider != null) {
            DownloadApplication.miProvider.init();
        }
    }

    public DownloadInfo getInfo(){
        return mInfo;
    }

    /**
     * Returns the user agent provided by the initiating app, or use the default
     * one
     */
    protected String userAgent() {
        String userAgent = mInfo.mUserAgent;
        if (userAgent == null) {
            userAgent = Constants.DEFAULT_USER_AGENT;
        }
        return userAgent;
    }

    public boolean isRunning() {
        return isRunning;
    }

    @Override
    public void run() {
        isRunning = true;
        Process.setThreadPriority(Process.THREAD_PRIORITY_BACKGROUND);
        try {
            runInternal();
        } catch (Exception e) {
            XLConfig.LOGW("runInternal exception ", e);
        } finally {
            mNotifier.notifyDownloadSpeed(mInfo.mId, 0);
            isRunning = false;
        }
    }

    protected String getDownloadInfo() {
        return "mId=" + mInfo.mId;
    }

    boolean isBtTask(String uri) {
        return !TextUtils.isEmpty(uri) ? uri.startsWith("bt:") : false;
    }

    boolean isHttpOrHttps(String uri) {
        return !TextUtils.isEmpty(uri) ? uri.startsWith("http") : false;
    }

    private void runInternal() {
        // Skip when download already marked as finished; this download was
        // probably started again while racing with UpdateThread.
        int status = Downloads.Impl.STATUS_PENDING;
        String extra = null;
        String extra2 = null;
        Cursor cursor = null;
        try {
            cursor = DownloadInfo.queryDownloadInfo(mContext.getContentResolver(), mInfo.mId);
            if (cursor != null && cursor.moveToFirst()) {
                status = cursor.getInt(0);
                extra = cursor.getString(cursor.getColumnIndex(COLUMN_EXTRA));
                extra2 = cursor.getString(cursor.getColumnIndex(COLUMN_EXTRA2));
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        if (status != STATUS_RUNNING) {
            String statuStr = Downloads.Impl.statusToString(status);
            XLConfig.LOGD(TAG, " in runInternal, Download " + getDownloadInfo() + " statu=" + statuStr
                    + " not running");
            return;
        }

        final Uri uri = ContentUris.withAppendedId(ALL_DOWNLOADS_CONTENT_URI, mInfo.mId);

        if (DownloadExtra.notifyIsClear(extra)) {
            mContext.getContentResolver().update(uri, DownloadExtra.setNotifyClear(extra, false), null, null);
        }
        CloudControlHelper.getInstance().getCloudConfig();

        State state = new State(mInfo);
        wakelockTime = DownloadExtra2.getWakelockTime(extra2);
        trackTaskStart(state);
        PowerManager.WakeLock wakeLock = null;
        WifiManager.WifiLock wifiLock = null;
        int finalStatus = Downloads.Impl.STATUS_UNKNOWN_ERROR;
        int soRet = 0;
        int numFailed = mInfo.mNumFailed;
        String errorMsg = null;
        final NetworkPolicyManager netPolicy = NetworkPolicyManager.from(mContext);
        final PowerManager pm = (PowerManager) mContext.getSystemService(Context.POWER_SERVICE);
        final WifiManager wm = (WifiManager) mContext.getSystemService(Context.WIFI_SERVICE);
        registerMobileLimitChange(mContext);
        registerNetChange();
        ExtraNetwork.registerFirewallContentObserver(mContext, mFirewallObserver);

        try {
            wakeLock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, Constants.TAG);
            wakeLock.setWorkSource(new WorkSource(mInfo.mUid));
            if (!CloudConfigPreference.getInstance().isWakelockExposed()) {
                wakelockTime = 0;
            }
            if (wakelockTime <= 0) {
                wakeLock.acquire();
                XLConfig.LOGD_INFO(TAG, "mId=" + mInfo.mId + " wakelock acquire for long");
            } else {
                wakeLock.acquire(wakelockTime * 1000L);
                XLConfig.LOGD_INFO(TAG, "mId=" + mInfo.mId + " wakelock acquire for " + wakelockTime +" s");
            }
            wifiLock = wm.createWifiLock(WifiManager.WIFI_MODE_FULL_HIGH_PERF, Constants.TAG);
            wifiLock.acquire();

            // while performing download, register for rules updates
            netPolicy.registerListener(mPolicyListener);

            XLConfig.LOGD_INFO(TAG, "in runInternal, Download "
                    + getDownloadInfo() + " uri=" + state.mRequestUri);

            // Remember which network this download started on; used to
            // determine if errors were due to network changes.
            final NetworkInfo info = mSystemFacade.getActiveNetworkInfo(mInfo.mUid);
            if (info != null) {
                state.mNetworkType = info.getType();
            }
            XLConfig.LOGD_INFO(TAG, String.format("in runInternal %s", NetworkInfo2Str(info)));

            // Network traffic on this thread should be counted against the
            // requesting UID, and is tagged with well-known value.
            DownloadThreadCompat.setThreadStatsTag();
            TrafficStats.setThreadStatsUid(mInfo.mUid);
            setThreadStatsPid(mContext, mInfo.mUid, mInfo.mPackage);

            DownloadType downloadType = XLDownloadHelper.getDownloadType(state.mRequestUri);
            if (downloadType == DownloadType.UNKNOW) {
                String requestUri = state.mRequestUri != null ? state.mRequestUri : "";
                throw new StopRequestException(STATUS_BAD_REQUEST, "bad_request exception uri="
                        + requestUri);
            }
            state.mUrl = isHttpOrHttps(state.mRequestUri) ? new URL(state.mRequestUri) : null;

            downloadSequenceId = Statistics.generateUniqueId(mContext,state);

            checkStatePausedOrCanceled(state);
            checkConnectivity(true);
            preDownload();
            checkCleartextTrafficPermission(mInfo);
            executeDownload(state);

//            finalizeDestinationFile(state);
            finalStatus = Downloads.Impl.STATUS_SUCCESS;
            errorMsg = Downloads.Impl.statusToString(finalStatus);
        } catch (StopRequestException error) {
            XLConfig.LOGD("exc", error);
            // remove the cause before printing, in case it contains PII
            errorMsg = error.getMessage();
            finalStatus = error.getFinalStatus();
            soRet = error.getSoRet();
            String fileName = mDownloadFile != null ? mDownloadFile.getName() : "";
            String fileInfo = mInfo.mId + " fileName=" + fileName
                    + " url=" + Statistics.getNotEmptyString(mInfo.mUri);
            String statusInfo = Downloads.Impl.statusToString(finalStatus) + "(" + finalStatus
                    + ")";
            XLConfig.LOGD_INFO(TAG, " runInternal catch StopRequestException:" + fileInfo
                    + " statusInfo=" + statusInfo + ", errorMsg=" + errorMsg);
            // 移动送测版需求: 从WiFi切换到数据网络需要有一个提醒的弹窗

            // Nobody below our level should request retries, since we handle
            // failure counts at this level.
            if (finalStatus == STATUS_WAITING_TO_RETRY) {
                XLConfig.LOGD(TAG, " in runInternal, throw IllegalStateException");
                throw new IllegalStateException("Execution should always throw final error codes");
            }

            // Some errors should be retryable, unless we fail too many times.
            if (isStatusRetryable(finalStatus)) {
//                if (state.mGotData) {
//                    numFailed = 1;
//                } else {
                numFailed += 1;
//                }

                XLConfig.LOGD(TAG, " runInternal status is retryable numFailed="
                        + numFailed);
                if (numFailed < Constants.MAX_RETRIES) {
                    final NetworkInfo info = mSystemFacade.getActiveNetworkInfo();
                    XLConfig.LOGD(TAG, String.format("runInternal %s", NetworkInfo2Str(info)));
                    if (info != null && info.isConnected()) {
                        // Underlying network is still intact, use normal
                        // backoff
                        finalStatus = STATUS_WAITING_TO_RETRY;
                    } else {
                        // Network changed, retry on any next available
                        finalStatus = STATUS_WAITING_FOR_NETWORK;
                    }
                }
            }

        } catch (Exception ex) {
            errorMsg = ex.getMessage();
            String msg = "Exception for id " + mInfo.mId + ": " + errorMsg;
            XLConfig.LOGD_INFO(TAG, msg, ex);
            finalStatus = Downloads.Impl.STATUS_UNKNOWN_ERROR;
        } finally {
            try {
                stopTask(finalStatus, soRet);
            } catch (Exception e) {
                XLConfig.LOGD_INFO(TAG, "stopTask ex", e);
            }
            trackTaskStop(state, finalStatus, soRet, errorMsg);
            if (finalStatus == STATUS_SUCCESS) {
                TrafficStats.incrementOperationCount(1);
            }

            if (finalStatus != XL_STATUS__FAIL) {
                errorMsg = finalStatus + ":" + errorMsg;
            }

            TrafficStats.clearThreadStatsTag();
            TrafficStats.clearThreadStatsUid();

            cleanupDestination(state, finalStatus);
            reportDownloadZeroSpeed();

            notifyDownloadCompleted(state, finalStatus, errorMsg, numFailed);

            XLConfig.LOGD_INFO(TAG, " runInternal " + mInfo.mId + " finished with status "
                    + Downloads.Impl.statusToString(finalStatus));

            netPolicy.unregisterListener(mPolicyListener);
            unRegisterMobileLimitChange(mContext);
            unRegisterNetChange();
            if (mFirewallObserver != null) {
                ExtraNetwork.unRegisterFirewallContentObserver(mContext, mFirewallObserver);
            }

            if (wakeLock != null && wakeLock.isHeld()) {
                wakeLock.release();
                wakeLock = null;
                XLConfig.LOGD_INFO(TAG, "mId=" + mInfo.mId + " release wakeLock ");
            }

            if (wifiLock != null && wifiLock.isHeld()) {
                wifiLock.release();
                wifiLock = null;
                XLConfig.LOGD_INFO(TAG, "mId=" + mInfo.mId + " release wifiLock ");
            }
            final NetworkInfo info = mSystemFacade.getActiveNetworkInfo();
            XLConfig.LOGD_INFO(TAG, String.format("runInternal finished %s", NetworkInfo2Str(info)));
        }

        mStorageManager.incrementNumDownloadsSoFar();
    }

    private void setThreadStatsPid(Context context, int uid,String pkgName) {
        //用来区分1000uid的流量
        int pid = ProcessUtil.getPid(context, pkgName);
        XLConfig.LOGD_INFO(TAG, String.format("setThreadStatsPid  %s===>%d", mInfo.mPackage, pid));
        if (uid != 1000) {
            return;
        }
        try {
            Class trafficStatsClazz = Class.forName("android.net.TrafficStats");
            Method method = trafficStatsClazz.getMethod("setThreadStatsPid", int.class);
            method.invoke(null, pid);
        } catch (Exception e) {
            XLConfig.LOGD(TAG, "setThreadStatsPid e", e);
        }
    }

    /**
     * Receives notifications when the data in the content provider changes
     */
    private class MobileLimiteListener extends ContentObserver {
        public MobileLimiteListener() {
            super(new Handler());
        }

        @Override
        public void onChange(final boolean selfChange) {
            if(Statistics.isMobileActive(mContext)){
                mMobileLimiteChange = true;
            }
        }
    }

    private class FirewallObserver extends ContentObserver {
        public FirewallObserver() {
            super(new Handler());
        }

        @Override
        public void onChange(final boolean selfChange) {
            XLConfig.LOGD(TAG, "FirewallObserver " + selfChange);
            mFirewallRuleChange = true;
        }
    }

    private void registerMobileLimitChange(Context context) {
        if (context == null) {
            XLConfig.LOGD_INFO("registerMobileLimitChange context is null");
            return;
        }
        context.getContentResolver().registerContentObserver(XLConfig.MobileLimitUri, true, mMobileLimiteListener);
    }

    private void unRegisterMobileLimitChange(Context context) {
        if (context == null) {
            XLConfig.LOGD_INFO("unRegisterMobileLimitChange context is null");
            return;
        }
        if (mMobileLimiteListener != null) {
            context.getContentResolver().unregisterContentObserver(mMobileLimiteListener);
        }
    }

    private NetworkChangeManager.NetworkChangeListener mNetworkChangeListener = new NetworkChangeManager.NetworkChangeListener() {
        @Override
        public void onChange(int oldNetType, int newNetType) {
            mNetChange = true;
        }
    };

    private void registerNetChange() {
        NetworkChangeManager.getInstance().registerNetChangeListener(mNetworkChangeListener);
    }

    private void unRegisterNetChange() {
        NetworkChangeManager.getInstance().unRegisterNetChangeListener(mNetworkChangeListener);
    }

    protected void preDownload() {
        //留给子类用
    }

    protected void stopTask() throws StopRequestException {
        //留给子类用
    }

    protected void stopTask(int finalStatus, int ret) throws StopRequestException {
        //留给子类用
    }

    /**
     * Fully execute a single download request. Setup and send the request,
     * handle the response, and transfer the data to the destination file.
     */
    protected void executeDownload(State state) throws StopRequestException {
        XLConfig.LOGD(TAG, " executeDownload() ---> fun called");
        state.resetBeforeExecute();
        setupDestinationFile(state);

        // skip when already finished; remove after fixing race in 5217390
        if (state.mTotalBytes != 0 && state.mCurrentBytes == state.mTotalBytes) {
            XLConfig.LOGD(TAG,
                    " executeDownload Skipping initiating request for download "
                            + mInfo.mId + "; already completed");
            return;
        }

        NetworkInfo info = mSystemFacade.getActiveNetworkInfo(mInfo.mUid);
        // only do this proc in mobile network and new task
        if (info != null && Statistics.isMobileActive(info,mSystemFacade)
                && !state.mContinuingDownload && mInfo.mBypassRecommendedSizeLimit == 0) {
            Long recommendedMaxBytesOverMobile = mSystemFacade.getRecommendedMaxBytesOverMobile();
            if (recommendedMaxBytesOverMobile == null
                    || recommendedMaxBytesOverMobile < DownloadInfo.MAX_BYTES_OVER_MOBILE) {
                mIfMobileFileSizeChecked = true;
                checkFileSizeinMobile(state);
            }
        }
        SSLContext appContext;
        try {
            appContext = getSSLContextForPackage(mContext, mInfo.mPackage);
        } catch (GeneralSecurityException e) {
            // This should never happen.
            throw new StopRequestException(STATUS_UNKNOWN_ERROR, "Unable to create SSLContext.");
        }

        while (state.mRedirectionCount++ < Constants.MAX_REDIRECTS) {
            // Open connection and follow any redirects until we have a useful
            // response with body.
            HttpURLConnection conn = null;
            try {
                checkConnectivity(true);
                // conn = (HttpURLConnection) state.mUrl.openConnection();
                // try mi_extend okhttp only in unmetered wifi situation
                boolean useMiExtend = (info != null && mSystemFacade != null && info.getType() == ConnectivityManager.TYPE_WIFI && !mSystemFacade.isActiveNetworkMetered());
                if (useMiExtend && mMiHttpHandler != null && "http".equalsIgnoreCase(state.mUrl.getProtocol())) {
                    // conn = (HttpURLConnection) mMiHttpHandler.openConnection(state.mUrl);
                    conn = miuiOpenConnection(mMiHttpHandler, state.mUrl);
                }
                else if (useMiExtend && mMiHttpsHandler != null && "https".equalsIgnoreCase(state.mUrl.getProtocol())) {
                    // conn = (HttpURLConnection) mMiHttpsHandler.openConnection(state.mUrl);
                    conn = miuiOpenConnection(mMiHttpsHandler, state.mUrl);
                }
                // else {
                if (conn == null) {
                    conn = (HttpURLConnection) state.mUrl.openConnection();
                }
                conn.setInstanceFollowRedirects(false);
                conn.setConnectTimeout(DEFAULT_TIMEOUT);
                conn.setReadTimeout(DEFAULT_TIMEOUT);
                // If this is going over HTTPS configure the trust to be the same as the calling
                // package.
                if (conn instanceof HttpsURLConnection) {
                    ((HttpsURLConnection)conn).setSSLSocketFactory(appContext.getSocketFactory());

                    setKTlsPrefer((HttpsURLConnection)conn, (networkZeroCopyCloudControl() && !forceDisableZeroCopy));
                    Log.i(TAG, "getKTlsPrefer:" + getKTlsPrefer((HttpsURLConnection)conn));
                }

                addRequestHeaders(state, conn);
                Map<String, List<String>> requestProperties = conn.getRequestProperties();

                XLConfig.LOGD(TAG, " executeDownload addRequestHeaders \nheader=" + requestProperties);
                final int responseCode = conn.getResponseCode();
                XLConfig.LOGD(TAG, " executeDownload responseCode = " + responseCode
                        + ", continuingDownload=" + state.mContinuingDownload + " url="
                        + state.mUrl + "\nheader=" + conn.getHeaderFields());

                switch (responseCode) {
                    case HTTP_OK:
                        if (state.mContinuingDownload) {
                            throw new StopRequestException(
                                    STATUS_CANNOT_RESUME, "Expected partial, but received OK");
                        }
                        processResponseHeaders(state, conn);
                        checkFileSize(getHeaderFieldLong(conn, "Content-Length", -1));
                        transferData(state, conn);
                        return;
                    case HTTP_PARTIAL:
                        if (!state.mContinuingDownload) {
                            throw new StopRequestException(
                                    STATUS_CANNOT_RESUME, "Expected OK, but received partial");
                        }
                        transferData(state, conn);
                        return;
                    case HTTP_MOVED_PERM:
                    case HTTP_MOVED_TEMP:
                    case HTTP_SEE_OTHER:
                    case HTTP_TEMP_REDIRECT:
                        final String location = conn.getHeaderField("Location");
                        state.mUrl = new URL(state.mUrl, location);
                        if (responseCode == HTTP_MOVED_PERM) {
                            // Push updated URL back to database
                            state.mRequestUri = state.mUrl.toString();
                        }
                        continue;

                    case HTTP_REQUESTED_RANGE_NOT_SATISFIABLE:
                        throw new StopRequestException(
                                STATUS_CANNOT_RESUME, "Requested range not satisfiable");

                    case HTTP_UNAVAILABLE:
                        parseRetryAfterHeaders(state, conn);
                        throw new StopRequestException(
                                HTTP_UNAVAILABLE, conn.getResponseMessage());

                    case HTTP_INTERNAL_ERROR:
                        throw new StopRequestException(
                                HTTP_INTERNAL_ERROR, conn.getResponseMessage());

                    default:
                        StopRequestException.throwUnhandledHttpError(
                                responseCode, conn.getResponseMessage());
                }
            } catch (IOException e) {
                XLConfig.LOGD(TAG, " executeDownload error when executeDownload: ", e);
                throw new StopRequestException(STATUS_HTTP_DATA_ERROR, e);
            } finally {
                if (conn != null) {
                    conn.disconnect();
                }
            }
        }

        throw new StopRequestException(STATUS_TOO_MANY_REDIRECTS, "Too many redirects");
    }

    private void checkFileSize(long fileSize) throws StopRequestException {
        if (!BuildUtils.isSdkOverR()) {
            return;
        }
        if (!CloudConfigPreference.getInstance().enableCheckFileSize()) {
            return;
        }
        long sFileSize = mInfo.mFileSize;
        if (sFileSize > 0 && fileSize > 0) {
            if (sFileSize != fileSize) {
                throw new StopRequestException(STATUS_CHECK_FILE_SIZE_FAIL, "check file size fail");
            }
        }
    }

    public SSLContext getSSLContextForPackage(Context context, String packageName)
            throws GeneralSecurityException {
        ApplicationConfig appConfig;
        try {
            appConfig = NetworkSecurityPolicy.getApplicationConfigForPackage(context, packageName);
        } catch (PackageManager.NameNotFoundException e) {
            // Unknown package -- fallback to the default SSLContext
            return SSLContext.getDefault();
        }
        SSLContext ctx = SSLContext.getInstance("TLS");
        ctx.init(null, new TrustManager[] {appConfig.getTrustManager()}, null);
        return ctx;
    }

    /**
     * Transfer data from the given connection to the destination file.
     */
    private void transferData(State state, HttpURLConnection conn) throws StopRequestException {
        DrmManagerClient drmClient = null;
        InputStream in = null;
        OutputStream out = null;
        FileDescriptor outFd = null;

        try {
            try {
                in = conn.getInputStream();
            } catch (IOException e) {
                XLConfig.LOGD(TAG, " transferData error when transferData: ", e);
                throw new StopRequestException(STATUS_HTTP_DATA_ERROR, e);
            }

            try {
                ParcelFileDescriptor outPfd = mContext.getContentResolver()
                        .openFileDescriptor(mInfo.getAllDownloadsUri(), "rw");
                outFd = outPfd.getFileDescriptor();

                if (DownloadDrmHelper.isDrmConvertNeeded(state.mMimeType)) {
                    drmClient = new DrmManagerClient(mContext);
                    out = new DrmOutputStream(drmClient, outPfd, state.mMimeType);
                } else {
                    out = new ParcelFileDescriptor.AutoCloseOutputStream(outPfd);
                }

                // Move into place to begin writing
                Os.lseek(outFd, state.mCurrentBytes, OsConstants.SEEK_SET);
            } catch (Exception e) {
                throw new StopRequestException(STATUS_FILE_ERROR, e);
            }
            mDownloadDestFile = new File(state.mFilename);
            if (!BuildUtils.isTablet() && !Helpers.isEnglishEnv(mContext)
                    && !Helpers.isInternationalBuilder()) {
                Intent intent = new Intent(XLConfig.ACTION_NOTIFICATION_WITHOUT_ENGINE);
                intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                XLConfig.LOGD(TAG,
                        " transferData send ACTION_NOTIFICATION_WITHOUT_ENGINE");
                mContext.sendBroadcast(intent);
            }

            // Start streaming data, periodically watch for pause/cancel
            // commands and checking disk space as needed.
            transferData(state, in, out,outFd);
            try {
                if (out instanceof DrmOutputStream) {
                    ((DrmOutputStream) out).finish();
                }
            } catch (IOException e) {
                throw new StopRequestException(STATUS_FILE_ERROR, e);
            }
        } finally {
            XLConfig.LOGD(TAG, "transferData " + getDownloadInfo()
                    + "---> finally in transferData!");
            if (drmClient != null) {
                drmClient.release();
            }

            IoUtils.closeQuietly(in);

            try {
                if (out != null)
                    out.flush();
                if (outFd != null)
                    outFd.sync();
            } catch (IOException e) {
                XLConfig.LOGD("exc", e);
            } finally {
                IoUtils.closeQuietly(out);
            }
        }
    }

    /**
     * Check if current connectivity is valid for this request.
     */
    protected void checkConnectivity(boolean isTaskRunning) throws StopRequestException {
        mPolicyDirty = false;
        mMobileLimiteChange = false;
        mFirewallRuleChange = false;
        mNetChange = false;

        final NetworkState networkUsable = mInfo.checkCanUseNetwork(isTaskRunning);
        if (networkUsable != NetworkState.OK) {
            Log.i(TAG, " checkConnectivity ---> networkUsable:" + networkUsable);
            int status = networkUsable == NetworkState.NO_CONNECTION ? STATUS_WAITING_FOR_NETWORK : STATUS_QUEUED_FOR_WIFI;
            String errorMsg = null;

            if (networkUsable == NetworkState.UNUSABLE_DUE_TO_SIZE ||
                    networkUsable == NetworkState.RECOMMENDED_UNUSABLE_DUE_TO_SIZE) {
                errorMsg = mInfo.generateReason(STATUS_NET_UNUSABLE_DUE_TO_SIZE, networkUsable.name());
            } else if (networkUsable == NetworkState.TYPE_DISALLOWED_BY_REQUESTOR) {
                errorMsg = mInfo.generateReason(STATUS_NET_DISALLOWED_BY_REQUESTOR, networkUsable.name());
            } else if (networkUsable == NetworkState.TYPE_DISALLOWED_BY_APP) {
                errorMsg = mInfo.generateReason(STATUS_NET_DISALLOWED_BY_APP, networkUsable.name());
            }

            if (TextUtils.isEmpty(errorMsg)) {
                errorMsg = networkUsable.name();
            }
            throw new StopRequestException(status, errorMsg);
        }
    }

    /**
     * prase field value from http response
     *
     * @param field
     * @param target
     * @return
     */
    private String parseFieldValue(String field, final String target) {
        int begin_index = target.indexOf(field);
        int end_index = target.indexOf("\n", begin_index);
        if (end_index == -1) {
            end_index = target.length();
        }
        return target.substring(begin_index + field.length() + 1, end_index);
    }

    // xiaomi okhttp api
    private static Class<?> xiaomiInputStreamClazz = null;
    private static Class<?> xiaomiHttpsURLConnectionImplClazz = null;
    private static Class<?> xiaomiHttpHandlerClazz = null;
    private static Method method_supportMiuiZeroCopy = null;
    private static Method method_recvIntoFile = null;
    private static Method method_setKTlsPrefer = null;
    private static Method method_getKTlsPrefer = null;
    private static Method method_openConnection = null;
    private static boolean miOkhttpApiSupport = false;
    // avoid unexpected situation
    private static boolean forceDisableZeroCopy = false;

    static {
        try {
            xiaomiInputStreamClazz = Class.forName("com.xiaomi.okhttp.okio.RealBufferedSource$MiuiInputStream");
            xiaomiHttpsURLConnectionImplClazz = Class.forName("com.xiaomi.okhttp.internal.huc.HttpsURLConnectionImpl");

            method_supportMiuiZeroCopy = xiaomiInputStreamClazz.getMethod("supportMiuiZeroCopy");
            // add setAccessible if necessary.
            // method_supportMiuiZeroCopy.setAccessible(true);
            method_recvIntoFile = xiaomiInputStreamClazz/*MiuiInputStream.class*/.getMethod("recvIntoFile", FileDescriptor.class, long.class, long.class);
            // add setAccessible if necessary.
            // method_recvIntoFile.setAccessible(true);
            method_setKTlsPrefer = xiaomiHttpsURLConnectionImplClazz.getMethod("setKTlsPrefer", boolean.class);
            method_getKTlsPrefer = xiaomiHttpsURLConnectionImplClazz.getMethod("getKTlsPrefer");
            xiaomiHttpHandlerClazz = Class.forName("com.xiaomi.okhttp.HttpHandler");
            method_openConnection = xiaomiHttpHandlerClazz.getMethod("openConnection", URL.class);
            mMiHttpHandler = /*new HttpHandler()*/xiaomiHttpHandlerClazz.newInstance();
            // HttpsHandler extends HttpHandler
            mMiHttpsHandler = /*new HttpsHandler()*/Class.forName("com.xiaomi.okhttp.HttpsHandler").newInstance();
            miOkhttpApiSupport = true;
        } catch (Throwable e) {
            Log.e(TAG, "miui okhttp api init failed:" + e);
            miOkhttpApiSupport = false;
        }
    }

    /**
     * zero copy cloud controller
     * @return true support
     *         false not support
     */
    private boolean networkZeroCopyCloudControl() {
        final int SUPPORT_VER = 1;
        int ver = android.os.SystemProperties.getInt("sys.network.zerocopy.config.dp", 0);
        return (ver == SUPPORT_VER);
    }

    /**
     * miOkhttpApiSupport should be true.
     */
    private boolean supportNetworkZeroCopy(InputStream in, int uid, String pkgName) {
        // TODO check cloud controller?
        if (!miOkhttpApiSupport) {
            return false;
        }
        //
        try {
            if (xiaomiInputStreamClazz.isInstance(in)) {
                return (boolean)method_supportMiuiZeroCopy.invoke(in);
            }
            else {// TODO delete or use XLConfig.LOGD instead
                Log.i(TAG, "supportNetworkZeroCopy inputstream is not matched xiaomiInputStreamClazz:" + xiaomiInputStreamClazz.getName());
            }
        } catch (Exception e) {
            Log.e(TAG, "supportNetworkZeroCopy catch", e);
        }
        return false;
    }

    /**
     * condition:
     * 1. miOkhttpApiSupport should be true.
     * 2. InputStream should be MiuiInputStream from miui okhttp.
     */
    private long recvIntoFile(InputStream in, FileDescriptor fileFd, long offset, long len) {
        if (!miOkhttpApiSupport) {
            return -101;
        }
        //
        try {
            if (xiaomiInputStreamClazz.isInstance(in)) {
                return (long)method_recvIntoFile.invoke(in, fileFd, offset, len);
            }
            else {// TODO delete or use XLConfig.LOGD instead
                Log.i(TAG, "recvIntoFile inputstream is not matched xiaomiInputStreamClazz:" + xiaomiInputStreamClazz.getName());
            }
        } catch (Exception e) {
            Log.e(TAG, "recvIntoFile catch", e);
        }
        return -102;
    }

    /**
     * condition:
     * 1. miOkhttpApiSupport should be true.
     * 2. HttpsURLConnection should be HttpsURLConnectionImpl from miui okhttp.
     */
    private boolean setKTlsPrefer(HttpsURLConnection conn, boolean prefer) {
        if (!miOkhttpApiSupport) {
            return false;
        }
        //
        try {
            if (xiaomiHttpsURLConnectionImplClazz.isInstance(conn)) {
                method_setKTlsPrefer.invoke(conn, prefer);
                return true;
            }
            else {// TODO delete or use XLConfig.LOGD instead
                Log.i(TAG, "setKTlsPrefer conn is not matched xiaomiHttpsURLConnectionImplClazz:" + xiaomiHttpsURLConnectionImplClazz.getName());
            }
        } catch (Exception e) {
            Log.e(TAG, "setKTlsPrefer catch", e);
        }
        return false;
    }

    /**
     * condition:
     * 1. miOkhttpApiSupport should be true.
     * 2. HttpsURLConnection should be HttpsURLConnectionImpl from miui okhttp.
     */
    private boolean getKTlsPrefer(HttpsURLConnection conn) {
        if (!miOkhttpApiSupport) {
            return false;
        }
        //
        try {
            if (xiaomiHttpsURLConnectionImplClazz.isInstance(conn)) {
                return (boolean)method_getKTlsPrefer.invoke(conn);
            }
            else {// TODO delete or use XLConfig.LOGD instead
                Log.i(TAG, "getKTlsPrefer conn is not matched xiaomiHttpsURLConnectionImplClazz:" + xiaomiHttpsURLConnectionImplClazz.getName());
            }
        } catch (Exception e) {
            Log.e(TAG, "getKTlsPrefer catch", e);
        }
        return false;
    }

    /**
     * miOkhttpApiSupport should be true.
     * HttpsHandler extends HttpHandler: sync with mi-okhttp library
     */
    private HttpURLConnection miuiOpenConnection(Object handler, URL url) {
        // TODO check cloud controller?
        if (!miOkhttpApiSupport) {
            return null;
        }
        Log.i(TAG, "miuiOpenConnection handler:" + handler + " url:" + url);
        try {
            if (xiaomiHttpHandlerClazz.isInstance(handler)) {
                return (HttpURLConnection)method_openConnection.invoke(handler, url);
            }
            else {// TODO delete or use XLConfig.LOGD instead
                Log.i(TAG, "miuiOpenConnection handler is not matched xiaomiHttpHandlerClazz handler's Class:" + handler.getClass());
            }
        } catch (Exception e) {
            Log.e(TAG, "miuiOpenConnection catch", e);
        }
        return null;
    }

    /**
     * supportNetworkZeroCopy should be true.
     * combine readFromResponse and writeDataToDestination
     * Read some data from the HTTP response stream, handling I/O errors.
     *
     * @param data buffer to use to read data
     * @param entityStream stream for reading the HTTP response entity
     * @return the number of bytes actually read or -1 if the end of the stream
     *         has been reached
     */
    private long recvFromResponseToDestination(State state, InputStream entityStream, FileDescriptor fileFd, long offset, long len)
            throws StopRequestException {
        if (!miOkhttpApiSupport) {
            return -101;
        }
        long ret = -100;
        try {
            if (xiaomiInputStreamClazz.isInstance(entityStream)) {
                ret = (long)method_recvIntoFile.invoke(entityStream, fileFd, offset, len);
                // it's seem useless now, just set true at here.
                state.mGotData = true;
            }
            else {// TODO delete or use XLConfig.LOGD instead
                Log.i(TAG, "recvIntoFile inputstream is not matched xiaomiInputStreamClazz:" + xiaomiInputStreamClazz.getName());
                return -102;
            }
            // return entityStream.read(data);
        } catch(IllegalAccessException ignored) {
            // ignore
        } catch(InvocationTargetException ie) {
            // get real excaption
            Throwable thr = ie.getCause();
            boolean isFileSystemError = false;
            if (thr != null && thr instanceof ErrnoException) {
                // throw (ErrnoException) thr;
                isFileSystemError = true;
            }
            if (thr != null && thr instanceof IOException) {
                String msg = thr.getMessage();
                if (msg != null && msg.startsWith("xiaomi zero copy filesystem error")) {
                    isFileSystemError = true;
                }
                else {
                    return dealWithNetworkError(state, (IOException)thr);
                }
            }
            if (isFileSystemError) {
                // refer to writeDataToDestination begin:
                // file system ioexception
                // TODO: better differentiate between DRM and disk failures
                // 此处无法反复单独write，文件系统错误检查空间后一定进行报错
                // if (!forceVerified) {
                // couldn't write to file. are we out of space? check.
                mStorageManager.verifySpace(mInfo.mDestination, state.mFilename,
                        len);// TODO 找一个更合适的数值来判断是否没有空间了
                // forceVerified = true;
                // } else {
                throw new StopRequestException(Downloads.Impl.STATUS_FILE_ERROR,
                        "Failed to write data: " + thr);
                // }
                // refer to writeDataToDestination end
            }
            else {
                // unexpected exception.
                throw new StopRequestException(STATUS_UNKNOWN_ERROR, "Xiaomi zero copy api found unexcepted error.");
            }
        }
        return ret;
    }

    /**
     * deal with network error
     */
    private long dealWithNetworkError(State state, IOException ioe) throws StopRequestException {
        String msg = ioe.getMessage();
        ErrnoException errCause = null;
        // network error
        // refer to readFromResponse begin:
        // TODO: handle stream errors the same as other retries
        if ("unexpected end of stream".equals(msg)) {
            return -1;
        }

        ContentValues values = new ContentValues();
        values.put(Downloads.Impl.COLUMN_CURRENT_BYTES, state.mCurrentBytes);
        mContext.getContentResolver().update(mInfo.getAllDownloadsUri(), values, null, null);
        // miui custom ktls exception
        // sync with ZERO_COPY_INTERNAL_ERROR_MSG_PREFIX
        if (msg.startsWith("xiaomi zero copy unexpected situation:")) {
            // 目前采用出现非预期场景直接禁用功能到进程退出的方案
            forceDisableZeroCopy = true;
            // 屏蔽功能后允许重试
            throw new StopRequestException(STATUS_HTTP_DATA_ERROR,
                    "Xiaomi zero copy unexpected situation: " + ioe, ioe);
        }
        // sync with ZERO_COPY_KTLS_ERROR_MSG_PREFIX
        if (msg.startsWith("xiaomi zero copy ktls error:")) {
            // 判断cause的 errno 识别真实原因
            Throwable errThr = ioe.getCause();
            if (errThr != null && errThr instanceof ErrnoException) {
                errCause = (ErrnoException) errThr;
                // EBADMSG(-74) 代表内核ktls处理tls record遇到问题.
                Log.e(TAG, "errCause:" + errCause);
            }
        }
        if (cannotResume(state)) {
            throw new StopRequestException(STATUS_CANNOT_RESUME,
                    "Failed reading response: " + ioe + "; unable to resume", ioe);
        } else {
            throw new StopRequestException(STATUS_HTTP_DATA_ERROR,
                    "Failed reading response: " + ioe, ioe);
        }
        // refer to readFromResponse end
    }

    /**
     * Transfer as much data as possible from the HTTP response to the
     * destination file.
     *
     * @param data buffer to use to read data
     * @param entityStream stream for reading the HTTP response entity
     */
    private void transferData(State state, InputStream in, OutputStream out,FileDescriptor outFd)
            throws StopRequestException {
        final byte data[] = new byte[Constants.BUFFER_SIZE];
        long countBytes = 0;
        long begintime = mSystemFacade.currentTimeMillis();
        boolean miuiApiInternalError = false;
        boolean forceOrigin = !networkZeroCopyCloudControl() || forceDisableZeroCopy || !supportNetworkZeroCopy(in, mInfo.mUid, mInfo.mPackage);
        Log.i(TAG, "transferData in:" + in.getClass() + " supportNetworkZeroCopy:" + supportNetworkZeroCopy(in, mInfo.mUid, mInfo.mPackage)
                + " forceDisableZeroCopy:" + forceDisableZeroCopy + " forceOrigin:" + forceOrigin + " target:" + state.mFilename);
        for (;;) {
            checkPausedOrCanceled(state);
            if (!forceOrigin && !miuiApiInternalError/* && supportNetworkZeroCopy(in, mInfo.mUid, mInfo.mPackage)*/) {
                // Log.i(TAG, "lethe transferData use zero copy api");
                long bytesRead = recvFromResponseToDestination(state, in, outFd, 0, Constants.BUFFER_SIZE);
                if (bytesRead == -1) { // success, end of stream already reached
                    handleEndOfStream(state);
                    return;
                }
                if (bytesRead <= -99) { // miui api internal failure
                    // 终止使用该接口改用原生路径
                    Log.e(TAG, "transferData zero copy api fail:" + bytesRead);
                    miuiApiInternalError = true;
                    continue;
                }
                state.mCurrentBytes += bytesRead;
                countBytes += bytesRead;
            }
            else {
                // origin logic
                int bytesRead = readFromResponse(state, data, in);
                if (bytesRead == -1) { // success, end of stream already reached
                    handleEndOfStream(state);
                    return;
                }

                state.mGotData = true;
                writeDataToDestination(state, data, bytesRead, out);
                state.mCurrentBytes += bytesRead;
                countBytes += bytesRead;
            }

            long endtime = mSystemFacade.currentTimeMillis();
            long time = endtime - begintime;
            if (time >= Constants.MIN_PROGRESS_TIME) {
                begintime = endtime;

                XLConfig.LOGD(TAG, "transferData ----->duration=" + time);
                state.mDownloadingCurrentSpeed = 1000 * countBytes / time;
                XLConfig.LOGD(TAG, " transferData " + getDownloadInfo()
                        + " Android-download ---> bytesRead=" + countBytes
                        + ", time=" + time
                        + ", speed=" + state.mDownloadingCurrentSpeed
                        + " currentBytes = " + state.mCurrentBytes);
                reportProgress(state,outFd);
                checkFileOrSpace(state, countBytes);
                countBytes = 0;
            }
        }
    }

    private void checkFileOrSpace(State state,long bytesRead) throws StopRequestException {
        long needSpace = state.mTotalBytes < 0 ? bytesRead : state.mTotalBytes - state.mCurrentBytes;
        mStorageManager.verifySpaceBeforeWritingToFile(
                mInfo.mDestination, state.mFilename, needSpace);
        checkFileExists(mDownloadDestFile, state);
    }

    /**
     * Called after a successful completion to take any necessary action on the
     * downloaded file.
     */
    private void finalizeDestinationFile(State state) {
        if (state.mFilename != null && state.mDownloadingFileName != null) {
            // move file xxx.midownload to xxx
            File oldFile = new File(state.mDownloadingFileName);
            if (oldFile.exists()) {
                File newFile = new File(state.mFilename);
                if (newFile.exists()) {
                    newFile.delete();
                }
                newFile.getParentFile().mkdirs();
                oldFile.renameTo(newFile);
                // make sure the file is readable
                FileUtils.setPermissions(state.mFilename, 0644, -1, -1);

                boolean retCfgFile = new File(state.mDownloadingFileName + Helpers.sDownload2GCfgFileExtension).delete();
                if (!retCfgFile) {
                    XLConfig.LOGD(TAG, " cleanupDestination() delete cfg fail ");
                }
            }
        }
    }

    /**
     * Called just before the thread finishes, regardless of status, to take any
     * necessary action on the downloaded file.
     */
    private void cleanupDestination(State state, int finalStatus) {
        if (state.mFilename != null && Downloads.Impl.isStatusError(finalStatus)) {
            XLConfig.LOGD(TAG, " cleanupDestination() deleting " + state.mFilename);
            if (state.mFilename != null) {
                boolean retDownloadingFile = new File(state.mFilename).delete();
                if (!retDownloadingFile) {
                    XLConfig.LOGD(TAG, " cleanupDestination() delete fail " + state.mFilename);
                }
                XLConfig.LOGD(TAG, " cleanupDestination: delete file: "
                        + state.mFilename);
            }
            state.mFilename = null;
        } else if (Downloads.Impl.isStatusSuccess(finalStatus)) {
            // When success, open access if local file
            if (state.mFilename != null && Helpers.isFileInExternalAndroidDirs(state.mFilename)) {
                // Files that are downloaded in Android/ may need fixing up
                // of permissions on devices without sdcardfs; do so here,
                // before we give the file back to the client
                File file = new File(state.mFilename);
                mStorageManager.fixupAppDir(file.getParentFile());
            }
        }
    }

    /**
     * Check if the download has been paused or canceled, stopping the request
     * appropriately if it has been.
     */
    protected void checkPausedOrCanceled(State state) throws StopRequestException {
        checkStatePausedOrCanceled(state);
        //if policy has been changed, trigger connectivity check
        if (mPolicyDirty || mMobileLimiteChange || mFirewallRuleChange || mNetChange) {
            //这个不能去掉，只有在需要的时候才去连网检测
            XLConfig.LOGD_INFO(TAG, String.format("checkPausedOrCanceled mPolicyDirty=%b " +
                            "mMobileLimiteChange = %b mFirewallRuleChange = %b mNetChange= %b", mPolicyDirty,
                    mMobileLimiteChange, mFirewallRuleChange, mNetChange));
            checkConnectivity(true);
        }
    }

    protected void checkStatePausedOrCanceled(State state) throws StopRequestException {
        synchronized (mInfo) {
            if (mInfo.mControl == Downloads.Impl.CONTROL_PAUSED) {
                throw new StopRequestException(Downloads.Impl.STATUS_PAUSED_BY_APP,
                        "download paused by owner");
            }
            if (mInfo.mStatus == Downloads.Impl.STATUS_CANCELED || mInfo.mDeleted) {
                throw new StopRequestException(Downloads.Impl.STATUS_CANCELED, "task_canceled");
            }

            if (mInfo.mStatus == Downloads.Impl.STATUS_QUEUED_FOR_WIFI) {
                throw new StopRequestException(Downloads.Impl.STATUS_QUEUED_FOR_WIFI,
                        mInfo.mErrorMsg);
            }

            if (mInfo.mStatus == Downloads.Impl.STATUS_WAITING_FOR_NETWORK) {
                throw new StopRequestException(Downloads.Impl.STATUS_WAITING_FOR_NETWORK,
                        mInfo.mErrorMsg);
            }
        }
    }

    /**
     * Report download progress through the database if necessary.
     */
    protected void reportProgress(State state) {
        reportProgress(state,null);
    }

    /**
     * Report download progress through the database if necessary.
     */
    protected void reportProgress(State state,FileDescriptor outFd) {
        final long now = SystemClock.elapsedRealtime();

        final long sampleDelta = now - state.mSpeedSampleStart;
        if (sampleDelta > 500) {
            final long sampleSpeed = ((state.mCurrentBytes - state.mSpeedSampleBytes) * 1000)
                    / sampleDelta;

            if (state.mSpeed == 0) {
                state.mSpeed = sampleSpeed;
            } else {
                state.mSpeed = ((state.mSpeed * 3) + sampleSpeed) / 4;
            }

            // Only notify once we have a full sample window
            if (state.mSpeedSampleStart != 0) {
                mNotifier.notifyDownloadSpeed(mInfo.mId, state.mSpeed);
            }

            state.mSpeedSampleStart = now;
            state.mSpeedSampleBytes = state.mCurrentBytes;
        }
        long durationSize = state.mCurrentBytes - state.mBytesNotified;
        if ((durationSize > Constants.MIN_PROGRESS_STEP
                || (mPreDownloadSpeed != state.mDownloadingCurrentSpeed)
                || (!mHasUpdateFilesizeToDB && state.mTotalBytes > 0))
                && now - state.mTimeLastNotification > Constants.MIN_PROGRESS_TIME) {
            mPreDownloadSpeed = state.mDownloadingCurrentSpeed;
            ContentValues values = new ContentValues();
            values.put(Downloads.Impl.COLUMN_CURRENT_BYTES, state.mCurrentBytes);
            values.put(ExtraDownloads.COLUMN_DOWNLOADING_CURRENT_SPEED,
                    state.mDownloadingCurrentSpeed);
            values.put(ExtraDownloads.COLUMN_DOWNLOAD_SURPLUS_TIME, state.mDownloadSurplustime);
            if (1 == state.mXlTaskOpenMark) {
                values.put(ExtraDownloads.COLUMN_XL_ACCELERATE_SPEED, state.mXlAccelerateSpeed);
            }
            if ((!mHasUpdateFilesizeToDB && state.mTotalBytes > 0)
                    || mTotalBytes != state.mTotalBytes) {
                mHasUpdateFilesizeToDB = true;
                values.put(Downloads.Impl.COLUMN_TOTAL_BYTES, state.mTotalBytes);
            }
            if (outFd != null) {
                // fsync() to ensure that current progress has been flushed to disk,
                // so we can always resume based on latest database information.
                try {
                    outFd.sync();
                } catch (SyncFailedException e) {
                    XLConfig.LOGD("exc", e);
                }
            }
            values.put(COLUMN_PERCENT, state.mPercent);
            mContext.getContentResolver().update(mInfo.getAllDownloadsUri(), values, null, null);
            state.mBytesNotified = state.mCurrentBytes;
            state.mTimeLastNotification = now;
            if (Constants.sDownloadSetNeedToUpdateProgress.contains(mInfo.mId)) {
                mInfo.sendDownloadProgressUpdateIntent();
            }
        }
    }

    /**
     * Report download zero speed when task stoped
     */
    private void reportDownloadZeroSpeed() {
        ContentValues values = new ContentValues();
        values.put(ExtraDownloads.COLUMN_DOWNLOADING_CURRENT_SPEED, 0);
        values.put(ExtraDownloads.COLUMN_XL_ACCELERATE_SPEED, 0);
        values.put(ExtraDownloads.COLUMN_DOWNLOAD_SURPLUS_TIME, 0);
        mContext.getContentResolver().update(mInfo.getAllDownloadsUri(), values, null, null);
    }

    /**
     * Write a data buffer to the destination file.
     *
     * @param data buffer containing the data to write
     * @param bytesRead how many bytes to write from the buffer
     */
    private void writeDataToDestination(State state, byte[] data, int bytesRead, OutputStream out)
            throws StopRequestException {
        boolean forceVerified = false;
        while (true) {
            try {
                out.write(data, 0, bytesRead);
                return;
            } catch (IOException ex) {
                // TODO: better differentiate between DRM and disk failures
                if (!forceVerified) {
                    // couldn't write to file. are we out of space? check.
                    mStorageManager.verifySpace(mInfo.mDestination, state.mFilename,
                            bytesRead);
                    forceVerified = true;
                } else {
                    throw new StopRequestException(Downloads.Impl.STATUS_FILE_ERROR,
                            "Failed to write data: " + ex);
                }
            }
        }
    }

    protected void checkFileExists(File file, State state) throws StopRequestException {
        if (file == null) {
            return;
        }

        if (state.mCurrentBytes <= 0) {
            return;
        }
        if (!file.exists()) {
            if (!mStorageManager.isExternalSdcardMounted(mContext, file.getPath())) {
                throw new StopRequestException(STATUS_DEVICE_NOT_FOUND_ERROR,
                        String.format("STATUS_DEVICE_NOT_FOUND_ERROR(%d) downloading file(%s)",
                                STATUS_DEVICE_NOT_FOUND_ERROR, file.toString()));
            }
            throw new StopRequestException(STATUS_DOWNLOAD_FILE_NOT_EXSIT,
                    String.format("downloading file(%s) not exsit ", file.toString()));
        }
    }

    /**
     * Called when we've reached the end of the HTTP response stream, to update
     * the database and check for consistency.
     */
    private void handleEndOfStream(State state) throws StopRequestException {
        ContentValues values = new ContentValues();
        values.put(Downloads.Impl.COLUMN_CURRENT_BYTES, state.mCurrentBytes);
        if (state.mContentLength <= 0) {
            values.put(Downloads.Impl.COLUMN_TOTAL_BYTES, state.mCurrentBytes);
        }
        mContext.getContentResolver().update(mInfo.getAllDownloadsUri(), values, null, null);

        final boolean lengthMismatched = (state.mContentLength > 0)
                && (state.mCurrentBytes != state.mContentLength);
        if (lengthMismatched) {
            if (cannotResume(state)) {
                throw new StopRequestException(STATUS_CANNOT_RESUME,
                        "mismatched content length; unable to resume");
            } else {
                throw new StopRequestException(STATUS_HTTP_DATA_ERROR,
                        "closed socket before end of file");
            }
        }
    }

    private boolean cannotResume(State state) {
        XLConfig.LOGD(TAG, " cannotResume state=" + state + ", mCurrentBytes="
                + state.mCurrentBytes + ", mNoIntegrity=" + mInfo.mNoIntegrity
                + ", mHeaderEtag=" + state.mHeaderETag + ", mHeaderIfRangeId="
                + state.mHeaderIfRangeId + ", mMimeType=" + state.mMimeType);
        return (state.mCurrentBytes > 0 && !mInfo.mNoIntegrity && state.mHeaderETag == null
                && state.mHeaderIfRangeId == null)
                || DownloadDrmHelper.isDrmConvertNeeded(state.mMimeType);
    }

    /**
     * Read some data from the HTTP response stream, handling I/O errors.
     *
     * @param data buffer to use to read data
     * @param entityStream stream for reading the HTTP response entity
     * @return the number of bytes actually read or -1 if the end of the stream
     *         has been reached
     */
    private int readFromResponse(State state, byte[] data, InputStream entityStream)
            throws StopRequestException {
        try {
            return entityStream.read(data);
        } catch (IOException ex) {
            // TODO: handle stream errors the same as other retries
            if ("unexpected end of stream".equals(ex.getMessage())) {
                return -1;
            }

            ContentValues values = new ContentValues();
            values.put(Downloads.Impl.COLUMN_CURRENT_BYTES, state.mCurrentBytes);
            mContext.getContentResolver().update(mInfo.getAllDownloadsUri(), values, null, null);
            if (cannotResume(state)) {
                throw new StopRequestException(STATUS_CANNOT_RESUME,
                        "Failed reading response: " + ex + "; unable to resume", ex);
            } else {
                throw new StopRequestException(STATUS_HTTP_DATA_ERROR,
                        "Failed reading response: " + ex, ex);
            }
        }
    }

    /**
     * Prepare target file based on given network response. Derives filename and
     * target size as needed.
     */
    protected void processResponseHeaders(State state, HttpURLConnection conn)
            throws StopRequestException {
        // TODO: fallocate the entire file if header gave us specific length

        if (conn != null) {
            readResponseHeaders(state, conn);
        }
        state.mFilename = Helpers.generateSaveFile(
                mContext,
                mInfo.mUri,
                mInfo.mHint,
                state.mContentDisposition,
                state.mContentLocation,
                state.mMimeType,
                mInfo.mDestination,
                state.mContentLength,
                mStorageManager,
                true,
                needFixExtension(mInfo.mPackage));
        // correct mimetype
        correctMimeType(state);
        // update header values into database
        updateDatabaseFromHeaders(state);
        // now we get filename, and check space.
        mStorageManager.verifySpace(mInfo.mDestination, state.mFilename, state.mTotalBytes);
        // check connectivity again now that we know the total size
        checkConnectivity(true);
    }

    protected boolean needFixExtension(String sourcePackage) {
        if (TextUtils.isEmpty(sourcePackage)) {
            return false;
        }
        if (TextUtils.equals(sourcePackage, "com.xiaomi.market")) {
            return true;
        }
        if (TextUtils.equals(sourcePackage, "com.xiaomi.gamecenter")) {
            return true;
        }
        return false;
    }

    /**
     * Update necessary database fields based on values of HTTP response headers
     * that have been read.
     */
    protected void updateDatabaseFromHeaders(State state) {
        ContentValues values = new ContentValues();
        values.put(Downloads.Impl._DATA, state.mFilename);
        if (state.mHeaderETag != null) {
            values.put(Constants.ETAG, state.mHeaderETag);
        }
        if (state.mHeaderIfRangeId != null) {
            values.put(ExtraDownloads.COLUMN_IF_RANGE_ID, state.mHeaderIfRangeId);
        }
        if (state.mMimeType != null) {
            values.put(Downloads.Impl.COLUMN_MIME_TYPE, state.mMimeType);
        }
        values.put(Downloads.Impl.COLUMN_TOTAL_BYTES, mInfo.mTotalBytes);
        mContext.getContentResolver().update(mInfo.getAllDownloadsUri(), values, null, null);
    }

    /**ø
     * Read headers from the HTTP response and store them into local state.
     */
    protected void readResponseHeaders(State state, HttpURLConnection conn)
            throws StopRequestException {
        state.mContentDisposition = conn.getHeaderField("Content-Disposition");
        state.mContentLocation = conn.getHeaderField("Content-Location");

        if (state.mMimeType == null) {
            String mimeType = Intent.normalizeMimeType(conn.getContentType());
            if (!TextUtils.isEmpty(mimeType)) {
                state.mMimeType = mimeType;
                state.mMimeTypeFromHead = true;
            }
        }

        state.mHeaderETag = conn.getHeaderField("ETag");
        state.mHeaderIfRangeId = conn.getHeaderField("Last-Modified"); // be
        // careful
        // in
        // here -
        // added
        // by
        // xunlei
        state.mHeaderAcceptRanges = conn.getHeaderField("Accept-Ranges");
        final String transferEncoding = conn.getHeaderField("Transfer-Encoding");
        if (transferEncoding == null) {
            state.mContentLength = getHeaderFieldLong(conn, "Content-Length", -1); // get
            // file
            // size
            // form
            // http
            // content
        } else {
            XLConfig.LOGD(
                    TAG,
                    "  readResponseHeaders Ignoring Content-Length since Transfer-Encoding is also defined");
            state.mContentLength = -1;
        }

        state.mTotalBytes = state.mContentLength;
        mInfo.mTotalBytes = state.mContentLength;

        final boolean hasLength = state.mContentLength != -1;
        final boolean isConnectionClose = "close".equalsIgnoreCase(conn.getHeaderField("Connection"));
        final boolean isEncodingChunked = "chunked".equalsIgnoreCase(transferEncoding);
        final boolean finishKnown = hasLength || isConnectionClose || isEncodingChunked;
        if (!finishKnown) {
            throw new StopRequestException(STATUS_CANNOT_RESUME,
                    "can't know size of download, giving up");
        }
    }

    protected void parseRetryAfterHeaders(State state, HttpURLConnection conn) {
        if (conn != null) {
            state.mRetryAfter = conn.getHeaderFieldInt("Retry-After", -1);
        }
        if (state.mRetryAfter < 0) {
            state.mRetryAfter = 0;
        } else {
            if (state.mRetryAfter < Constants.MIN_RETRY_AFTER) {
                state.mRetryAfter = Constants.MIN_RETRY_AFTER;
            } else if (state.mRetryAfter > Constants.MAX_RETRY_AFTER) {
                state.mRetryAfter = Constants.MAX_RETRY_AFTER;
            }
            state.mRetryAfter += Helpers.getsRandom().nextInt(Constants.MIN_RETRY_AFTER + 1);
            state.mRetryAfter *= 1000;
        }
    }

    /**
     * Prepare the destination file to receive data. If the file already exists,
     * we'll set up appropriately for resumption.
     */
    protected void setupDestinationFile(State state) throws StopRequestException {
        if (!TextUtils.isEmpty(state.mFilename)) { // only true if we've already
            // run a thread for this
            // download
            XLConfig.LOGD(TAG, "setupDestinationFile run thread before for id: "
                    + mInfo.mId +
                    ", and state.mFilename: " + state.mFilename);

            // We're resuming a download that got interrupted
            File f = new File(state.mFilename);
            if (f.exists()) {
                XLConfig.LOGD(TAG, "setupDestinationFile resuming download for id: "
                        + mInfo.mId +
                        ", and state.mFilename: " + state.mFilename);
                long fileLength = f.length();
                if (fileLength == 0) {
                    // The download hadn't actually started, we can restart from
                    // scratch
                    XLConfig.LOGD(TAG,
                            "setupDestinationFile() found fileLength=0, deleting "
                                    + state.mFilename);
                    boolean result=f.delete();
                    if (!result) {
                        XLConfig.LOGD(TAG, " setupDestinationFile: delete file fail "
                                + state.mFilename);
                    }
                    state.mFilename = null;
                    XLConfig.LOGD(TAG,
                            " setupDestinationFile resuming download for id: "
                                    + mInfo.mId +
                                    ", BUT starting from scratch again: ");
                } else {
                    // All right, we'll be able to resume this download
                    XLConfig.LOGD(TAG,
                            " setupDestinationFile resuming download for id: "
                                    + mInfo.mId +
                                    ", and starting with file of length: " + fileLength);
                    state.mCurrentBytes = fileLength;
                    if (mInfo.mTotalBytes > 0) {
                        state.mContentLength = mInfo.mTotalBytes;
                    }
                    state.mHeaderETag = mInfo.mETag;
                    state.mHeaderIfRangeId = mInfo.mIfRange;
                    state.mContinuingDownload = true;

                    XLConfig.LOGD(TAG,
                            " setupDestinationFile resuming download for id: "
                                    + mInfo.mId +
                                    ", state.mCurrentBytes: " + state.mCurrentBytes +
                                    ", and setting mContinuingDownload to true: ");
                }
            } else {
                // if file does not exist, means it was deleted before it is
                // finished.
                state.mCurrentBytes = mInfo.mCurrentBytes = 0;
            }
        }
    }

    /**
     * Add custom headers for this download to the HTTP request.
     */
    protected void addRequestHeadersToXlEngine(XLDownloadManager manager, long taskId) {
        if (manager == null) {
            return;
        }

        for (Pair<String, String> header : mInfo.getHeaders()) {
            if (header.first != null && header.second != null) {
                XLConfig.LOGD(TAG, " addRequestHeadersToXlEngine " + header.first
                        + ": "
                        + header.second);
                manager.setHttpHeaderProperty(taskId, header.first, header.second);
            }
        }
    }

    /**
     * Add custom headers for this download to the HTTP request.
     */
    protected void addRequestHeaders(State state, HttpURLConnection conn) {
        for (Pair<String, String> header : mInfo.getHeaders()) {
            conn.addRequestProperty(header.first, header.second);
        }

        // Only splice in user agent when not already defined
        if (conn.getRequestProperty("User-Agent") == null) {
            conn.addRequestProperty("User-Agent", userAgent());
        }

        // Defeat transparent gzip compression, since it doesn't allow us to
        // easily resume partial downloads.
        if (0 == state.mXlTaskOpenMark) {
            conn.setRequestProperty("Accept-Encoding", "identity");
        }

        // Defeat connection reuse, since otherwise servers may continue
        // streaming large downloads after cancelled.
        conn.setRequestProperty("Connection", "close");

        if (state.mContinuingDownload) {
            if (state.mHeaderETag != null) {
                conn.addRequestProperty("If-Match", state.mHeaderETag);
            }
            if (state.mHeaderIfRangeId != null) {
                conn.addRequestProperty("If-Range", state.mHeaderIfRangeId);
            }
            conn.addRequestProperty("Range", "bytes=" + state.mCurrentBytes + "-");
        }
    }

    /**
     * Stores information about the completed download, and notifies the
     * initiating application.
     */
    private void notifyDownloadCompleted(
            State state, int finalStatus, String errorMsg, int numFailed) {
        if (Downloads.Impl.isStatusSuccess(finalStatus)) {
            //如果成功下载一个apk，则上报给小米统计，提前是为了防止下载的apk被删除
            Statistics.uploadApkDownload(mContext, state.mPackage, state.mFilename, state.mPassBackStr);
        }
        notifyThroughDatabase(state, finalStatus, errorMsg, numFailed);
        if (Downloads.Impl.isStatusCompleted(finalStatus)) {
            Constants.sDownloadSetNeedToUpdateProgress.remove(mInfo.mId);
            mInfo.sendDownloadProgressUpdateIntent();
            mInfo.sendIntentIfRequested();
        }

        if (Downloads.Impl.isStatusSuccess(finalStatus)) {
            boolean isApk = state.mFilename != null ? state.mFilename.endsWith(".apk") : false;
            if (isApk) {
                mInfo.sendCompletedBroadcast();
            }
        }
    }

    protected void notifyThroughDatabase(
            State state, int finalStatus, String errorMsg, int numFailed) {
        ContentValues values = new ContentValues();
        // 如果本次下载被手动暂停了，那么数据库字段一定是事先更新过的，这里不需要再次更新，防止快速点击暂停开始的时候，RUNNING的状态在这里被覆盖了
        if (finalStatus != Downloads.Impl.STATUS_PAUSED_BY_APP) {
            values.put(Downloads.Impl.COLUMN_STATUS, finalStatus);
        }
        values.put(Downloads.Impl._DATA, state.mFilename);
        values.put(Downloads.Impl.COLUMN_MIME_TYPE, state.mMimeType);
        values.put(Downloads.Impl.COLUMN_LAST_MODIFICATION, mSystemFacade.currentTimeMillis());
        values.put(Downloads.Impl.COLUMN_FAILED_CONNECTIONS, numFailed);
        values.put(Constants.RETRY_AFTER_X_REDIRECT_COUNT, state.mRetryAfter);

        if (!TextUtils.equals(mInfo.mUri, state.mRequestUri)) {
            values.put(Downloads.Impl.COLUMN_URI, state.mRequestUri);
        }

        // save the error message. could be useful to developers.
        if (!TextUtils.isEmpty(errorMsg)) {
            values.put(Downloads.Impl.COLUMN_ERROR_MSG, errorMsg);
        }

        XLConfig.LOGD(TAG, " notifyThroughDatabase " + values);
        mContext.getContentResolver().update(mInfo.getAllDownloadsUri(), values, null, null);
    }

    private INetworkPolicyListener mPolicyListener = new SimpleNetworkPolicyManager.Listener() {
        private static final String TAG = "NetworkPolicyListener";

        public  void onPolicyChange() {
            XLConfig.LOGD(TAG, " onPolicyChange");
            mPolicyDirty = true;
        }

        public  void onUidPolicyChange(int uid) {
            if (uid == mInfo.mUid) {
                mPolicyDirty = true;
            }
            XLConfig.LOGD(TAG, " onUidPolicyChange uid=" + uid + ", mInfo.uid="
                    + mInfo.mUid);
        }
    };

    public static long getHeaderFieldLong(URLConnection conn, String field, long defaultValue) {
        try {
            return Long.parseLong(conn.getHeaderField(field));
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * Return if given status is eligible to be treated as
     * {@link Downloads.Impl#STATUS_WAITING_TO_RETRY}.
     */
    public static boolean isStatusRetryable(int status) {
        switch (status) {
            case STATUS_HTTP_DATA_ERROR:
            case HTTP_UNAVAILABLE:
            case HTTP_INTERNAL_ERROR:
                return true;
            default:
                return false;
        }
    }

    /**
     * Get right MIME type
     *
     * @param state
     */
    protected void correctMimeType(State state) {
        // do not change mime type of drm
        if (DownloadDrmHelper.isDrmConvertNeeded(state.mMimeType)) {
            return;
        }
        //mimetype来源于Header才修改
        if(!state.mMimeTypeFromHead){
            return ;
        }

        if (TextUtils.isEmpty(state.mFilename)) {
            return;
        }

        int dotIndex = state.mFilename.lastIndexOf('.');
        boolean missingExtension = dotIndex < 0 || dotIndex < state.mFilename.lastIndexOf('/')
                || dotIndex == (state.mFilename.length() - 1);
        if (missingExtension) {
            return;
        }
        String extension = state.mFilename.substring(dotIndex + 1);

        // get MIME type according to extension name
        String mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension);
        if (!TextUtils.isEmpty(mimeType) && !mimeType.equals(state.mMimeType)) {
            state.mMimeType = mimeType;
        }
    }

    public boolean getVipSwitchStatus() {
        return DownloadSettings.XLShareConfigSettings.isVipEnable();
    }

    protected void checkFileSizeinMobile(State state) throws StopRequestException {

        SSLContext appContext;
        try {
            appContext = getSSLContextForPackage(mContext, mInfo.mPackage);
        } catch (GeneralSecurityException e) {
            // This should never happen.
            throw new StopRequestException(STATUS_UNKNOWN_ERROR, "Unable to create SSLContext.");
        }

        long fileSize = -1;
        String headerTransferEncoding = null;

        int index = 0;

        while (index++ < Constants.MAX_REDIRECTS) {
            HttpURLConnection conn = null;
            try {
                conn = (HttpURLConnection) state.mUrl.openConnection();
                conn.setInstanceFollowRedirects(false);
                conn.setConnectTimeout(DEFAULT_TIMEOUT);
                conn.setReadTimeout(DEFAULT_TIMEOUT);
                // If this is going over HTTPS configure the trust to be the same as the calling
                // package.
                if (conn instanceof HttpsURLConnection) {
                    ((HttpsURLConnection) conn).setSSLSocketFactory(appContext.getSocketFactory());
                }

                addRequestHeaders(state, conn);
                Map<String, List<String>> requestProperties = conn.getRequestProperties();

                XLConfig.LOGD(TAG, " executeDownload addRequestHeaders \nheader=" + requestProperties);
                final int code = conn.getResponseCode();
                XLConfig.LOGD(TAG, " checkFileSizeinMobile responseCode=" + code);
                if (code == HTTP_MOVED_PERM
                        || code == HTTP_MOVED_TEMP
                        || code == HTTP_SEE_OTHER
                        || code == HTTP_TEMP_REDIRECT) {
                    final String location = conn.getHeaderField("Location");
                    state.mUrl = new URL(state.mUrl, location);
                    if (code == HTTP_MOVED_PERM) {
                        // Push updated URL back to database
                        state.mRequestUri = state.mUrl.toString();
                    }
                    continue;
                }
                readResponseHeaders(state, conn);
                state.mFilename = Helpers.generateSaveFile(
                        mContext,
                        mInfo.mUri,
                        mInfo.mHint,
                        state.mContentDisposition,
                        state.mContentLocation,
                        state.mMimeType,
                        mInfo.mDestination,
                        state.mContentLength,
                        mStorageManager,
                        state.mXlTaskOpenMark == 1,
                        needFixExtension(mInfo.mPackage));
                final String transferEncoding = conn.getHeaderField("Transfer-Encoding");
                if (transferEncoding == null) {
                    // get file size form http content
                    state.mContentLength = getHeaderFieldLong(conn, "Content-Length", -1);
                    fileSize = state.mContentLength;
                    state.mTotalBytes = state.mContentLength;
                    mInfo.mTotalBytes = state.mContentLength;
                    // now we get filename, and check space.
                    mStorageManager.verifySpace(mInfo.mDestination, state.mFilename,
                            state.mTotalBytes);
                }
                // correct mimetype
                correctMimeType(state);
                // update header values into database
                updateDatabaseFromHeaders(state);
                if (headerTransferEncoding == null) {
                    break;
                } else {
                    checkPausedOrCanceled(state);
                    if (Helpers.isCmTestBuilder()) {
                        break;
                    }

                    String errorMsg = mInfo.generateReason(STATUS_NET_UNUSABLE_DUE_TO_SIZE, "No Content-Length");
                    throw new StopRequestException(Downloads.Impl.STATUS_QUEUED_FOR_WIFI,
                            errorMsg);
                }
            } catch (IOException ex) {
                if (ex instanceof SSLPeerUnverifiedException) {
                    throw new StopRequestException(
                            STATUS_BAD_REQUEST,
                            "while trying to execute request: " + ex.toString(), ex);
                } else {
                    throw new StopRequestException(STATUS_HTTP_DATA_ERROR,
                            "while trying to execute request: " + ex.toString(), ex);
                }
            } finally {
                if (conn != null) {
                    InputStream in = null;
                    try {
                        in = conn.getInputStream();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    IoUtils.closeQuietly(in);
                    conn.disconnect();
                }
            }
        } /* End of While */

        int status = Downloads.Impl.STATUS_WAITING_FOR_NETWORK;

        if (index >= Constants.MAX_REDIRECTS) {
            throw new StopRequestException(STATUS_TOO_MANY_REDIRECTS, "Too many redirects");
        }

        if (Helpers.isCmTestBuilder()) {
            return;
        }

        Long maxBytesOverMobile = mSystemFacade.getMaxBytesOverMobile();
        if (maxBytesOverMobile != null && fileSize > maxBytesOverMobile) {
            status = Downloads.Impl.STATUS_QUEUED_FOR_WIFI;
            XLConfig.LOGD("DownloadThread  checkFileSizeinMobile ---> notifyPauseDueToSize");
            String errorMsg = mInfo.generateReason(STATUS_NET_UNUSABLE_DUE_TO_SIZE,
                    "download size exceeds limit for mobile network");
            throw new StopRequestException(status, errorMsg);
        }

        if (mInfo.mBypassRecommendedSizeLimit == 0) {
            Long recommendedMaxBytesOverMobile = mSystemFacade.getRecommendedMaxBytesOverMobile();
            if (recommendedMaxBytesOverMobile != null && fileSize > recommendedMaxBytesOverMobile) {
                status = Downloads.Impl.STATUS_QUEUED_FOR_WIFI;
                String errorMsg = mInfo.generateReason(STATUS_NET_UNUSABLE_DUE_TO_SIZE,
                        "download size exceeds limit for mobile network");
                throw new StopRequestException(status, errorMsg);
            }
        }
    }

    protected void trackTaskStart(State state) {
        if (!TextUtils.isEmpty(state.mFilename)) {
            mDownloadingFile = new File(state.mFilename);
        }
        long currentBytes = (mDownloadingFile != null && mDownloadingFile.exists()) ? mDownloadingFile
                .length()
                : state.mCurrentBytes;

        mDownloadFile = state.mFilename != null ? new File(state.mFilename) : null;
        String fileName = mDownloadFile != null ? mDownloadFile.getName() : "";
        mStartSize = currentBytes > 0 ? currentBytes : 0;
        mStartTime = System.currentTimeMillis();
        Statistics.trackDownloadStart(mContext, state, false, fileName, mStartSize, wakelockTime);
    }

    protected void trackTaskStop(State state, int finalStatus, int soRet, String errorMsg) {
        long fileSize = (mDownloadingFile != null && mDownloadingFile.exists()) ? mDownloadingFile
                .length() : state.mCurrentBytes;
        String fileName = mDownloadFile != null ? mDownloadFile.getName() : "";
        long currentBytes = fileSize > state.mCurrentBytes ? fileSize : state.mCurrentBytes;
        long dSize = currentBytes - mStartSize;
        long dTime = System.currentTimeMillis() - mStartTime;
        Statistics.trackDownloadStop(mContext, state, false, fileName,
                dTime, dSize, mStartSize, mInfo.mDeleted, finalStatus, soRet, errorMsg, wakelockTime);
    }

    public void checkCleartextTrafficPermission(DownloadInfo info) throws  StopRequestException{
        if(android.os.Build.VERSION.SDK_INT < 23 || info.mDownloadType > 2){
            return;
        }
        URL url;
        try {
            // TODO: migrate URL sanity checking into client side of API
            url = new URL(info.mUri);
        } catch (Exception e) {
            url = null;
            XLConfig.LOGD("exc", e);
        }

        if (url == null) {
            return;
        }
        boolean cleartextTrafficPermitted = mSystemFacade.isCleartextTrafficPermitted(mInfo.mUid);
        boolean b = (!cleartextTrafficPermitted) && ("http".equalsIgnoreCase(url.getProtocol()));
        boolean cleartext = isCleartextTrafficPermittedFromConfig(mInfo.mUid,url.getHost());
        if (b && !cleartext) {
            throw new StopRequestException(STATUS_BAD_REQUEST,
                    "Cleartext traffic not permitted for UID " + mInfo.mUid + ": "
                            + Uri.parse(url.toString()));
        }
    }

    public String NetworkInfo2Str(NetworkInfo info) {
        if (info == null) {
            return "no info";
        }
        StringBuilder builder = new StringBuilder("NetworkInfo: ");
        builder.append("type: ").append(info.getTypeName()).append("[").append(info.getSubtypeName()).
                append("], state: ").append(info.getState()).append("/").append(info.getDetailedState()).
                append(", reason: ").append(info.getReason() == null ? "(unspecified)" : info.getReason()).
                append(", roaming: ").append(info.isRoaming()).
                append(", failover: ").append(info.isFailover()).
                append(", isAvailable: ").append(info.isAvailable());
        return builder.toString();
    }

    @Override
    public String toString() {
        StringBuilder sb=new StringBuilder();
        sb.append(String.format(">mId=%d",mInfo.mId));
        return sb.toString();
    }

    public boolean isCleartextTrafficPermittedFromConfig(int uid,String host) {
        PackageManager packageManager = mContext.getPackageManager();
        String[] packageNames = packageManager.getPackagesForUid(uid);
        if (packageNames == null || packageNames.length == 0) {
            // Unknown UID -- fail safe: cleartext traffic not permitted
            return false;
        }
        boolean cleartext = false;

        // Cleartext traffic is permitted from the UID if it's permitted for any of the packages
        // belonging to that UID.
        for (String packageName : packageNames) {
            try {
                ApplicationConfig appConfig = NetworkSecurityPolicy.getApplicationConfigForPackage(mContext, packageName);
                cleartext = appConfig.isCleartextTrafficPermitted(host);
                if (cleartext) {
                    return true;
                }
            } catch (PackageManager.NameNotFoundException e) {
                // Unknown package -- fallback to the default SSLContext
            }

        }
        return false;
    }
}
