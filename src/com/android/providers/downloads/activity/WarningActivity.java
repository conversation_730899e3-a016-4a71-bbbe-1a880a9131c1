/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.providers.downloads.activity;

import android.app.AlertDialog;
import android.content.ContentResolver;

import java.lang.reflect.Array;

import com.android.providers.downloads.config.DownloadSettings;
import com.android.providers.downloads.config.XLConfig;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.util.Helpers;

import android.app.DownloadManager;
import android.content.ContentValues;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.database.Cursor;
import android.net.Uri;
import android.provider.Downloads;
import android.text.format.Formatter;
import android.util.Log;
import android.view.View;
import android.widget.CheckBox;
import android.os.Bundle;
import com.android.providers.downloads.R;

public class WarningActivity extends android.app.Activity
        implements DialogInterface.OnCancelListener, DialogInterface.OnClickListener {
    private AlertDialog mDialog;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        showDialog();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (Helpers.isCmTestBuilder()) {
          /*  SharedPreferences sp = getSharedPreferences(XLConfig.PREF_NAME, MODE_PRIVATE);
            sp.edit().putBoolean(XLConfig.PREF_IS_WARNING_ACTIVITY_SHOWING, true).commit();*/
            DownloadSettings.XLSecureConfigSettings.saveWarnActivityShowStatus(true);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (Helpers.isCmTestBuilder()) {
           /* SharedPreferences sp = getSharedPreferences(XLConfig.PREF_NAME, MODE_PRIVATE);
            sp.edit().putBoolean(XLConfig.PREF_IS_WARNING_ACTIVITY_SHOWING, false).commit();*/
            DownloadSettings.XLSecureConfigSettings.saveWarnActivityShowStatus(false);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);

        showDialog();
    }

    private void showDialog() {

       /* SharedPreferences sharedPreferences =
                getApplicationContext().getSharedPreferences(XLConfig.PREF_NAME, MODE_PRIVATE);
        sharedPreferences
                .edit()
                .putLong(XLConfig.PREF_LAST_TIME_MOBILE_ALERT_DISPLAYED, System.currentTimeMillis())
                .commit();*/
        DownloadSettings.XLSecureConfigSettings.saveLastMobileAlertDisplayedTime(System.currentTimeMillis());

        // 先强制将状态改成等待网络连接，待交互了之后才进行相应的操作
        ContentValues values = new ContentValues();
        values.put(Downloads.Impl.COLUMN_STATUS, Downloads.Impl.STATUS_QUEUED_FOR_WIFI);

        String whereClause = "( " + getWhereClauseForStatuses(new String[] {
                "!=", "!="
        }, new String[] {
                "AND"
        }) + ")";
        String[] whereArgs = getWhereArgsForStatuses(new int[] {
                Downloads.Impl.STATUS_SUCCESS, Downloads.Impl.STATUS_PAUSED_BY_APP
        });
        int row = getContentResolver().update(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, values,
                whereClause, whereArgs);
        // 不存在符合条件的记录，那么直接返回，
        if (row <= 0) {
            XLConfig.LOGD("WarningActivity#showDialog row=" + row + ", return directly");
            finish();
            return;
        }

        if (mDialog == null) {
            CheckBox checkBox = (CheckBox) getLayoutInflater().inflate(
                    R.layout.check, null);
            checkBox.setChecked(true);
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle(R.string.warning_tip_title)
                    .setView(checkBox)
                    .setMessage(R.string.warning_tip_content)
                    .setPositiveButton(R.string.warning_tip_ok, this)
                    .setNegativeButton(R.string.warning_tip_cancel, this);
            mDialog = builder.create();
            mDialog.setOnCancelListener(this);
        }
        mDialog.show();
    }

    @Override
    public void onCancel(DialogInterface dialog) {
        dialogClosed();
    }

    private void dialogClosed() {
        if (mDialog != null) {
            mDialog.dismiss();
            mDialog = null;
        }
        finish();
    }

    @Override
    public void onClick(DialogInterface dialog, int which) {
        if (which == AlertDialog.BUTTON_NEGATIVE) {
            ContentValues values = new ContentValues();
            DownloadManager.addRunningStatusAndControlRun(values);
            values.put(Downloads.Impl.COLUMN_BYPASS_RECOMMENDED_SIZE_LIMIT, true);
            values.put(Downloads.Impl.COLUMN_ALLOWED_NETWORK_TYPES, -1);
            values.put(Downloads.Impl.COLUMN_ALLOW_METERED, true);

            String whereClause = "( " + getWhereClauseForStatuses(new String[] {
                    "!=", "!="
            }, new String[] {
                    "AND"
            }) + ")";
            String[] whereArgs = getWhereArgsForStatuses(new int[] {
                    Downloads.Impl.STATUS_SUCCESS, Downloads.Impl.STATUS_PAUSED_BY_APP
            });
            getContentResolver().update(Downloads.Impl.ALL_DOWNLOADS_CONTENT_URI, values,
                    whereClause, whereArgs);
        }
        CheckBox checkView = (CheckBox) mDialog.findViewById(R.id.checkbox);
        setWarningNotShowAgain(this, checkView.isChecked());
        dialogClosed();
    }

    private void setWarningNotShowAgain(Context context, boolean checked) {
        /*if (context == null) {
            return;
        }
        XLConfig.LOGD("WarningActivity#showDialog not show again=" + checked);
        SharedPreferences sp = context.getSharedPreferences(XLConfig.PREF_NAME,
                MODE_PRIVATE);
        Editor edit = sp.edit();
        edit.putBoolean(XLConfig.DIALOG_WARNING_NOT_SHOW_AGAIN, checked);
        edit.commit();*/
        DownloadSettings.XLSecureConfigSettings.saveWarningActivityShowFlag(checked);
    }

    public static boolean IsWarningNotShowAgain(Context context) {
        /*if (context == null) {
            return false;
        }

        SharedPreferences sp = context.getSharedPreferences(XLConfig.PREF_NAME,
                MODE_PRIVATE);
        boolean show = sp.getBoolean(XLConfig.DIALOG_WARNING_NOT_SHOW_AGAIN, false);
        XLConfig.LOGD("WarningActivity#showDialog not show again=" + show);
        return show;*/
        return DownloadSettings.XLSecureConfigSettings.getWarningActivityShowFlag(false);
    }

    static String getWhereClauseForIds(long[] ids) {
        StringBuilder whereClause = new StringBuilder();
        whereClause.append("(");
        for (int i = 0; i < ids.length; i++) {
            if (i > 0) {
                whereClause.append("OR ");
            }
            whereClause.append(Downloads.Impl._ID);
            whereClause.append(" = ? ");
        }
        whereClause.append(")");
        return whereClause.toString();
    }

    static String[] getWhereArgsForIds(long[] ids) {
        String[] whereArgs = new String[ids.length];
        for (int i = 0; i < ids.length; i++) {
            whereArgs[i] = Long.toString(ids[i]);
        }
        return whereArgs;
    }

    static String getWhereClauseForStatuses(String[] operators, String[] jointConditions) {
        StringBuilder whereClause = new StringBuilder();
        whereClause.append("(");
        for (int i = 0; i < operators.length; i++) {
            if (i > 0) {
                whereClause.append(jointConditions[i - 1] + " ");
            }
            whereClause.append(Downloads.Impl.COLUMN_STATUS);
            whereClause.append(" " + operators[i] + " ? ");
        }
        whereClause.append(")");
        return whereClause.toString();
    }

    static String[] getWhereArgsForStatuses(int[] statuses) {
        String[] whereArgs = new String[statuses.length];
        for (int i = 0; i < statuses.length; i++) {
            whereArgs[i] = Integer.toString(statuses[i]);
        }
        return whereArgs;
    }

    static <T> T[] concatArrays(T[] src1, T[] src2, Class<T> type) {
        T dst[] = (T[]) Array.newInstance(type, src1.length + src2.length);
        System.arraycopy(src1, 0, dst, 0, src1.length);
        System.arraycopy(src2, 0, dst, src1.length, src2.length);
        return dst;
    }
}
