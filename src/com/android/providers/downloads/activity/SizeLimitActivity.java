/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.providers.downloads.activity;

import static com.android.providers.downloads.util.Constant.RECEIVER_EXPORTED;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.format.Formatter;
import android.text.style.TextAppearanceSpan;
import android.text.style.UnderlineSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;

import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.android.providers.downloads.R;
import com.android.providers.downloads.config.MobileDataConfig;
import com.android.providers.downloads.provider.DownLoadProviderUtils;
import com.android.providers.downloads.service.DownloadInfo;
import com.android.providers.downloads.statistics.Statistics;
import com.android.providers.downloads.util.DataUsageHelper;
import com.android.providers.downloads.util.MergeUtils;
import com.android.providers.downloads.util.Util;
import com.android.providers.downloads.utils.LogUtil;

import java.lang.ref.WeakReference;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import miuix.appcompat.app.AlertDialog;
import miuix.pickerwidget.widget.NumberPicker;


/**
 * Activity to show dialogs to the user when a download exceeds a limit on
 * download sizes for mobile networks. This activity gets started by the
 * background download service when a download's size is discovered to be
 * exceeded one of these thresholds.
 */
public class SizeLimitActivity extends Activity implements DialogInterface.OnCancelListener,
        DialogInterface.OnClickListener, AnalysisCallback {
    private static final int MSG_REFRESH_UI = 0;
    private static final int MSG_SHOW = 1;

    private Handler mHandler;

    private List<MergeIntent> mMergeQueue;
    private HashMap<String, MergeIntent> mMergeQueueIndex;
    private MergeUtils mMergeUtils;
    private MergeIntent mCurrentIntent;
    private BroadcastReceiver mNetworkChange;
    private String mDialogId;

    private static final long BASE_B = 1L;
    private static final long BASE_KB = 1024;
    private static final long BASE_MB = 1024 * BASE_KB;
    private static final long BASE_GB = 1024 * BASE_MB;
    private static final long BASE_TB = 1024 * BASE_GB;
    private static final String UNIT_BIT = "B";
    private static final String UNIT_KB = "KB";
    private static final String UNIT_MB = "MB";
    private static final String UNIT_GB = "GB";
    private static final String UNIT_TB = "TB";
    private static final String UNIT_PB = "PB";

    private final static String TAG_DATA_LIMIT = "DATA_LIMIT";
    private static String[] mDataSelKey = new String[]{"无限制", "100M", "40M", "20M", "5M", "2M", "1M", "0M"};
    private static long[] mDataSelValue = new long[]{Long.MAX_VALUE, 100 * BASE_MB, 40 * BASE_MB, 20 * BASE_MB,
            5 * BASE_MB, 2 * BASE_MB, 1 * BASE_MB, 0};
    private long mDataLimit;
    private int mSelIndex = -1;
    private int mPickerNewValue = -1;
    private NumberPicker mNumberPicker;

    private void clearDialogData() {
        mCurrentIntent = null;
        mMergeUtils.clear();
        mMergeQueue.clear();
        if (mDataLimitSelDialog != null && mDataLimitSelDialog.isShowing()) {
            mDataLimitSelDialog.dismiss();
            mDataLimitSelDialog = null;
        }
        if (mDataLimitDialog != null && mDataLimitDialog.isShowing()) {
            mDataLimitDialog.dismiss();
            mDataLimitDialog = null;
        }
    }

    private SpannableString mTextMsg3;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mMergeQueue = new ArrayList<MergeIntent>();
        mHandler = new ActionHandler();
        mMergeQueueIndex = new HashMap<String, MergeIntent>();
        mMergeUtils = new MergeUtils(this, this);
        registerReceiver();
        onNewIntent(getIntent());
        String msg3Text = getString(R.string.dialog_data_limit_msg3);
        mTextMsg3 = new SpannableString(msg3Text);
        mTextMsg3.setSpan(new UnderlineSpan(), 0, msg3Text.length(), Spanned.SPAN_INCLUSIVE_INCLUSIVE);
    }

    private void registerReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        if (mNetworkChange == null) {
            mNetworkChange = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    String action = intent.getAction();
                    if (TextUtils.isEmpty(action)) {
                        return;
                    }
                    if (action.equals(ConnectivityManager.CONNECTIVITY_ACTION)) {
                        if (!Statistics.isMobileActive(context)) {
                            consumeDialog();
                        }
                    }
                }
            };
        }
        registerReceiver(mNetworkChange, filter,RECEIVER_EXPORTED);
    }

    private void consumeDialog() {
        clearDialogData();
        finish();
    }


    private void unregisterReceiver() {
        if (mNetworkChange != null) {
            unregisterReceiver(mNetworkChange);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        enqueue(intent);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unregisterReceiver();
    }

    private void enqueue(Intent intent) {
        if (intent != null) {
            mMergeUtils.enqueue(intent);
            setIntent(null);
        }
    }

    @Override
    public void onCancel(DialogInterface dialog) {
        dialogClosed();
    }

    private void dialogClosed() {
        Statistics.logD(TAG_DATA_LIMIT, "dialogClosed", mCurrentIntent);
        mDataLimitDialog = null;
        mCurrentIntent = null;
        mHandler.sendEmptyMessage(MSG_SHOW);
    }

    @Override
    public void onClick(DialogInterface dialog, int which) {
        if (mCurrentIntent != null && Statistics.isMobileActive(this)) {
            boolean isRequired = mCurrentIntent.isWifiRequired();
            if (isRequired) {
                if (which == AlertDialog.BUTTON_NEGATIVE) {
                    DownLoadProviderUtils.delete(this, mCurrentIntent.getIds());
                }
            } else {
                if (which == AlertDialog.BUTTON_POSITIVE) {
                    DownLoadProviderUtils.passRecommendedSizeLimit(this, mCurrentIntent.getIds());
                    Statistics.reportDataLimitDialogClick(this, mDialogId, mCurrentIntent.getSize(), 2);
                    Statistics.reportClickMobilePop(this, Statistics.MOBILEPOP_CLICKID.download_now, getPkgName());
                } else if (which == AlertDialog.BUTTON_NEGATIVE) {
                    Statistics.reportDataLimitDialogClick(this, mDialogId, mCurrentIntent.getSize(), 1);
                    Statistics.reportClickMobilePop(this, Statistics.MOBILEPOP_CLICKID.wait_wifi, getPkgName());
                    dialogClosed();
                }
            }
        }

        dialogClosed();
    }

    private String getPkgName() {
        String pkg = "";
        if (mCurrentIntent != null) {
            pkg = mCurrentIntent.getPkgName();
        }
        return pkg;
    }

    @Override
    public void AnalysisComplete(Intent intent, long id, String pkgName, long size) {
        Statistics.logD(TAG_DATA_LIMIT, "AnalysisComplete", intent, id, pkgName, size);
        if (TextUtils.isEmpty(pkgName)) {
            return;
        }

        if (mCurrentIntent != null && TextUtils.equals(pkgName, getPkgName())
                && mCurrentIntent.getSize() >= 0 && size >= 0) {
            mCurrentIntent.addMember(id, size);
            mHandler.sendEmptyMessage(MSG_REFRESH_UI);
        } else {
            addMember(id, pkgName, isWifiRequired(intent), size);
            mHandler.sendEmptyMessage(MSG_SHOW);
        }
    }

    private boolean isWifiRequired(Intent intent) {
        Bundle extras = intent.getExtras();
        return (extras != null) && extras.getBoolean(DownloadInfo.EXTRA_IS_WIFI_REQUIRED);
    }

    private void addMember(long id, String pkgName, boolean isWifiRequest, long size) {
        MergeIntent mergeIntent;
        String key = getKey(pkgName, isWifiRequest);
        if (size >= 0) {
            if (mMergeQueueIndex.containsKey(key)) {
                mergeIntent = mMergeQueueIndex.get(key);
                if (mergeIntent != null) {
                    mergeIntent.addMember(id, size);
                }
            } else {
                mergeIntent = new MergeIntent(id, pkgName, isWifiRequest, size);
                mMergeQueue.add(mergeIntent);
                mMergeQueueIndex.put(key, mergeIntent);
            }
            Collections.sort(mMergeQueue);
        } else if (size == -1) {
            mergeIntent = new MergeIntent(id, pkgName, isWifiRequest, size);
            if (!mMergeQueue.contains(mergeIntent)) {
                mMergeQueue.add(mergeIntent);
            }
        }

    }

    private String getKey(String pkgName, boolean isWifiRequest) {
        return pkgName + isWifiRequest;
    }

    private class ActionHandler extends Handler {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_REFRESH_UI:
                    refreshUI(mCurrentIntent);
                    break;
                case MSG_SHOW:
                    showNextDialog();
                    break;

                default:
                    break;
            }
        }

        ;
    }

    ;

    private void refreshUI(MergeIntent intent) {
        if (mDataLimitDialog == null) {
            return;
        }

        Statistics.logD(TAG_DATA_LIMIT, "refreshUI", intent);
        long size = intent.getSize();
        TextView msg1View = (TextView) mDataLimitDialog.findViewById(R.id.dialog_msg1);
        msg1View.setText(getMsgText(size));
    }

    private synchronized void showNextDialog() {
        if (mDataLimitDialog != null && mDataLimitDialog.isShowing()) {
            return;
        }

        boolean hasData = hasData();
        if (!hasData) {
            finish();
            return;
        }

        mCurrentIntent = poll();
        showDataLimitDialog(mCurrentIntent);
    }

    WeakReference<Activity> reference = new WeakReference<Activity>(this);
    private AlertDialog mDataLimitDialog;
    private AlertDialog mDataLimitSelDialog;


    private void showDataLimitDialog(MergeIntent intent) {
        if (intent == null) {
            return;
        }

        long size = intent.getSize();
        boolean required = intent.isWifiRequired();
        if (required && size < 0) {
            return;
        }

        Statistics.logD(TAG_DATA_LIMIT, "showDialog", intent);
        Activity activity = reference.get();
        if (activity == null || activity.isFinishing()) {
            return;
        }

        loadDataLimit();

        LayoutInflater inflater = LayoutInflater.from(this);
        View msgView = inflater.inflate(R.layout.dialog_msg_data_limit, null);
        TextView msg1View = (TextView) msgView.findViewById(R.id.dialog_msg1);
        msg1View.setText(getMsgText(size));
        TextView msg2View = (TextView) msgView.findViewById(R.id.dialog_msg2);
        msg2View.setText(buildDataLimitTip(mDataLimit));
        TextView msg3View = (TextView) msgView.findViewById(R.id.dialog_msg3);
        msg3View.setOnClickListener(mDialogMsgClickListener);
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        if (required) {
            String sizeString = size > 0 ? Formatter.formatFileSize(this, size) : "";
            String queueText = getString(R.string.button_queue_for_wifi);
            builder.setTitle(R.string.wifi_required_title)
                    .setMessage(getString(R.string.wifi_required_body, sizeString, queueText))
                    .setPositiveButton(R.string.button_queue_for_wifi, this)
                    .setNegativeButton(R.string.button_cancel_download, this);
        } else {
            builder.setTitle(R.string.dialog_data_limit_title)
                    .setView(msgView)
                    .setPositiveButton(R.string.dialog_data_limit_button2, mDialogClickListener)
                    .setNegativeButton(R.string.dialog_data_limit_button1, mDialogClickListener);
        }
        builder.setOnCancelListener(this);
        builder.setCancelable(true);

        mDataLimitDialog = builder.create();
        Window window = mDataLimitDialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = Util.getScreenWeight(getApplication()); //设置宽度
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
            window.setAttributes(lp);
        }
        mDataLimitDialog.setCanceledOnTouchOutside(false);
        mDataLimitDialog.show();
        mDialogId = Statistics.getMd5Digest(String.valueOf(System.currentTimeMillis()));
        Statistics.reportDataLimitDialogShow(this, mDialogId, size);
        Statistics.reportClickMobilePopShow(this,size,mDataLimit, getPkgName());
    }

    private String getMsgText(long fileSize) {
        String msgTxt;
        if (fileSize >= 0) {
            msgTxt = getString(R.string.dialog_data_limit_msg1,
                    convertFileSize(fileSize, 1));
        } else {
            msgTxt = getString(R.string.recommended_no_file_size_download_in_mobile);
        }
        return msgTxt;
    }

    public boolean hasData() {
        return mMergeQueue.size() > 0 || mMergeUtils.hasData();
    }

    public MergeIntent poll() {
        if (mMergeQueue.size() < 1) {
            return null;
        }

        MergeIntent intent = mMergeQueue.remove(0);
        mMergeQueueIndex.remove(getKey(intent.getPkgName(), intent.isWifiRequired()));
        return intent;
    }

    public static String convertFileSize(long file_size, int precision) {
        long int_part = 0;
        double fileSize = file_size;
        double floatSize = 0L;
        long temp = file_size;
        int i = 0;
        long base = 1L;
        String baseUnit = "";
        String fileSizeStr = null;
        int indexMid = 0;

        while (temp / 1024 > 0) {
            int_part = temp / 1024;
            temp = int_part;
            i++;
            if (i == 4) {
                break;
            }
        }

        switch (i) {
            case 0: // B
                base = BASE_B;
                baseUnit = UNIT_BIT;
                break;

            case 1: // KB
                base = BASE_KB;
                baseUnit = UNIT_KB;
                break;

            case 2: // MB
                base = BASE_MB;
                baseUnit = UNIT_MB;
                break;

            case 3: // GB
                base = BASE_GB;
                baseUnit = UNIT_GB;
                break;

            case 4: // TB
                base = BASE_TB;
                baseUnit = UNIT_TB;
                break;
            default:
                break;
        }
        BigDecimal filesizeDecimal = new BigDecimal(fileSize);
        BigDecimal baseDecimal = new BigDecimal(base);
        floatSize = filesizeDecimal.divide(baseDecimal, precision, BigDecimal.ROUND_HALF_UP)
                .doubleValue();
        fileSizeStr = Double.toString(floatSize);
        if (precision == 0) {
            indexMid = fileSizeStr.indexOf('.');
            if (-1 == indexMid) {
                return fileSizeStr + baseUnit;
            }
            return fileSizeStr.substring(0, indexMid) + baseUnit;
        }

        // baseUnit = UNIT_BIT;
        if (baseUnit.equals(UNIT_BIT)) {
            int pos = fileSizeStr.indexOf('.');
            fileSizeStr = fileSizeStr.substring(0, pos);
        }

        if (baseUnit.equals(UNIT_KB)) {
            int pos = fileSizeStr.indexOf('.');
            if (pos != -1) {
                fileSizeStr = fileSizeStr.substring(0, pos + 2);
            } else {
                fileSizeStr = fileSizeStr + ".0";
            }
        }

        return fileSizeStr + baseUnit;
    }


    private View.OnClickListener mDialogMsgClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            switch (v.getId()) {
                case R.id.dialog_msg3:
                    showDataLimitSelDialog();
                    Statistics.reportClickMobilePop(SizeLimitActivity.this, Statistics.MOBILEPOP_CLICKID.change_setting, getPkgName());
                    break;

            }
        }
    };

    private void showDataLimitSelDialog() {
        mDataSelKey = new String[mDataSelValue.length];
        for (int i = 0; i < mDataSelValue.length; i++) {
            if (mDataSelValue[i] == mDataLimit) {
                mSelIndex = i;
            }
            mDataSelKey[i] = buildDataLimitContent(mDataSelValue[i]);
        }

        LogUtil.logD(TAG_DATA_LIMIT, dumpDataLimitSel());
        mNumberPicker = new NumberPicker(this);
        mNumberPicker.setDisplayedValues(mDataSelKey);
        mNumberPicker.setPadding(1, 0, 1, 0);
        mNumberPicker.setMinValue(0);
        mNumberPicker.setMaxValue(mDataSelKey.length - 1);
        mNumberPicker.setWrapSelectorWheel(false);
        mNumberPicker.setValue(mSelIndex);
        mNumberPicker.setOnValueChangedListener(new NumberPicker.OnValueChangeListener() {
            @Override
            public void onValueChange(NumberPicker picker, int oldVal, int newVal) {
                mDataLimitSelDialog.setTitle(buildDataLimitTip(mDataSelValue[newVal]));
                mPickerNewValue = newVal;
            }
        });
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(buildDataLimitTip(mDataLimit))
                .setView(mNumberPicker)
                .setPositiveButton(R.string.dialog_button_ok, mDialogDataSelListener)
                .setNegativeButton(R.string.dialog_button_cancel, mDialogDataSelListener);
        mDataLimitSelDialog = builder.create();
        mDataLimitSelDialog.show();
    }

    private String buildDataLimitContent(long value) {
        String valueKey;
        if (value == Long.MAX_VALUE) {
            valueKey = getString(R.string.dialog_content_limit_max);

        } else if (value == 0) {
            valueKey = getString(R.string.dialog_content_limit0);
        } else {
            float result = value;
            String suffix = UNIT_BIT;
            if (result >= 1024) {
                suffix = UNIT_KB;
                result = result / 1024;
            }
            if (result >= 1024) {
                suffix = UNIT_MB;
                result = result / 1024;
            }
            if (result >= 1024) {
                suffix = UNIT_GB;
                result = result / 1024;
            }
            if (result >= 1024) {
                suffix = UNIT_TB;
                result = result / 1024;
            }
            if (result >= 1024) {
                suffix = UNIT_PB;
                result = result / 1024;
            }
            valueKey = String.format("%.0f" + suffix, result);
        }
        return valueKey;
    }

    private void loadDataLimit() {
        LogUtil.logD(TAG_DATA_LIMIT, Arrays.toString(mDataSelValue));
        mDataSelValue = DataUsageHelper.getFlowLimitLevel();
        mDataLimit = DataUsageHelper.getFlowLimit();
    }

    private SpannableString buildDataLimitTip(long limitValue) {
        SpannableString selTip;
        if (limitValue == 0) {
            selTip = SpannableString.valueOf(getString(R.string.dialog_tip_limit0));
        } else if (MobileDataConfig.isUnlimited(limitValue)) {
            selTip = SpannableString.valueOf(getString(R.string.dialog_tip_no_limit));
        } else {
            String limitStr = buildDataLimitContent(limitValue);
            String tip = String.format(getString(R.string.dialog_tip_limit), limitStr);
            selTip = new SpannableString(tip);
        }
        return selTip;
    }


    DialogInterface.OnClickListener mDialogDataSelListener = new DialogInterface.OnClickListener() {
        @Override
        public void onClick(DialogInterface dialog, int which) {
            switch (which) {
                case DialogInterface.BUTTON_POSITIVE:
                    dialogDataSelPositiveClick();
                    break;
                case DialogInterface.BUTTON_NEGATIVE:
                    dialogDataSelNegativeClick();
                    break;
            }

        }
    };


    private void dialogDataSelPositiveClick() {
        if (mDataLimitDialog != null && mPickerNewValue >= 0) {
            TextView msg2View = (TextView) mDataLimitDialog.findViewById(R.id.dialog_msg2);
            mDataLimit = mDataSelValue[mPickerNewValue];
            mSelIndex = mPickerNewValue;
            msg2View.setText(buildDataLimitTip(mDataLimit));
        }
        Statistics.reportClickMobilePopChangeSetting(this, Statistics.MOBILEPOP_CHANGESETTING_CLICKID.ok,mDataLimit, getPkgName());
        LogUtil.logD(TAG_DATA_LIMIT, "dialogDataSelPositiveClick " + dumpDataLimitSel());
    }

    @Override
    protected void onStop() {
        super.onStop();
        consumeDialog();
    }

    private String dumpDataLimitSel() {

        if (mDataSelKey == null || mDataSelValue == null) {
            return "DataSelKey/DataSelValue is null";
        }
        if (mDataSelKey.length != mDataSelValue.length) {
            return "DataSelKey DataSelValue lengths must be the same";
        }
        if (mSelIndex < 0 || mSelIndex > mDataSelValue.length - 1) {
            return String.format("number pick index must be between [0,%d]", mDataSelValue.length);
        }
        return String.format("dumpDataLimitSel mSelIndex=%d,key=%s,value=%s", mSelIndex, mDataSelKey[mSelIndex], mDataSelValue[mSelIndex]);
    }

    private void dialogDataSelNegativeClick() {
        mPickerNewValue = -1;
        Statistics.reportClickMobilePopChangeSetting(this, Statistics.MOBILEPOP_CHANGESETTING_CLICKID.cancel,mDataLimit, getPkgName());
    }

    private DialogInterface.OnClickListener mDialogClickListener = new DialogInterface.OnClickListener() {
        @Override
        public void onClick(DialogInterface dialog, int which) {
            switch (which) {
                case DialogInterface.BUTTON_POSITIVE:
                    dialogPositiveClick();
                    break;
                case DialogInterface.BUTTON_NEGATIVE:
                    dialogNegativeClick();
                    break;
            }
            dialogClosed();
        }
    };

    private void dialogNegativeClick() {
        if (mCurrentIntent != null && Statistics.isMobileActive(this)) {
            boolean isRequired = mCurrentIntent.isWifiRequired();
            if (isRequired) {
                DownLoadProviderUtils.delete(this, mCurrentIntent.getIds());
            } else {
                Statistics.reportDataLimitDialogClick(this, mDialogId, mCurrentIntent.getSize(), 1);
                Statistics.reportClickMobilePop(this, Statistics.MOBILEPOP_CLICKID.wait_wifi, getPkgName());
            }
        }
    }

    private void dialogPositiveClick() {
        if (mCurrentIntent != null && Statistics.isMobileActive(this)) {
            DownLoadProviderUtils.passRecommendedSizeLimit(this, mCurrentIntent.getIds());
            Statistics.reportDataLimitDialogClick(this, mDialogId, mCurrentIntent.getSize(), 2);
            Statistics.reportClickMobilePop(this, Statistics.MOBILEPOP_CLICKID.download_now, getPkgName());
        }
        if (mSelIndex >= 0 && mSelIndex <= mDataSelValue.length - 1) {
            DataUsageHelper.setFlowLimit(mDataSelValue[mSelIndex]);
        }
    }
}
