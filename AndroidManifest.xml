<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.android.providers.downloads"
    android:sharedUserId="android.media"
    android:versionCode="**********"
    android:versionName="136.25.06.800020">
    <uses-sdk
        android:minSdkVersion="26"
        android:targetSdkVersion="34" />

    <!-- Allows access to the Download Manager -->
    <permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER"
                android:description="@string/permdesc_downloadManager"
                android:label="@string/permlab_downloadManager"
                android:protectionLevel="signatureOrSystem"/>

    <!-- Allows advanced access to the Download Manager -->
    <permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER_ADVANCED"
                android:description="@string/permdesc_downloadManagerAdvanced"
                android:label="@string/permlab_downloadManagerAdvanced"
                android:protectionLevel="signatureOrSystem"/>

    <!-- Allows to send download completed intents -->
    <permission android:name="android.permission.SEND_DOWNLOAD_COMPLETED_INTENTS"
                android:description="@string/permdesc_downloadCompletedIntent"
                android:label="@string/permlab_downloadCompletedIntent"
                android:protectionLevel="signature"/>

    <!-- Allows to download non-purgeable files to the cache partition through the public API -->
    <permission android:name="android.permission.DOWNLOAD_CACHE_NON_PURGEABLE"
                android:description="@string/permdesc_downloadCacheNonPurgeable"
                android:label="@string/permlab_downloadCacheNonPurgeable"
                android:protectionLevel="signatureOrSystem"/>

    <!-- Allows to queue downloads without a notification shown while the download runs. -->
    <permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION"
                android:description="@string/permdesc_downloadWithoutNotification"
                android:label="@string/permlab_downloadWithoutNotification"
                android:protectionLevel="normal"/>

    <!-- Allows an app to access all downloads in the system via the /all_downloads/ URIs.  The
         protection level could be relaxed in the future to support third-party download
         managers. -->
    <permission android:name="android.permission.ACCESS_ALL_DOWNLOADS"
                android:description="@string/permdesc_accessAllDownloads"
                android:label="@string/permlab_accessAllDownloads"
                android:protectionLevel="signature"/>
    <permission android:name="com.android.providers.downloads.permission.MIPUSH_RECEIVE"/>
    <permission android:name="android.permission.XL_DOWNLOAD_INSTALL"
                android:protectionLevel="signature"/>
    <permission android:name="android.permission.XL_SILENCE_INSTALL"
                android:protectionLevel="signatureOrSystem"/>
    <permission android:name="xunlei.permission.EXTRA_LIMIT_SPEED"
                android:protectionLevel="signatureOrSystem"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <permission android:name="xunlei.permission.DOWNLOADS"
        android:protectionLevel="signature"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER"/>
    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM"/>
    <uses-permission android:name="android.permission.SEND_DOWNLOAD_COMPLETED_INTENTS"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.ACCESS_ALL_DOWNLOADS"/>
    <uses-permission android:name="android.permission.UPDATE_DEVICE_STATS"/>
    <uses-permission android:name="android.permission.LOCAL_MAC_ADDRESS"/>
    <!-- TODO: replace with READ_NETWORK_POLICY permission when it exists -->
    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL"/>

    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.GET_ACCOUNTS"/>
    <uses-permission android:name="com.android.providers.downloads.permission.MIPUSH_RECEIVE"/>
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.GET_TASKS"/>
    <uses-permission android:name="com.xiaomi.permission.AUTH_SERVICE"/>
    <uses-permission android:name="com.xiaomi.permission.CLOUD_MANAGER"/>
    <uses-permission android:name="miui.permission.USE_INTERNAL_GENERAL_API"/>
    <uses-permission android:name="miui.permission.EXTRA_NETWORK"/>
    <uses-permission android:name="android.permission.DUMP"/>
    <uses-permission android:name="com.miui.whetstone.permission.ACCESS_PROVIDER" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
    <uses-permission android:name="android.permission.UPDATE_APP_OPS_STATS" />
    <uses-permission android:name="android.permission.START_ACTIVITIES_FROM_BACKGROUND"/>
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CONNECTIVITY_USE_RESTRICTED_NETWORKS"/>
    <uses-permission android:name="com.android.settings.permission.CLOUD_SETTINGS_PROVIDER" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <application android:name="com.android.providers.downloads.DownloadApplication"
                 android:allowBackup="false"
                 android:icon="@mipmap/ic_launcher_download"
                 android:label="@string/app_label"
                 android:supportsRtl="true"
                 android:usesCleartextTraffic="true"
                 android:requestLegacyExternalStorage="true"
                 android:process="android.process.media">
        <meta-data
            android:name="os_private_label"
            android:value="PRIVATE_OS030"
            />
        <property
            android:name="android.view.PROPERTY_MIUI_SMOOTH_CORNER_ENABLED"
            android:value="true" />
        <uses-library android:name="com.miui.core" android:required="false"/>
        <uses-library android:name="com.miui.system" android:required="false" />
        <!-- miui zero copy -->
        <uses-library android:name="com.xiaomi.okhttp" android:required="false" />
        <provider android:name=".provider.DownloadProvider"
                  android:authorities="downloads" android:exported="true">
            <!-- Anyone can access /my_downloads, the provider internally restricts access by UID for
                 these URIs -->
            <path-permission android:pathPrefix="/my_downloads"
                             android:permission="android.permission.INTERNET"/>
            <!-- to access /all_downloads, ACCESS_ALL_DOWNLOADS permission is required -->
            <path-permission android:pathPrefix="/all_downloads"
                             android:permission="android.permission.ACCESS_ALL_DOWNLOADS"/>
            <path-permission android:pathPrefix="/all_downloads_download_bt_detail"
                             android:permission="android.permission.ACCESS_ALL_DOWNLOADS"/>
            <!-- Temporary, for backwards compatibility -->
            <path-permission android:pathPrefix="/download"
                             android:permission="android.permission.INTERNET"/>
            <!-- Apps with access to /all_downloads/... can grant permissions, allowing them to share
                 downloaded files with other viewers -->
            <grant-uri-permission android:pathPrefix="/all_downloads/"/>
            <!-- Apps with access to /my_downloads/... can grant permissions, allowing them to share
                 downloaded files with other viewers -->
            <grant-uri-permission android:pathPrefix="/my_downloads/"/>
            <grant-uri-permission android:pathPrefix="/all_downloads"/>
            <grant-uri-permission android:pathPrefix="/all_downloads_download_bt_detail"/>
            <!-- to set download size limit over mobile -->
            <path-permission android:pathSuffix="/xl_download_bytes_limit_over_mobile"
                             android:permission="android.permission.ACCESS_ALL_DOWNLOADS"/>
        </provider>

        <provider
            android:name=".provider.DownloadStorageProvider"
            android:authorities="com.android.providers.downloads.documents"
            android:grantUriPermissions="true"
            android:exported="true"
            android:permission="android.permission.MANAGE_DOCUMENTS">
            <intent-filter>
                <action android:name="android.content.action.DOCUMENTS_PROVIDER"/>
            </intent-filter>
        </provider>

        <service android:name=".service.DownloadService"
                 android:permission="android.permission.ACCESS_DOWNLOAD_MANAGER"/>
        <receiver android:name=".receiver.DownloadReceiver" android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="com.android.providers.downloads.PRIVACY_ACCEPT"/>
                <action android:name="com.android.providers.downloads.xlspeedup.GSSY_EXPERIENCE"/>
                <action android:name="com.android.providers.downloads.NOTIFY_RECOMMEND"/>
		        <action android:name="android.intent.action.UID_REMOVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_MOUNTED"/>
                <data android:scheme="file"/>
            </intent-filter>
        </receiver>
        <receiver
            android:name=".receiver.DownloadNotificationReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="miui.intent.action.DOWNLOAD_OPEN" />
                <action android:name="miui.intent.action.DOWNLOAD_LIST" />
                <action android:name="miui.intent.action.DOWNLOAD_HIDE" />
                <data android:scheme="active-dl" />
            </intent-filter>
        </receiver>
        <receiver android:name=".receiver.DownloadXunleiTokenReceiver" android:exported="true">
            <intent-filter>
                <action android:name="com.process.media.broadcast.downloadlist"/>
                <action android:name="com.process.media.broadcast.falsetoken"/>
            </intent-filter>
        </receiver>
        <receiver android:name=".receiver.DownloadMiAccountReceiver" android:exported="true">
            <intent-filter>
                <action android:name="android.accounts.LOGIN_ACCOUNTS_POST_CHANGED"/>
            </intent-filter>
        </receiver>

        <service
            android:name=".DownloadIdleService"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <service
                android:name=".MediaScanTriggerJob"
                android:exported="true"
                android:permission="android.permission.BIND_JOB_SERVICE" />

        <receiver android:name=".receiver.LocaleChangedListener"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.LOCALE_CHANGED"/>
            </intent-filter>
        </receiver>

        <receiver android:name=".receiver.DownloadUpdateReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="miui.intent.action.DOWNLOAD_UPDATE_PROGRESS_REGISTRATION"/>
            </intent-filter>
        </receiver>

        <receiver android:name=".receiver.ApplicationInstallReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_ADDED"/>
                <action android:name="android.intent.action.PACKAGE_REMOVED"/>
                <data android:scheme="package"/>
            </intent-filter>
        </receiver>

  <!--     <receiver android:name=".receiver.MiuiHomeReceiver">
            <intent-filter>
                <action android:name="com.miui.home.action.on_click"/>
                <action android:name="com.miui.home.action.on_delete"/>
            </intent-filter>
        </receiver>-->

        <activity android:name=".activity.SizeLimitActivity"
                  android:excludeFromRecents="true"
                  android:exported="true"
                   android:taskAffinity=""
                  android:launchMode="singleTask"
                  android:theme="@style/ThemeDayNightNoTitleDialogActivity"/>
        <activity android:name=".activity.WarningActivity"
                  android:excludeFromRecents="true"
                  android:exported="true"
                  android:launchMode="singleTask"
                  android:theme="@style/ThemeLightNoTitileTransparent"/>
        <service
                android:name=".remote.service.DebugLogService" android:exported="true"
                android:permission="xunlei.permission.DOWNLOADS">
            <intent-filter>
                <action android:name="com.android.providers.downloads.remote.service.IDebugLogService"/>
            </intent-filter>
        </service>

        <service
            android:name=".remote.service.DownloadSettingsProviderService" android:exported="true">
            <intent-filter>
                <action android:name="com.android.providers.downloads.settings.service"/>
            </intent-filter>
        </service>
	<service
            android:name=".remote.service.LimitSpeedService" android:exported="true">
            <intent-filter>
                <action android:name="com.android.providers.downloads.XL_LIMIT_SPEED"/>
            </intent-filter>
        </service>

        <service
            android:name=".remote.service.XLDownloadService" android:exported="true"
            android:permission="xunlei.permission.EXTRA_LIMIT_SPEED">
            <intent-filter>
                <action android:name="com.android.providers.downloads.XL_DOWNLOAD"/>
            </intent-filter>
        </service>
        <activity
            android:name=".ui.ui.DownloadTasksActivity"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout"
            android:theme="@style/ThemeXDayNight"
            android:label="@string/download"
            android:screenOrientation="behind"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW_DOWNLOADS" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="miui.intent.action.VIEW_RANK" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="miui.intent.action.NOTIFY_RECOMMEND" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="miui.intent.action.GOTO_SEARCH_HOME" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="miui.intent.action.GOTO_NEW_DOWNLOAD" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="miui.intent.action.globalsearch.downloadprovider" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="downloadtag"
                    android:host="com.android.providers.downloads.ui"/>
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.SEARCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="mi.quicksearch.intent.action.RETRIEVE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="miui.intent.action.VIEW_DOWNLOADS_LIST" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:name=".ui.ui.DirectMailActivity"
            android:theme="@style/ThemeDayNightNoTitle"
            android:exported="true">
            <intent-filter>
                <data
                    android:scheme="downloads"
                    android:host="directmail"
                    />
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <activity android:name=".ui.ui.UiHandlerActivity"
            android:theme="@style/ThemeDayNightNoTitle"
            android:exported="true">
            <intent-filter>
                <data
                    android:scheme="downloads"
                    android:host="uihandle"
                    />
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.android.provider.downloads.androidx-startup"
            tools:replace="android:authorities"
            android:exported="false"/>
        <!-- kcg服务注册 -->
       <!-- <service
            android:name="com.ksyun.pp.service.KcgService"
            android:process=":kcg" />-->

        <!--标志当前dp支持那些功能,如果要修改,注意ui的ManifestUtils-->
        <meta-data android:name="support_vip_accelerate" android:value="true" />
        <meta-data android:name="support_notification_recommend" android:value="true" />
        <meta-data android:name="support_ctrol_recommend_push" android:value="true" />
        <meta-data android:name="support_install_guide" android:value="true" />
        <meta-data android:name="android.max_aspect" android:value="2.2" />
        <meta-data android:name="support_ctrol_data_limit" android:value="true" />
        <meta-data android:name="support_custom_notification_icon" android:value="true" />
        <meta-data android:name="support_ipv6" android:value="true" />
        <meta-data android:name="support_query_all" android:value="true" />
        <meta-data android:name="support_call_limit_speed2" android:value="true" />
        <meta-data android:name="support_only_delete_download_info" android:value="true" />
        <meta-data android:name="support_m3u82" android:value="true" />
        <meta-data android:name="support_selfupdate" android:value="true" />
    </application>
</manifest>
